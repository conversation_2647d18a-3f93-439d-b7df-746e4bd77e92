﻿using Brands.DAL;
using BrandsWebApp.Authentication;
using BrandsWebApp.Models;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Auth;
using Humanizer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Authorize(Policy = nameof(Permission.ManageContactsAndUsers))]
    [ApiController]
    [Route("api/Users")]
    public class UsersController : ControllerBase
    {
        private readonly EPDATAContext ePDATAContext;
        private readonly ILogger<UsersController> logger;
        private readonly RelatedContactsService relatedContactsService;
        private readonly PermissionDataService permissionDataService;

        public UsersController(EPDATAContext ePDATAContext, ILogger<UsersController> logger, RelatedContactsService relatedContactsService, PermissionDataService permissionDataService)
        {
            this.ePDATAContext = ePDATAContext;
            this.logger = logger;
            this.relatedContactsService = relatedContactsService;
            this.permissionDataService = permissionDataService;
        }

        [HttpGet("UsersAndRoles")]
        public async Task<IActionResult> GetUsersAndContactsAsync()
        {
            logger.LogDebug("Entering UsersAndRoles");
            var users = await GetUsersAndRoles();
            return Ok(users);
        }

        [HttpGet("SearchEmployee/{searchString}")]
        public async Task<IActionResult> SearchEmployee(string searchString)
        {
            var employees = ePDATAContext.Employees.Where(e => e.Conum == HttpContext.User.GetConum());
            if (int.TryParse(searchString, out int empnum))
            {
                employees = employees.Where(e => e.Empnum == empnum);
            }
            else
            {
                if (searchString?.Length < 3)
                {
                    return BadRequest("A minimum of 3 characters are required to search for an employee.");
                }
                var splitted = searchString.Split(' ', ',');
                if (splitted.Count() == 1)
                {
                    employees = employees.Where(e => e.FirstName.StartsWith(searchString) || e.LastName.StartsWith(searchString) || e.UserEmail.StartsWith(searchString) || e.ContactHomeEmail.StartsWith(searchString));
                }
                else
                {
                    employees = employees.Where(e => (e.FirstName.StartsWith(splitted[0]) && e.LastName.StartsWith(splitted[1])) || (e.FirstName.StartsWith(splitted[1]) && e.LastName.StartsWith(splitted[0])));
                }
            }

            var results = await (from e in employees
                                 join lco in ePDATAContext.CoContacts_Ts on new { EmpConum = e.Conum, e.Empnum } equals new { lco.EmpConum, lco.Empnum } into gco
                                 from co in gco.DefaultIfEmpty()
                                 where co == null || co.Conum == HttpContext.User.GetConum()
                                 select new
                                 {
                                     e.Empnum,
                                     e.FirstName,
                                     e.MiddleName,
                                     e.LastName,
                                     e.UserEmail,
                                     e.ContactHomeEmail,
                                     e.TermDate,
                                     e.EntryId,
                                     AlreadyAdded = co != null
                                 }).ToListAsync();
            return Ok(results);
        }


        [HttpPost("Save")]
        public async Task<IActionResult> Save(UserAndRolesSave userAndContact, [FromServices] ManageUserService createUserService)
        {
            logger.LogDebug("Entering Save");

            var saveEmployeeResults = await relatedContactsService.SaveEmployeeWithCoContact(userAndContact);

            if (!saveEmployeeResults.isSuccess)
            {
                return BadRequest(saveEmployeeResults.errorMessage);
            }
            var employeeResults = saveEmployeeResults.employeeResults;

            if (userAndContact.RemoveOldPaydeckEmail)
            {
                logger.LogDebug("RemoveOldPaydeckEmail");
                (bool isValid, string message) results = await UpdateUserCompanyLink(userAndContact, createUserService, true, false);
                if (!results.isValid)
                {
                    return BadRequest(results.message);
                }
            }

            if (userAndContact.InviteToPaydeck)
            {
                var email = userAndContact.PaydeckEmail.IsNullOrWhiteSpace() ? userAndContact.Email : userAndContact.PaydeckEmail;
                if (email.IsNullOrWhiteSpace())
                {
                    return BadRequest("Email is blank.");
                }
                var createUserRequest = new CreateUserRequest
                {
                    Conum = HttpContext.User.GetConum(),
                    EmpConum = employeeResults.Conum,
                    Empnum = employeeResults.Empnum,
                    EmailAddress = email,
                    CreatedByUserId = HttpContext.User.GetEmail(),
                    CreatedByUserType = "PD",
                    ResendInviteEmailOnly = false,
                    UserRole = userAndContact.UserRole,
                    Permissions = userAndContact.Permissions?.Where(p => p.Value).Select(p => p.Key).ToList(),
                    UpdateUserPermissionsOnly = userAndContact.PaydeckUserId.HasValue && !userAndContact.RemoveOldPaydeckEmail,
                    SwipeClockLoginName = userAndContact.SwipeClockLoginName,
                    UpdateSwipeClockLoginName = true
                };
                var user = await createUserService.FindByEmailAsync(email);
                var validCreateUserResults = await createUserService.ValidateCreateUser(createUserRequest, employeeResults, user);
                //TODO: fix validation messages 
                if (!validCreateUserResults.isValid)
                {
                    return BadRequest(validCreateUserResults.message);
                }
                var createUserResults = await createUserService.CreateOrLinkPaydeckUser(user, createUserRequest, employeeResults);
            }
            else if (userAndContact.PaydeckUserId.HasValue)
            {
                //disable paydeck user
                (bool isValid, string message) results = await UpdateUserCompanyLink(userAndContact, createUserService, false, true);
                if (!results.isValid)
                {
                    return BadRequest(results.message);
                }
            }
            var updatedUser = (await GetUsersAndRoles(employeeResults.Conum, employeeResults.Empnum)).Single();
            return Ok(updatedUser);
        }

        private async Task<(bool isValid, string message)> UpdateUserCompanyLink(UserAndRolesSave userAndContact, ManageUserService createUserService, bool deleteLink, bool inactivateLink)
        {
            var request = new ManageUserService.UpdateUserCompanyLinkRequest
            {
                UserId = userAndContact.PaydeckUserId?.ToString(),
                ErrorOnUserNull = false,
                DeleteLink = deleteLink,
                InactivateLink = inactivateLink,
                ActivateLink = false,
                Conum = User.GetConum(),
                CreatedByUserId = HttpContext.User.GetEmail(),
                CreatedByUserType = "PD",
            };
            var results = await createUserService.UpdateUserCompanyLink(request);
            return results;
        }

        [HttpPost("DeleteUser")]
        public async Task<IActionResult> DeleteUser(int entryId, [FromServices] ManageUserService manageUserService)
        {
            try
            {
                logger.LogDebug("Entering DeleteUser. EntryId: {EntryId}", entryId);
                var employee = await ePDATAContext.Employees.SingleAsync(e => e.EntryId == entryId);
                var coContact = await ePDATAContext.CoContacts_Ts.SingleAsync(c => c.Conum == HttpContext.User.GetConum() && c.EmpConum == employee.Conum && c.Empnum == employee.Empnum);
                //save the coContact in a history table
                ePDATAContext.CoContacts_Ts.Remove(coContact);

                var userEmployee = await ePDATAContext.BrandsUserEmployees
                    .Include(e => e.BrandsUser)
                    .SingleOrDefaultAsync(ue => ue.Conum == HttpContext.User.GetConum() && ue.EmpConum == employee.Conum && ue.Empnum == employee.Empnum);
                if (userEmployee != null)
                {
                    logger.LogDebug("Deleting user from company. UserId: {UserId} Email: {Email}", userEmployee.UserId, userEmployee.BrandsUser.Email);

                    var request = new ManageUserService.UpdateUserCompanyLinkRequest
                    {
                        UserId = userEmployee.UserId.ToString(),
                        DeleteLink = true,
                        Conum = userEmployee.Conum,
                        CreatedByUserId = User.GetEmail(),
                        CreatedByUserType = "PD"
                    };
                    var results = await manageUserService.UpdateUserCompanyLink(request);
                    if (!results.isValid)
                    {
                        return BadRequest(results.message);
                    }
                    //identityDataContext.BrandsUserEmployees.Remove(userEmployee);
                    //await identityDataContext.SaveChangesAsync();
                }

                await ePDATAContext.SaveChangesAsync();
                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error deleting user. EntryId: {EntryId}", entryId);
                return BadRequest();
            }
        }

        [HttpPost("ResendInvite")]
        public async Task<IActionResult> ResendInvite(int paydeckUserId, [FromServices] ManageUserService createUserService, [FromServices] BrandsAuthService brandsAuthService)
        {
            logger.LogDebug("Entering ResendInvite. {paydeckUserId}", paydeckUserId);

            //check if allowed 
            var user = await ePDATAContext.Users.Include(e => e.BrandsUserEmployees).SingleOrDefaultAsync(u => u.Id == paydeckUserId);
            if (user == null)
            {
                logger.LogWarning("User not found");
                return BadRequest();
            }
            var userEmployee = user.BrandsUserEmployees.SingleOrDefault(e => e.Conum == User.GetConum());
            if (userEmployee == null)
            {
                logger.LogWarning("No record found in BrandsUserEmployees");
                return BadRequest();
            }
            if (!userEmployee.IsActive)
            {
                logger.LogWarning("BrandsUserEmployees record is not active.");
                return BadRequest();
            }
            var employee = await ePDATAContext.Employees.SingleAsync(e => e.Conum == userEmployee.EmpConum && e.Empnum == userEmployee.Empnum);
            //resend 
            var createUserRequest = new CreateUserRequest
            {
                Conum = User.GetConum(),
                EmpConum = employee.Conum,
                Empnum = employee.Empnum,
                EmailAddress = user.Email,
                CreatedByUserId = HttpContext.User.GetEmail(),
                CreatedByUserType = "PD",
                ResendInviteEmailOnly = true
            };
            var validateResults = await createUserService.ValidateCreateUser(createUserRequest, employee, user);
            if (!validateResults.isValid)
            {
                return BadRequest(validateResults.message);
            }

            logger.LogDebug("Resending email.");
            await brandsAuthService.SendInviteEmail(user.Email, createUserRequest, employee, user);
            return Ok("Invite Sent Successfully.");
        }

        private async Task<IEnumerable<UserAndRoles>> GetUsersAndRoles(decimal? empConum = null, decimal? empnum = null)
        {
            var contacts = await (from ct in ePDATAContext.CoContacts_Ts
                                  join e in ePDATAContext.Employees on new { Conum = ct.EmpConum, ct.Empnum } equals new { e.Conum, e.Empnum }
                                  join cteL in ePDATAContext.CoContactsExtensions on new { ct.Conum, ct.EmpConum, ct.Empnum } equals new { cteL.Conum, cteL.EmpConum, cteL.Empnum } into cteG
                                  from cte in cteG.DefaultIfEmpty()
                                  where ct.Conum == HttpContext.User.GetConum() && (!empConum.HasValue || ct.EmpConum == empConum) && (!empnum.HasValue || ct.Empnum == empnum)
                                  select new
                                  {
                                      ct.EmpConum,
                                      e.Empnum,
                                      e.FirstName,
                                      e.MiddleName,
                                      e.LastName,
                                      e.UserEmail,
                                      e.ContactCellphone,
                                      e.EntryId,
                                      cte.Role
                                  }).ToListAsync();

            var users = await (from ue in ePDATAContext.BrandsUserEmployees
                               join u in ePDATAContext.Users on ue.UserId equals u.Id
                               where ue.Conum == HttpContext.User.GetConum() && (!empConum.HasValue || ue.EmpConum == empConum) && (!empnum.HasValue || ue.Empnum == empnum)
                               select new
                               {
                                   ue.EmpConum,
                                   ue.Empnum,
                                   u.Email,
                                   ue.UserId,
                                   u.EmailConfirmed,
                                   u.PasswordHash,
                                   ue.LastLogin,
                                   PaydeckUserId = u.Id,
                                   ue.UserRole,
                                   ue.ClaimsDisplay,
                                   ue.IsActive,
                                   ue.SwipeClockLoginName
                               }).ToListAsync();

            var result = (from c in contacts
                          join uLJ in users on new { c.EmpConum, c.Empnum } equals new { uLJ.EmpConum, uLJ.Empnum } into uG
                          from u in uG.DefaultIfEmpty()
                          select new UserAndRoles
                          {
                              EmpConum = c.EmpConum,
                              Empnum = c.Empnum,
                              FirstName = c.FirstName,
                              MiddleName = c.MiddleName,
                              LastName = c.LastName,
                              FullName = $"{c.FirstName} {c.MiddleName} {c.LastName}".Replace("  ", " "),
                              Email = c.UserEmail,
                              PaydeckEmail = u?.Email,
                              LoginStatus = u == null || !u.IsActive ? string.Empty : u.LastLogin.HasValue ? $"Last login {u.LastLogin.Value.Humanize(false)}" : "Invited",
                              LastLogin = u?.LastLogin,
                              IsPaydeckUser = u != null && u.IsActive,
                              PaydeckUserId = u?.PaydeckUserId,
                              EmailConfirmed = u?.EmailConfirmed,
                              UserRole = u != null && u.IsActive && u.UserRole.IsNotNullOrWhiteSpace() ? u.UserRole : c.Role,
                              PermissionsDisplay = u?.ClaimsDisplay,
                              ContactCellphone = c.ContactCellphone,
                              EntryId = c.EntryId,
                              CurrentCoEmpnum = c.EmpConum == User.GetConum() ? (int?)c.Empnum : null,
                              ItsMe = u?.PaydeckUserId == HttpContext.User.GetPaydeckUserId(),
                              SwipeClockLoginName = u?.SwipeClockLoginName
                          })
                          .OrderBy(c => c.FullName)
                          .ToList();

            var allUserClaims = await ePDATAContext.BrandsAuthUserClaims
                .Where(c => users.Select(u => u.UserId).Contains(c.UserId) && c.Conum == HttpContext.User.GetConum() && c.ClaimType == Permission.PermissionClaimName)
                .ToListAsync();
            var companyClaims = await permissionDataService.GetAllClaims(HttpContext.User.GetConum(), true);
            foreach (var user in result)
            {
                var checkedUserClaims = allUserClaims.Where(c => c.UserId == user.PaydeckUserId).ToList();
                var userClaims = await permissionDataService.GetUserClaims(checkedUserClaims, (int)HttpContext.User.GetConum(), false, companyClaims);
                user.Permissions = userClaims
                    .Select(p => p.Value)
                    .ToList();
                user.CheckedPermissions = user.Permissions.Where(p => p.IsDefaultChecked).Select(p => p.ClaimName).ToList();
            }
            return result;
        }

    }
}
