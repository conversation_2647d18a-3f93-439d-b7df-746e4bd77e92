﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Filters;
using BrandsWebApp.Models.Payroll.ManualCheck;
using BrandsWebApp.Services.Payroll;
using BrandsWebApp.Services.Validation.ManualCheckValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Payrolls
{
    [Route("api/ManualChecks")]
    [ApiController]
    [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
    public class ManualChecksController : ControllerBase
    {
        private readonly ManualChecksService manualChecksService;

        public ManualChecksController(ManualChecksService manualChecksService)
        {
            this.manualChecksService = manualChecksService;
        }

        /// <summary>
        /// Gets manual checks
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetManualChecks()
        {
            var manualChecks = await manualChecksService.GetManualChecksAsync(User.GetConum());
            return Ok(manualChecks);
        }

        /// <summary>
        /// Gets manual check details by key
        /// </summary>
        [HttpGet("{checkKey}")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<ManualCheckDetails>> GetManualCheckDetails([FromRoute] string checkKey)
        {
            var manualCheckDetails = await manualChecksService.GetManualCheckDetailsAsync(User.GetConum(), checkKey, User.GetPaydeckUserId());
            return Ok(manualCheckDetails);
        }

        /// <summary>
        /// Gets suggested manual check number
        /// </summary>
        [HttpGet("{checkKey}/Number")]
        public async Task<IActionResult> GetSuggestedManualCheckNumber([FromRoute] string checkKey)
        {
            var manualCheckNumber = await manualChecksService.GetNextManualCheckNumberAsync(User.GetConum(), checkKey);
            return Ok(new { CheckNumber = manualCheckNumber });
        }

        /// <summary>
        /// Adds manual check
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> AddManualCheck([FromBody] AddManualCheck check)
        {
            var result = await manualChecksService.AddManualCheckAsync(User.GetConum(), check);
            return Ok(result);
        }

        /// <summary>
        /// Processes manual check
        /// </summary>
        [HttpPost("Processed/{checkKey}")]
        [ServiceFilter(typeof(UpdatePayrollCheckAsyncFilter))]
        public async Task<IActionResult> ProcessManualCheck([FromRoute] string checkKey, [FromBody] ProcessManualCheck processManualCheck, [FromServices] ProcessManualCheckValidation validations, [FromQuery] string checkUpdateGuid)
        {
            processManualCheck.CheckKey = checkKey;

            var validationResult = await validations.ValidateAsync(processManualCheck);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            var result = await manualChecksService.ProcessManualCheckAsync(User.GetConum(), processManualCheck);
            return Ok(result);
        }

        /// <summary>
        /// Deletes manual check
        /// </summary>
        [HttpDelete("{checkKey}")]
        public async Task<IActionResult> DeleteManualCheck([FromRoute] string checkKey)
        {
            await manualChecksService.DeleteManualCheckAsync(User.GetConum(), checkKey);
            return NoContent();
        }

        /// <summary>
        /// Updates manual check
        /// </summary>
        [HttpPut("{checkKey}/CheckDate")]
        [ServiceFilter(typeof(UpdatePayrollCheckAsyncFilter))]
        public async Task<IActionResult> UpdateManualCheckDate([FromRoute] string checkKey, [FromBody] UpdateManualCheckDate check, [FromQuery] string checkUpdateGuid)
        {
            check.CheckKey = checkKey;
            var result = await manualChecksService.UpdateManualCheckDateAsync(User.GetConum(), check);
            return Ok(result);
        }
    }
}
