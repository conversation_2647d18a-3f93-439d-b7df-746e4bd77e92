﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [ApiController]
    [Route("api/Permission")]
    [Authorize(Policy = nameof(Permission.ManageContactsAndUsers))]
    public class PermissionController : ControllerBase
    {
        private readonly PermissionDataService permissionDataService;

        public PermissionController(PermissionDataService permissionDataService)
        {
            this.permissionDataService = permissionDataService;
        }

        [HttpGet("roles")]
        public async Task<IActionResult> GetRolesAsync()
        {
            var roles = await permissionDataService.GetRolesAsync(HttpContext.User.GetConum());
            return Ok(roles);
        }

        [HttpGet("permissions")]
        public async Task<IActionResult> GetPermissions(int? paydeckUserId)
        {
            var permissions = await permissionDataService.GetAllClaims(HttpContext.User.GetConum(), false, paydeckUserId);
            return Ok(permissions);
        }
    }
}
