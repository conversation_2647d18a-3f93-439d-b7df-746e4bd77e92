﻿using Brands.DAL;
using Brands.DataModels;
using Brands.DataModels.Interfaces;
using BrandsWebApp.Authentication;
using BrandsWebApp.Exceptions;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.Employee.DirectDeposit;
using BrandsWebApp.Services;
using BrandsWebApp.Services.EmployeeDeck;
using BrandsWebApp.Services.Validation.EmployeeValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Employee
{
    [ApiController]
    [Route("api/Employees")]
    [Authorize(Policy = AuthPolicies.ManageEmployeesOrEssDirectDeposit)]
    public class DirectDepositController : ControllerBase
    {
        private readonly EPDATAContext ePDATAContext;
        private readonly ILogger<DirectDepositController> logger;
        private readonly EmployeeService employeeService;
        private readonly EmployeeDirectDepositService employeeDirectDepositService;
        private readonly EssOnboardingService essOnboardingService;

        public DirectDepositController(
            EPDATAContext ePDATAContext,
            ILogger<DirectDepositController> logger,
            EmployeeService employeeService,
            EmployeeDirectDepositService employeeDirectDepositService,
            EssOnboardingService essOnboardingService)
        {
            this.ePDATAContext = ePDATAContext;
            this.logger = logger;
            this.employeeService = employeeService;
            this.employeeDirectDepositService = employeeDirectDepositService;
            this.essOnboardingService = essOnboardingService;
        }

        [HttpGet("{empnum:int}/DirectDeposit")]
        public async Task<IActionResult> GetDirectDeposit([FromRoute] int empnum)
        {
            Models.Employee.Employee employee = GetEmployee(empnum);
            var directDeposit = await employeeDirectDepositService.GetEmployeeDirectDepositAsync(employee.CoNum, employee.EmpNum, null, User.GetEssProfileId() != null, User.GetEssOnboardingProfileId() != null);
            return Ok(directDeposit);
        }

        [HttpGet("DirectDeposit/Options")]
        public async Task<IActionResult> DirectDepositDropdowns()
        {
            logger.LogDebug("Entering DirectDepositDropdowns");
            try
            {
                return Ok(new
                {
                    AccountTypes = new[] { "None", "Checking", "Savings", "Checking - Business", "Savings - Business" },
                    SplitMethods = new[] { "", "Flat Split", "Percent Split" },
                    AllowSkipPreNote = await AllowSkipPreNote() != null
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in DirectDepositDropdowns");
                throw;
            }
        }

        [HttpGet("Bank/{routing}")]
        public async Task<IActionResult> GetBankRouting(string routing)
        {
            logger.LogDebug("Entering GetBankRouting");
            try
            {
                var bank = await ePDATAContext.BankInfos.SingleOrDefaultAsync(b => b.ROUTING == routing);
                if (bank == null)
                {
                    return NotFound();
                }
                return Ok(new
                {
                    Name = bank.NAME,
                    RoutingNumber = bank.ROUTING
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetBankRouting");
                throw;
            }
        }

        [HttpPost("{empnum:int}/UpdateDirectDepositAmount")]
        public async Task<IActionResult> UpdateDirectDeposit([FromRoute] int empnum, EmployeeDirectDeposit employeeDirectDeposit, [FromServices] EmployeeDirectDepositValidation validations)
        {
            logger.LogDebug("Entering UpdateDirectDepositAmount. {@employeeDirectDeposit}", employeeDirectDeposit);
            employeeDirectDeposit.FixAmounts();

            var validationResult = await validations.ValidateAsync(employeeDirectDeposit).ConfigureAwait(false);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            try
            {
                Models.Employee.Employee employeeInfo = GetEmployee(empnum);
                var isOnboarding = User.IsEssOnboarding();
                IEmployee employee = isOnboarding ?
                    await essOnboardingService.GetActiveOnboardingEmployeeAsync(employeeInfo.CoNum, employeeInfo.EmpNum, true) :
                    await employeeService.GetEmployeeAsync(employeeInfo.CoNum, employeeInfo.EmpNum);

                var employeeOptions = await ePDATAContext.EmployeeOptions.SingleOrDefaultAsync(eo => eo.CoNum == employee.Conum && eo.EmpNum == employee.Empnum);
                if (employeeOptions == null)
                {
                    employeeOptions = new EmployeeOptions { CoNum = employee.Conum, EmpNum = employee.Empnum };
                    ePDATAContext.EmployeeOptions.Add(employeeOptions);
                }

                employeeDirectDeposit.Copy(employee, employeeOptions);
                
                await ePDATAContext.SaveChangesAsync();

                bool isEssEmployee = User.GetEssProfileId() != null;
                var directDeposit = await employeeDirectDepositService.GetEmployeeDirectDepositAsync(employeeInfo.CoNum, employeeInfo.EmpNum, employee, isEssEmployee, isOnboarding);
                return Ok(directDeposit);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdateDirectDepositAmount");
                throw;
            }
        }

        [HttpPost("{empnum:int}/UpdateDirectDeposit")]
        public async Task<IActionResult> UpdateDirectDeposit([FromRoute] int empnum, EmployeeDirectDeposit.DirectDepositAccount directDeposit, [FromServices] DirectDepositAccountValidation validations)
        {
            logger.LogDebug("Entering UpdateDirectDeposit. {@DirectDeposit}", directDeposit);
            if (directDeposit.SplitMethod == "Percent Split" && !directDeposit.SplitAmount.HasValue)
            {
                directDeposit.SplitAmount = 1;
            }

            var validationResult = await validations.ValidateAsync(directDeposit);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            try
            {
                Models.Employee.Employee employeeInfo = GetEmployee(empnum);
                var isOnboarding = User.IsEssOnboarding();
                IEmployee employee = isOnboarding ?
                    await essOnboardingService.GetActiveOnboardingEmployeeAsync(employeeInfo.CoNum, employeeInfo.EmpNum, true) :
                    await employeeService.GetEmployeeAsync(employeeInfo.CoNum, employeeInfo.EmpNum);
                CoUdfs udf = await AllowSkipPreNote(employeeInfo.CoNum);

                var employeeOptions = await ePDATAContext.EmployeeOptions.SingleOrDefaultAsync(eo => eo.CoNum == employee.Conum && eo.EmpNum == employee.Empnum);
                if (employeeOptions == null)
                {
                    employeeOptions = new EmployeeOptions { CoNum = employee.Conum, EmpNum = employee.Empnum };
                    ePDATAContext.EmployeeOptions.Add(employeeOptions);
                }

                directDeposit.Copy(employee, udf == null, employeeOptions);

                await ePDATAContext.SaveChangesAsync();

                bool isEssEmployee = User.GetEssProfileId() != null;
                var ddResult = await employeeDirectDepositService.GetEmployeeDirectDepositAsync(employeeInfo.CoNum, employeeInfo.EmpNum, employee, isEssEmployee, isOnboarding);
                return Ok(ddResult);
            }
            catch (BadRequestException ex)
            {
                logger.LogWarning(ex, ex.Message);
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdateDirectDeposit");
                throw;
            }
        }

        /// <summary>
        /// Activates direct deposit accounts (skips pre-note)
        /// </summary>
        [Authorize(Policy = nameof(Permission.ManageEmployees))]
        [HttpPost("{empnum}/DirectDeposit/Activation")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<IActionResult> SkipPreNote([FromRoute] int empnum)
        {
            Models.Employee.Employee employee = GetEmployee(empnum);
            await employeeDirectDepositService.ActivateDirectDepositAccountsAsync(employee.CoNum, employee.EmpNum);
            return NoContent();
        }

        [Authorize(Policy = nameof(Permission.ManageEmployees))]
        [HttpGet("{empnum:int}/DirectDepositAuthForm")]
        public async Task<IActionResult> GetDirectDepositAuthorizationForm([FromRoute] int empnum)
        {
            Models.Employee.Employee employeeInfo = GetEmployee(empnum);
            Employee_T employee = await employeeService.GetEmployeeAsync(employeeInfo.CoNum, employeeInfo.EmpNum);
            byte[] fileContent = await employeeService.GetDirectDepositEmployeeAuthFormAsync(employee.Empnum, employee.Conum);
            string fileName = $"Direct Deposit Authorization Form ({employeeInfo.EmpNum}).pdf";
            this.Response.Headers.Add("X-Filename", fileName);
            return File(fileContent, "application/pdf", fileName);
        }

        private async Task<CoUdfs> AllowSkipPreNote(decimal? coNum = null)
        {
            if (coNum == null)
            {
                coNum = User.GetEssEmpConum() ?? User.GetConum();
            }

            return await ePDATAContext.CoUdfs.FirstOrDefaultAsync(c => c.CONUM == coNum && c.UDF_DESCR == "Allow Employee ACH Management" && c.UDF_STRING == "Active");
        }

        private Models.Employee.Employee GetEmployee(int empNum)
        {
            return new Models.Employee.Employee
            {
                CoNum = User.GetEssEmpConum() ?? User.GetConum(),
                EmpNum = User.GetEssEmpNum() ?? empNum
            };
        }
    }
}
