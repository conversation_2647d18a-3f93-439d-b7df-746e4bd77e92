﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Filters;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.Payroll.Check;
using BrandsWebApp.Models.PowerGrid;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Payroll;
using BrandsWebApp.Services.Validation.PowerGridValidation;
using FluentValidation.Results;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RpcMessagingModels;
using RpcMessagingModels.ProcessPowerGrid;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GetDistributedPowerGridResponse = BrandsWebApp.Models.PowerGrid.GetDistributedPowerGridResponse;
using GetNewPowerGridPayeeResponse = BrandsWebApp.Models.PowerGrid.GetNewPowerGridPayeeResponse;

namespace BrandsWebApp.Api.Payrolls
{
    [Route("api/PowerGrids")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "v2")]
    public class PowerGridsController : ControllerBase
    {
        private readonly PowerGridsService powerGridsService;
        private readonly ExecupayApiService execupayApiService;

        public PowerGridsController(PowerGridsService powerGridsService, ExecupayApiService execupayApiService)
        {
            this.powerGridsService = powerGridsService;
            this.execupayApiService = execupayApiService;
        }

        /// <summary>
        /// Gets payroll power grids
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpGet("{payrollNumber}")]
        public async Task<ActionResult<IEnumerable<PowerGridAndImport>>> GetPowerGrids([FromRoute] decimal payrollNumber)
        {
            var powerGridsAndImports = await powerGridsService.GetPowerGridsAndImportsAsync(User.GetConum(), payrollNumber);
            return Ok(powerGridsAndImports);
        }

        /// <summary>
        /// Completes payroll power grid
        /// </summary>
        [Authorize(Policy = AuthPolicies.PaydeckPayrollOrPowerImports)]
        [HttpPost("Completed/{powerGridId}")]
        public async Task<ActionResult<PowerGridAndImport>> CompletePowerGrid([FromRoute] Guid powerGridId)
        {
            var powerGrid = await powerGridsService.CompletePowerGridAsync(User.GetConum(), powerGridId);
            return Ok(powerGrid);
        }

        /// <summary>
        /// Processes power grid
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpPost("{payrollNumber}/Processed/{powerGridId}")]
        public async Task<ActionResult<IEnumerable<Check>>> ProcessPowerGrid([FromRoute] decimal payrollNumber, [FromRoute] Guid powerGridId)
        {
            var updatedChecks = await powerGridsService.ProcessPowerGridAsync(User.GetConum(), payrollNumber, powerGridId);
            return Ok(updatedChecks);
        }

        /// <summary>
        /// Unprocesses power grid
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpDelete("{payrollNumber}/Processed/{powerGridId}")]
        public async Task<ActionResult<IEnumerable<Check>>> UnprocessPowerGrid([FromRoute] decimal payrollNumber, [FromRoute] Guid powerGridId)
        {
            var updatedChecks = await powerGridsService.UnprocessPowerGridAsync(User.GetConum(), payrollNumber, powerGridId);
            return Ok(updatedChecks);
        }

        /// <summary>
        /// Processes time and labor power grid
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpPost("{payrollNumber}/TimeAndLabor")]
        public async Task<ActionResult<IEnumerable<Check>>> ProcessTlmPowerGrid([FromRoute] decimal payrollNumber, [FromBody] TimePeriod timePeriod)
        {
            var updatedChecks = await powerGridsService.ProcessTlmPowerGridAsync(User.GetConum(), payrollNumber, timePeriod);
            return Ok(updatedChecks);
        }

        /// <summary>
        /// Gets power grid schemas
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [HttpGet("Schemas")]
        public async Task<ActionResult<IEnumerable<PowerGridSchema>>> GetPowerGridSchemas()
        {
            var schemas = await powerGridsService.GetSchemasAsync(User.GetConum());
            return Ok(schemas);
        }

        /// <summary>
        /// Creates new power grid
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [HttpPost]
        public async Task<ActionResult<CreatePowerGridResponse>> CreatePowerGrid([FromBody] CreatePowerGrid createPowerGrid, [FromServices] CreatePowerGridValidation validation)
        {
            ValidationResult validationResult = await validation.ValidateAsync(createPowerGrid);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            var powerGridId = await powerGridsService.CreatePowerGridAsync(User.GetConum(), createPowerGrid);
            return Ok(new CreatePowerGridResponse { PowerGridId = powerGridId });
        }

        /// <summary>
        /// Gets power grids
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [HttpGet]
        public async Task<ActionResult<IEnumerable<PowerGridAndImport>>> GetPowerGrids()
        {
            var powerGridsAndImports = await powerGridsService.GetPowerGridsAndImportsAsync(User.GetConum(), null);
            return Ok(powerGridsAndImports);
        }

        /// <summary>
        /// Gets power grid data
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [HttpGet("{id}/Data")]
        public async Task<ActionResult<PowerGridData>> GetPowerGridData([FromRoute] Guid id)
        {
            var data = await powerGridsService.GetPowerGridAsync(User.GetConum(), id);
            return Ok(data);
        }

        /// <summary>
        /// Updates power grid
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdatePowerGrid([FromRoute] Guid id, [FromBody] UpdatePowerGrid powerGrid)
        {
            powerGrid.Id = id;
            await powerGridsService.UpdatePowerGridAsync(User.GetConum(), powerGrid);
            return NoContent();
        }

        /// <summary>
        /// Deletes power grid
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeletePowerGrid([FromRoute] Guid id)
        {
            await powerGridsService.DeletePowerGridAsync(User.GetConum(), id);
            return NoContent();
        }

        /// <summary>
        /// Gets distributed power grid rows
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [HttpGet("{id}/Distribution")]
        public async Task<ActionResult<GetDistributedPowerGridResponse>> GetDistributedPowerGridRows([FromRoute] Guid id, [FromQuery] GetDistributedPowerGridRequest distributePowerGrid)
        {
            distributePowerGrid.PowerGridId = id;
            var response = await powerGridsService.GetDistributedPowerGridRowsAsync(User.GetConum(), distributePowerGrid);
            return Ok(response);
        }

        /// <summary>
        /// Gets new power grid payee rows
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [HttpGet("{id}/Payees/New")]
        public async Task<ActionResult<GetNewPowerGridPayeeResponse>> GetNewPowerGridPayee([FromRoute] Guid id, [FromQuery] GetNewPowerGridPayeeRequest newPowerGridPayee)
        {
            newPowerGridPayee.PowerGridId = id;
            var response = await powerGridsService.GetNewPowerGridPayeeRowsAsync(User.GetConum(), newPowerGridPayee);
            return Ok(response);
        }
    }
}
