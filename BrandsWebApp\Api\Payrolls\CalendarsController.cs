﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Payroll.Calendar;
using BrandsWebApp.Services.Payroll;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Payrolls
{
    [Route("api/Payrolls/Calendars")]
    [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
    [ApiController]
    public class CalendarsController : ControllerBase
    {
        private readonly CalendarsService calendarsService;
        private readonly ExecupayApiService execupayApiService;

        public CalendarsController(ExecupayApiService execupayApiService, CalendarsService calendarsService)
        {
            this.execupayApiService = execupayApiService;
            this.calendarsService = calendarsService;
        }

        /// <summary>
        /// Gets calendars
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetCalendars([FromQuery] CalendarsQueryFilter calendarsQueryFilter)
        {
            var calendars = await calendarsService.GetCalendarsAsync(User.GetConum(), calendarsQueryFilter.FreeOnly);
            return Ok(calendars);
        }

        /// <summary>
        /// Gets default calendars
        /// </summary>
        [HttpGet("Default")]
        public async Task<IActionResult> GetDefaultCalendars()
        {
            var calendars = await execupayApiService.GetDefaultCalendarsAsync(User.GetConum());
            return Ok(calendars);
        }

        /// <summary>
        /// Gets calendar frequencies
        /// </summary>
        [HttpGet("Frequencies")]
        public async Task<IActionResult> GetCalendarFrequencies()
        {
            var frequencies = await calendarsService.GetCalendarFrequenciesAsync(User.GetConum());
            return Ok(frequencies);
        }

        /// <summary>
        /// Gets bank holidays
        /// </summary>
        [HttpGet("BankHolidays")]
        public async Task<IActionResult> GetBankHolidays()
        {
            var holidays = await calendarsService.GetBankHolidaysAsync();
            return Ok(holidays);
        }
    }
}
