﻿using Brands.DAL;
using BrandsWebApp.Authentication;
using BrandsWebApp.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/debug")]
    [ApiController]
    [Authorize]
    public class DebugController : ControllerBase
    {
        private readonly ILogger<DebugController> logger;
        private readonly IConfiguration configuration;
        private readonly SecurityUtils securityUtils;
        private readonly IWebHostEnvironment webHostEnvironment;
        private readonly EPDATAContext dataContext;

        public DebugController(ILogger<DebugController> logger, IConfiguration configuration, SecurityUtils securityUtils, IWebHostEnvironment webHostEnvironment, EPDATAContext dataContext)
        {
            this.logger = logger;
            this.configuration = configuration;
            this.securityUtils = securityUtils;
            this.webHostEnvironment = webHostEnvironment;
            this.dataContext = dataContext;
        }

        [HttpGet]
        public async Task<IActionResult> GetAsync()
        {
            var user = await dataContext.Users.SingleAsync(u => u.Id == User.GetPaydeckUserId());
            if (user.InternalClaims.IsNullOrWhiteSpace() || !user.InternalClaims.Contains("AllowDebugView"))
            {
                logger.LogWarning("User not allowed");
                return Unauthorized();
            }

            var root = (IConfigurationRoot)configuration;
            var debugView = root.GetDebugView();
            return Ok(new
            {
                ConfigurationSection = debugView,
                webHostEnvironment
            });
        }
    }
}
