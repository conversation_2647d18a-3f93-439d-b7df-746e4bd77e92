﻿using Brands.DAL;
using Brands.DataModels;
using BrandsWebApp.Authentication;
using BrandsWebApp.Services.Auth;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Authorize]
    [ApiController]
    [Route("api/IpRestrictions")]
    [Authorize(Policy = nameof(Permission.IpRestrictions))]
    public class IpRestrictionController : ControllerBase
    {
        private readonly ILogger<IpRestrictionController> logger;
        private readonly EPDATAContext ePDATAContext;
        private readonly UserEventService userEventService;

        public IpRestrictionController(ILogger<IpRestrictionController> logger, EPDATAContext ePDATAContext, UserEventService userEventService)
        {
            this.logger = logger;
            this.ePDATAContext = ePDATAContext;
            this.userEventService = userEventService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAsync()
        {
            logger.LogDebug("Entering GetAsync");
            var coOpt = await ePDATAContext.CoOptionPayrolls.SingleOrDefaultAsync(c => c.Conum == HttpContext.User.GetConum());
            return Ok(new
            {
                EnableIpRestriction = coOpt?.PaydeckEnableIpRestriction,
                YourIpAddress = HttpContext.Connection.RemoteIpAddress?.ToString(),
                AllowedIps = coOpt?.PaydeckAllowedIps,
                DisableUntil = coOpt?.PaydeckIpRestDisableUntil,
                ExcludedUsers = await GetPaydeckUsers(true),
                AvailableUsers = await GetPaydeckUsers(false)
            });
        }

        //log on company.

        [HttpPost("Save")]
        public async Task<IActionResult> Save(SaveIpRestrictions saveIpRestrictions)
        {
            logger.LogDebug("Entering Save. {@saveIpRestrictions}", saveIpRestrictions);
            var (isValidIpRange, errorMessage) = await IsValidIpRange(saveIpRestrictions.AllowedIps);
            if (saveIpRestrictions.AllowedIps.IsNotNullOrWhiteSpace() && !isValidIpRange)
            {
                return BadRequest(errorMessage);
            }

            CoOptionPayroll coOpt = await GetCoOptions();
            if (saveIpRestrictions.EnableIpRestriction.HasValue)
                await EnableIpRestriction(saveIpRestrictions.EnableIpRestriction.Value, coOpt);
            await UpdateAllowedIps(saveIpRestrictions.AllowedIps, coOpt);
            await UpdateDisableUntil(saveIpRestrictions.DisableUntil, coOpt);
            await ePDATAContext.SaveChangesAsync();

            return Ok();
        }

        [HttpPost("SaveEnableIpRestriction")]
        public async Task<IActionResult> SaveEnableIpRestriction(bool enable)
        {
            logger.LogDebug("Entering SaveEnableIpRestriction. {enable}", enable);
            CoOptionPayroll coOpt = await GetCoOptions();
            await EnableIpRestriction(enable, coOpt);
            await ePDATAContext.SaveChangesAsync();

            return Ok();
        }

        [HttpPost("SaveDisableUntil")]
        public async Task<IActionResult> SaveDisableUntil(DateTime? disableUntil)
        {
            logger.LogDebug("Entering SaveDisableUntil. {disableUntil}", disableUntil);
            CoOptionPayroll coOpt = await GetCoOptions();
            await UpdateDisableUntil(disableUntil, coOpt);
            await ePDATAContext.SaveChangesAsync();

            return Ok();
        }

        [HttpPost("SaveAllowedIps")]
        public async Task<IActionResult> SaveAllowedIps(string allowedIps)
        {
            logger.LogDebug("Entering SaveAllowedIps. {allowedIps}", allowedIps);

            var (isValidIpRange, errorMessage) = await IsValidIpRange(allowedIps);
            if (allowedIps.IsNotNullOrWhiteSpace() && !isValidIpRange)
            {
                return BadRequest(errorMessage);
            }
            CoOptionPayroll coOpt = await GetCoOptions();
            await UpdateAllowedIps(allowedIps, coOpt);
            await ePDATAContext.SaveChangesAsync();

            return Ok();
        }

        [HttpPost("excludedUsers/add")]
        public async Task<IActionResult> ExcludedUsersAddAsync(int userId)
        {
            logger.LogDebug("Entering ExcludedUsersAdd. {userId}", userId);
            if (await SetUserExludeAsync(userId, true))
            {
                return Ok();
            }
            return BadRequest();
        }

        [HttpPost("excludedUsers/remove")]
        public async Task<IActionResult> ExcludedUsersRemoveAsync(int userId)
        {
            logger.LogDebug("Entering ExcludedUsersRemoveAsync. {userId}", userId);
            if (await SetUserExludeAsync(userId, false))
            {
                return Ok();
            }
            return BadRequest();
        }

        private async Task<bool> SetUserExludeAsync(int userId, bool exclude)
        {
            var userEmployee = await ePDATAContext.BrandsUserEmployees
                .Include(ue => ue.BrandsUser)
                .SingleOrDefaultAsync(ue => ue.Conum == HttpContext.User.GetConum() && ue.UserId == userId);
            if (userEmployee == null)
            {
                logger.LogWarning("User not found. userId: {userId}", userId);
                return false;
            }
            var message = $"Updating IpRestrictionExclude from: [{userEmployee.IpRestrictionExclude}] to: [{exclude}]";
            await userEventService.SaveUserEvent(userEmployee.BrandsUser, "IP_RESTRICTION_EXCLUDE_USER", true, HttpContext.User.GetConum(), message);
            userEmployee.IpRestrictionExclude = exclude;
            await ePDATAContext.SaveChangesAsync();
            return true;
        }

        private async Task <(bool isValid, string errorMessage)> IsValidIpRange(string allowedIps)
        {
            logger.LogDebug("Entering IsValidIpRange. {allowedIps}", allowedIps);
            if (allowedIps.IsNullOrWhiteSpace())
            {
                return (true,null);
            }
            foreach (var ip in allowedIps.Split(";"))
            {
                if (!IPAddressRange.TryParse(ip, out IPAddressRange iPAddress))
                {
                    logger.LogInformation("Invalid IP Address");
                    return (false, "Invalid IP address");
                }
            }

            var userEmployee = await ePDATAContext.BrandsUserEmployees
                .SingleOrDefaultAsync(ue => ue.Conum == HttpContext.User.GetConum() && ue.UserId == Convert.ToInt32(User.GetUserId()));

            if (!userEmployee.IpRestrictionExclude &&
                !allowedIps.Split(";").Any(ip => IPAddressRange.Parse(ip).Contains(HttpContext.Connection.RemoteIpAddress)))
            {
                logger.LogInformation("You can only set IP Restrictions, to an IP that includes you current IP. {allowedIps} {currentIp}", allowedIps, HttpContext.Connection.RemoteIpAddress);
                var errorMessage= $"You can only set IP Restrictions, to an IP that includes you current IP ({HttpContext.Connection.RemoteIpAddress}).";
                return (false, errorMessage);
            }
            return (true, null);
        }

        private async Task UpdateAllowedIps(string allowedIps, CoOptionPayroll coOpt)
        {
            logger.LogDebug("Entering UpdateAllowedIps. {old} {new}", coOpt.PaydeckAllowedIps, allowedIps);
            if (coOpt.PaydeckAllowedIps != allowedIps)
            {
                await userEventService.SaveCurrentUserEvent("IP_RESTRICTION_ALLOWED_IPS", true, HttpContext.User.GetConum(), $"Updating AllowedIps from: [{coOpt.PaydeckAllowedIps}] to: [{allowedIps}]");
                coOpt.PaydeckAllowedIps = allowedIps;
            }
        }

        private async Task UpdateDisableUntil(DateTime? disableUntil, CoOptionPayroll coOpt)
        {
            logger.LogDebug("Entering UpdateDisableUntil. {old} {new}", coOpt.PaydeckIpRestDisableUntil, disableUntil);
            if (coOpt.PaydeckIpRestDisableUntil != disableUntil)
            {
                coOpt.PaydeckIpRestDisableUntil = disableUntil;
                await userEventService.SaveCurrentUserEvent("IP_RESTRICTION_DISABLE_UNTIL", true, HttpContext.User.GetConum(), $"Updating DisableUntil from: [{coOpt.PaydeckIpRestDisableUntil}] to: [{disableUntil}]");
            }
        }

        private async Task EnableIpRestriction(bool enable, CoOptionPayroll coOpt)
        {
            logger.LogDebug("Entering EnableIpRestriction. {old} {new}", coOpt.PaydeckEnableIpRestriction, enable);
            if (coOpt.PaydeckEnableIpRestriction != enable)
            {
                coOpt.PaydeckEnableIpRestriction = enable;
                await userEventService.SaveCurrentUserEvent("IP_RESTRICTION_ENABLED", true, HttpContext.User.GetConum(), $"Updating EnableIpRestriction from: [{coOpt.PaydeckEnableIpRestriction}] to: [{enable}]");
            }
        }

        private async Task<CoOptionPayroll> GetCoOptions()
        {
            var coOpt = await ePDATAContext.CoOptionPayrolls.SingleOrDefaultAsync(c => c.Conum == HttpContext.User.GetConum());
            if (coOpt == null)
            {
                coOpt = new CoOptionPayroll { Conum = HttpContext.User.GetConum() };
                ePDATAContext.CoOptionPayrolls.Add(coOpt);
            }

            return coOpt;
        }

        private async Task<List<ExcludedUser>> GetPaydeckUsers(bool IpRestrictionExcluded)
        {
            var excludedUsers = await (from u in ePDATAContext.Users
                                       join ue in ePDATAContext.BrandsUserEmployees on u.Id equals ue.UserId
                                       where ue.Conum == HttpContext.User.GetConum() && ue.IpRestrictionExclude == IpRestrictionExcluded
                                       select new ExcludedUser
                                       {
                                           Id = u.Id,
                                           Email = u.Email,
                                           EmpConum = ue.EmpConum,
                                           Empnum = ue.Empnum,
                                           IpRestrictionExclude = ue.IpRestrictionExclude
                                       }).ToListAsync();
            foreach (var user in excludedUsers)
            {
                var emp = await ePDATAContext.Employees.SingleAsync(e => e.Conum == user.EmpConum && e.Empnum == user.Empnum);
                user.FirstName = emp.FirstName;
                user.MiddleName = emp.MiddleName;
                user.LastName = emp.LastName;
            }
            return excludedUsers;
        }

        public class ExcludedUser
        {
            public int Id { get; set; }
            public string Email { get; set; }
            public string FirstName { get; set; }
            public string MiddleName { get; set; }
            public string LastName { get; set; }
            public decimal EmpConum { get; set; }
            public decimal Empnum { get; set; }
            public bool IpRestrictionExclude { get; set; }
        }

        public class SaveIpRestrictions
        {
            public bool? EnableIpRestriction { get; set; }
            public string AllowedIps { get; set; }
            public DateTime? DisableUntil { get; set; }
        }
    }
}