﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Exceptions;
using BrandsWebApp.Models.Qbo;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Validation.QboValidation;
using FluentValidation.Results;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Net.Http;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/Qbo")]
    [ApiController]
    [Authorize(Policy = nameof(Permission.Qbo))]
    public class QboController : ControllerBase
    {
        private readonly QboService qboService;
        private readonly ILogger<QboController> logger;

        public QboController(QboService qboService, ILogger<QboController> logger)
        {
            this.qboService = qboService;
            this.logger = logger;
        }

        /// <summary>
        /// Gets QBO connection details for current company
        /// </summary>
        [HttpGet]
        [ProducesResponseType(typeof(CompanyQboDetails), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> GetQboDetails()
        {
            logger.LogDebug("Entering GetQboDetails.");

            try
            {
                var response = await qboService.GetCompanyDetailsAsync();
                return Ok(response);
            }
            catch (BadRequestException ex)
            {
                logger.LogWarning(ex.Message);
                return BadRequest(ex.Message);
            }
            catch (HttpRequestException ex)
            {
                logger.LogError(ex, ex.Message);
                return BadRequest("Unable to get company details.");
            }
        }

        /// <summary>
        /// Gets QBO authentication URL
        /// </summary>
        [HttpGet("AuthUrl")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAuthUrl()
        {
            logger.LogDebug("Entering GetAuthUrl.");

            string authorizeUrl = await qboService.GetAuthUrlAsync();
            return Ok(authorizeUrl);
        }

        /// <summary>
        /// Adds new QBO connection for current company
        /// </summary>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> AddQboConnection([FromBody] QboAuthDetails qboAuthRequest)
        {
            logger.LogDebug("Entering Authenticate.");

            try
            {
                await qboService.AddQboConnectionAsync(qboAuthRequest);
                return NoContent();
            }
            catch (BadRequestException ex)
            {
                logger.LogWarning(ex.Message);
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Removes QBO connection for current company
        /// </summary>
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> RemoveQboConnection()
        {
            logger.LogDebug("Entering RemoveConnection.");

            try
            {
                await qboService.RemoveConnectionAsync();
                return NoContent();
            }
            catch (BadRequestException ex)
            {
                logger.LogWarning(ex.Message);
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Maps QBO accounts for current company
        /// </summary>
        [HttpPost("Accounts")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> MapAccounts(
            [FromBody] QboAccountMappingDetails qboAccountMappingDetails,
            [FromServices] QboAccountMappingDetailsValidation validation)
        {
            logger.LogDebug("Entering MapAccounts.");

            ValidationResult validationResult = await validation.ValidateAsync(qboAccountMappingDetails);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            try
            {
                await qboService.MapQboAccountsAsync(qboAccountMappingDetails);
                return NoContent();
            }
            catch (BadRequestException ex)
            {
                logger.LogWarning(ex.Message);
                return BadRequest(ex.Message);
            }
        }
    }
}
