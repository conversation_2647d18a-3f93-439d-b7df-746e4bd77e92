using Brands.DAL;
using BrandsWebApp.Adapters;
using BrandsWebApp.Authentication;
using BrandsWebApp.Extensions;
using BrandsWebApp.Middleware;
using BrandsWebApp.Models;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Services.Auth;
using JSNLog;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.SpaServices.ReactDevelopmentServer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using System;
using System.IO;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using BrandsWebApp.HealthChecks;
using Brands.DataModels;
using Sentry;

namespace BrandsWebApp
{
    public class Startup
    {
        private readonly IWebHostEnvironment webHostEnvironment;
        private readonly IConfiguration Configuration;

        public Startup(IConfiguration configuration, IWebHostEnvironment webHostEnvironment)
        {
            Configuration = configuration;
            this.webHostEnvironment = webHostEnvironment;
        }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddDbContext<EPDATAContext>((sp, option) =>
            {
                option.UseSqlServer(Configuration.GetValue<string>("ConnectionString"));
                option.AddInterceptors(sp.GetRequiredService<DbContextSessionContextInterceptor>());
                option.EnableSensitiveDataLogging(webHostEnvironment.IsDevelopment());
                if (webHostEnvironment.IsDevelopment())
                {
                    option.LogTo(Console.WriteLine);
                };
            });

            services.AddDbContextFactory<EPDATAContext>((sp, option) =>
            {
                option.UseSqlServer(Configuration.GetValue<string>("ConnectionString"));
                option.AddInterceptors(sp.GetRequiredService<DbContextSessionContextInterceptor>());
                option.EnableSensitiveDataLogging(webHostEnvironment.IsDevelopment());
                if (webHostEnvironment.IsDevelopment())
                {
                    option.LogTo(Console.WriteLine);
                };
            }, ServiceLifetime.Scoped);

            services.AddIdentity<BrandsAuthUser, BrandsAuthRole>(options =>
            {
                options.SignIn.RequireConfirmedAccount = false;
                options.SignIn.RequireConfirmedEmail = false;

                options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
                options.Lockout.MaxFailedAccessAttempts = 5;

            }).AddEntityFrameworkStores<EPDATAContext>()
            .AddDefaultTokenProviders();

            services.Configure<DataProtectionTokenProviderOptions>(o =>
            {
                o.Name = "Brands Paydeck Identity";
                o.TokenLifespan = TimeSpan.FromHours(72);
            });

            services.AddBrandsCors(Configuration);

            services.AddControllers().AddNewtonsoftJson(options =>
            {
                options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
            });
            services.AddSignalR().AddNewtonsoftJsonProtocol(options =>
            {
                options.PayloadSerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
            });

            services.AddSpaStaticFiles(configuration => { configuration.RootPath = "ClientApp/build"; });

            services.AddBrandsService(Configuration);

            var securityKey = new Microsoft.IdentityModel.Tokens.SymmetricSecurityKey(System.Text.Encoding.UTF8.GetBytes(Configuration["JwtConfiguration:SigningKey"]));
            services.AddBrandsUserIdentity(securityKey);

            services.AddAuthentication("SwaggerLogIn").AddCookie("SwaggerLogIn");

            //services.AddAuthentication().AddApiKeySupport(options => new ApiKeyAuthenticationOptions());

            services.AddBrandsWebJwtAuthentication(options =>
            {
                options.TokenIssuer = Configuration["JwtConfiguration:Issuer"];
                options.TokenAudience = Configuration["JwtConfiguration:Audience"];
                options.PrivateKey = Configuration["JwtConfiguration:SigningKey"];
                options.TokenLifetime = Convert.ToInt32(Configuration["JwtConfiguration:TokenLifetime"]);
            });

            services.AddBrandsSwagger();
            services.AddHttpContextAccessor();
            services.AddHttpsRedirection(options =>
            {
                options.HttpsPort = int.TryParse(Configuration["SslPort"], out int port) ? port : 443;
                options.RedirectStatusCode = StatusCodes.Status307TemporaryRedirect;
            });

            services.AddApplicationInsightsTelemetry();
            services.AddSingleton<ITelemetryInitializer, TelemetryInitializer>();

            services.AddDataProtection().SetApplicationName("Paydeck");

            services.AddBrandsAuthorization();
            services.AddHttpClient();

            services.AddHealthChecks()
                .AddDbContextCheck<EPDATAContext>("EPDATAContext")
                .AddCheck<EPDATAContextHealthCheck>("EPDATAContextHealthCheck")
                .AddCheck<RabbitMqHealthAsync>("RabbitMqHealthAsync");
            services.AddSingleton<ISentryUserFactory, SentryUserFactory>();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILogger<Startup> logger, ILoggerFactory loggerFactory)
        {
            logger.LogDebug("Entering Configure. EnvironmentName: {EnvironmentName}", env.EnvironmentName);
            app.UseCors();
            app.UseIPAddressAndJwtGuidLogging();
            app.UseRequestDurationLogging();
            app.UseExceptionHandling();

            var allowedOrigins = Configuration.GetSection(nameof(CorsOptions)).Get<CorsOptions>()?.AllowOrigins.Join('|');
            var jsnlogConfiguration = new JsnlogConfiguration
            {
                corsAllowedOriginsRegex = $"({allowedOrigins})",
                serverSideMessageFormat = "{ 'Sent': '%date', 'Browser': '%userAgent', 'Message': %jsonmessage, 'Url': '%url', 'Ip': '%userHostAddress' }"
            };
            app.UseJsnLogAddUserInfo();
            app.UseJSNLog(new CustomLoggingAdapter(loggerFactory), jsnlogConfiguration);

            app.UseMiddleware<RequestResponseLoggingMiddleware>();
            app.UseSerilogRequestLogging(opts => opts.EnrichDiagnosticContext = LogHelper.EnrichFromRequest);
            if (!env.IsDevelopment())
            {
                app.UseHsts();
            }
            app.UseWhen(context => CheckIfNotLocalhost(context, logger), builder =>
            {
                logger.LogDebug("I'm using UseHttpsRedirection");
                builder.UseHttpsRedirection();
            });

            app.UseStaticFiles(new StaticFileOptions
            {
                HttpsCompression = Microsoft.AspNetCore.Http.Features.HttpsCompressionMode.Compress,
                OnPrepareResponse = ctx =>
                {
                    if (Path.GetExtension(ctx.File.Name) != ".html")
                    {
                        double durationInSeconds = TimeSpan.FromDays(7).TotalSeconds;
                        ctx.Context.Response.Headers[Microsoft.Net.Http.Headers.HeaderNames.CacheControl] = "public,max-age=" + durationInSeconds;
                    }
                }
            });

            app.UseSpaStaticFiles(new StaticFileOptions
            {
                OnPrepareResponse = ctx =>
                {
                    if (Path.GetExtension(ctx.File.Name) != ".html")
                    {
                        double durationInSeconds = TimeSpan.FromDays(7).TotalSeconds;
                        ctx.Context.Response.Headers[Microsoft.Net.Http.Headers.HeaderNames.CacheControl] = "public,max-age=" + durationInSeconds;
                    }
                }
            });

            app.UseRouting();
            app.UseSentryTracing();
            app.UseAuthentication();
            app.UseUserInfoLogging();
            app.UseAuthorization();

            app.UseSwaggerWithAuthentication();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapHub<Hubs.MainHub>("/hub/Main", options => { options.CloseOnAuthenticationExpiration = true; });

                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller}/{action=Index}/{id?}");
                endpoints.MapHealthChecks("/health");
            });


#if !API
            app.MapWhen(CheckRouteNotApi, builder =>
            {
                builder.UseSpa(spa =>
                {
                    spa.Options.SourcePath = "ClientApp";
                    if (env.IsDevelopment() && Configuration.GetValue<string>("RunNpmStart") != "NO")
                    {
                        logger.LogDebug("In Debug Mode");
                        spa.UseReactDevelopmentServer(npmScript: "start");
                    }
                });
            });
#endif

            // Send a test message to Sentry to verify it's working
            var sentryId = SentrySdk.CaptureMessage("Application started successfully", SentryLevel.Info);
            logger.LogInformation("Sentry test message sent with ID: {SentryId}", sentryId);
        }

        private bool CheckRouteNotApi(HttpContext context)
        {
            return !context.Request.Path.Value.StartsWith("/api");
        }

        private bool CheckIfNotLocalhost(HttpContext context, ILogger<Startup> logger)
        {
            var host = context.Request.Host.Host.ToLower();
            logger.LogDebug("host: {host}", host);

            return host != "localhost";
        }
    }
}
