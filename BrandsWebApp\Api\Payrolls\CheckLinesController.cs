﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Filters;
using BrandsWebApp.Models.Payroll.CheckLine;
using BrandsWebApp.Services.Payroll;
using BrandsWebApp.Services.Validation.CheckValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Payrolls
{
    [Route("api/Payrolls/{payrollNumber}/Checks/{checkKey}/CheckLines")]
    [ApiController]
    [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
    [ServiceFilter(typeof(UpdatePayrollCheckAsyncFilter))]
    [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
    public class CheckLinesController : ControllerBase
    {
        private readonly CheckLinesService checkLinesService;

        public CheckLinesController(CheckLinesService checkLinesService)
        {
            this.checkLinesService = checkLinesService;
        }

        /// <summary>
        /// Updates check line by key
        /// </summary>
        [HttpPut("{checkLineKey}")]
        public async Task<IActionResult> UpdateCheckLine([FromRoute] string checkLineKey, [FromBody] UpdateCheckLine checkLine, [FromQuery] string checkUpdateGuid, [FromServices] UpdateCheckLineValidation validations)
        {
            checkLine.CheckLineKey = checkLineKey;
            var validationResult = await validations.ValidateAsync(checkLine);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            var result = await checkLinesService.UpdateCheckLineAsync(User.GetConum(), checkLine);
            return Ok(result);
        }

        /// <summary>
        /// Adds check line to a check within payroll
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> AddCheckLine([FromBody] AddCheckLine checkLine, [FromQuery] string checkUpdateGuid)
        {
            var newCheck = await checkLinesService.AddCheckLineAsync(User.GetConum(), checkLine);
            return Ok(newCheck);
        }

        /// <summary>
        /// Removes check line from payroll
        /// </summary>
        [HttpDelete("{checkLineKey}")]
        public async Task<IActionResult> RemoveCheckLine([FromRoute] string checkLineKey, [FromQuery] string checkUpdateGuid)
        {
            await checkLinesService.DeleteCheckLineAsync(User.GetConum(), checkLineKey);
            return NoContent();
        }

        /// <summary>
        /// Updates check line local tax
        /// </summary>
        [HttpPut("{checkLineKey}/LocalTaxes/{taxNumber}")]
        public async Task<IActionResult> UpdateCheckLineLocalTax([FromRoute] string checkLineKey, [FromRoute] int taxNumber, [FromBody] UpdateCheckLineLocalTax localTax, [FromQuery] string checkUpdateGuid)
        {
            localTax.TaxNumber = taxNumber;
            await checkLinesService.UpdateCheckLineLocalTaxAsync(User.GetConum(), checkLineKey, localTax);
            return NoContent();
        }
    }
}
