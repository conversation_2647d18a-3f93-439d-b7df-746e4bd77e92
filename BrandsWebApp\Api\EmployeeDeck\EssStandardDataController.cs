﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Filters;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.EmployeeOnboarding;
using BrandsWebApp.Services.EmployeeDeck;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.EmployeeDeck
{
    [ApiController]
    [Route("api/employee-deck/my")]
    public class EssStandardDataController : ControllerBase
    {
        private readonly EssStandardDataService standardDataService;

        public EssStandardDataController(EssStandardDataService standardDataService)
        {
            this.standardDataService = standardDataService;
        }

        [Authorize(Policy = AuthPolicies.IsEssUser)]
        [HttpGet("bank/{routingNumber}")]
        public async Task<ActionResult> GetBankByRoutingNumber([FromRoute] string routingNumber)
        {
            var bank = await standardDataService.GetBankInfoAsync(routingNumber);
            return Ok(bank);
        }

        [Authorize(Policy = AuthPolicies.IsEssOnboarding)]
        [HttpGet("personal-info")]
        public async Task<ActionResult> GetEmployeePersonalInfo()
        {
            var info = await standardDataService.GetEmployeePersonalInfoAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value);
            return Ok(info);
        }

        [Authorize(Policy = AuthPolicies.IsEssOnboarding)]
        [ServiceFilter(typeof(UpdateEmployeeOnboardingAsyncFilter))]
        [HttpPost("personal-info")]
        public async Task<ActionResult> UpdateEmployeePersonalInfo([FromBody] EmployeePersonalInfo info)
        {
            var result = await standardDataService.UpdateEmployeePersonalInfoAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value, info);
            return Ok(result);
        }

        [Authorize(Policy = AuthPolicies.IsEssOnboarding)]
        [HttpGet("contact-info")]
        public async Task<ActionResult> GetEmployeeContactInfo()
        {
            var info = await standardDataService.GetEmployeeContactInfoAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value);
            return Ok(info);
        }

        [Authorize(Policy = AuthPolicies.IsEssOnboarding)]
        [ServiceFilter(typeof(UpdateEmployeeOnboardingAsyncFilter))]
        [HttpPost("contact-info")]
        public async Task<ActionResult> UpdateEmployeeContactInfo([FromBody] EmployeeContactDetails details)
        {
            var result = await standardDataService.UpdateEmployeeContactInfoAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value, details);
            return Ok(result);
        }
    }
}
