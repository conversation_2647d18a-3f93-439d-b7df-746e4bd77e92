﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Payroll.VoidedCheck;
using BrandsWebApp.Services.Payroll;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Payrolls
{
    [Route("api/VoidedChecks")]
    [ApiController]
    [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
    public class VoidedChecksController : ControllerBase
    {
        private readonly VoidedChecksService voidedChecksService;

        public VoidedChecksController(VoidedChecksService voidedChecksService)
        {
            this.voidedChecksService = voidedChecksService;
        }

        /// <summary>
        /// Gets voided checks
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetVoidedChecks()
        {
            var voidedChecks = await voidedChecksService.GetVoidedChecksAsync(User.GetConum());
            return Ok(voidedChecks);
        }

        /// <summary>
        /// Gets voided check
        /// </summary>
        [HttpGet("{checkKey}")]
        public async Task<IActionResult> GetVoidedCheck([FromRoute] string checkKey)
        {
            var voidedCheck = await voidedChecksService.GetVoidedCheckAsync(User.GetConum(), checkKey);
            return Ok(voidedCheck);
        }

        /// <summary>
        /// Gets void options
        /// </summary>
        [HttpGet("{checkKey}/Options")]
        public async Task<IActionResult> GetCheckVoidOptions([FromRoute] string checkKey)
        {
            var options = await voidedChecksService.GetVoidOptionsAsync(User.GetConum(), checkKey);
            return Ok(options);
        }

        /// <summary>
        /// Adds voided check
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> AddVoidedCheck([FromBody] AddCheckToVoid voidCheck)
        {
            var checkMasterKey = await voidedChecksService.AddCheckToVoidAsync(User.GetConum(), $"{User.GetFirstName()} {User.GetLastName()}", voidCheck);
            return Ok(new { ActivePayrollCheckKey = checkMasterKey });
        }

        /// <summary>
        /// Removes voided check
        /// </summary>
        [HttpDelete("{checkKey}")]
        public async Task<IActionResult> RemoveVoidedCheck([FromRoute] string checkKey)
        {
            await voidedChecksService.RemoveCheckToVoidAsync(User.GetConum(), checkKey);
            return NoContent();
        }
    }
}
