﻿using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.Ess.BrandsAdmin;
using BrandsWebApp.Services.EmployeeDeck;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.EmployeeDeck
{
    /// <summary>
    /// Brands Admin - Access with ApiKeys and _optional_ IpAddresses restrictions defined in appsettings.json
    /// </summary>
    [ApiController]
    [Route("api/brands-admin")]
    [Authorize(Policy = AuthPolicies.EmployeeDeckBrandsAdminAccess)]
    public class EssBrandsAdminController : ControllerBase
    {
        private readonly ILogger<EssBrandsAdminController> logger;
        private readonly EssUserService essUserService;

        public EssBrandsAdminController(
            ILogger<EssBrandsAdminController> logger,
            EssUserService essUserService)
        {
            this.logger = logger;
            this.essUserService = essUserService;
        }

        [HttpPost("users/{coNum}")]
        public async Task<ActionResult> InviteCompanyUsers([FromRoute] int coNum, [FromBody] InviteEssUsers body)
        {
            logger.LogDebug("Entering InviteCompanyUsers. conum: {conum} {@Body}", coNum, body);
            var inviteResult = await essUserService.InviteCompanyUsersAsync(coNum, body);
            return Ok(inviteResult);
        }

        /// <summary>
        /// Creates a user.
        /// </summary>
        /// <remarks>
        /// Requires **email**.
        /// Requires **coNum** and **empNum**.
        /// Optional **pwd** (password) is recommended only for development...
        /// when not supplied, user will have to create their own password first.
        /// </remarks>
        [HttpPost("users")]
        public async Task<ActionResult> CreateUser([FromBody] AddEssUser body)
        {
            if (body.CoNum <= 0 || body.EmpNum <= 0)
            {
                return this.Error(
                    "EmployeeNotIdentified",
                    "Employee not identified."
                );
            }

            var result = await essUserService.AddEssUserAsync(body);
            if (result.Errors.Any())
            {
                return BadRequest(new { result.Errors });
            }

            return Ok(result);
        }

        [HttpPut("users/impersonate")]
        public async Task<ActionResult> ImpersonateUser([FromBody] ImpersonateEssUser body)
        {
            await essUserService.ImpersonateAsync(body.UserEmail, body.CoNum, body.EmpNum);
            return Ok();
        }

        [HttpPost("users/{coNum}/resend-invitation")]
        public async Task<ActionResult> ResendInvitation([FromRoute] int coNum, [FromBody] ResendEssUsersInvite body)
        {
            await essUserService.ResendInvitationAsync(coNum, body.EmpNums);      
            return Ok();
        }

        [HttpPost("{coNum}/users/{empNum}")]
        public async Task<ActionResult> ChangePassword([FromRoute] int coNum, [FromRoute] int empNum, [FromBody] ResetEssUserPassword body)
        {
            logger.LogDebug("Entering ChangePassword {conum} {empnum}", coNum, empNum);
            body.EmpNum = empNum;
            var result = await essUserService.ResetPasswordAsync(coNum, body);
            if (result.Errors.Any())
            {
                return BadRequest(result.Errors);
            }

            return Ok(result.Password);
        }
    }
}
