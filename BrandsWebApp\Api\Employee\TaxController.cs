﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Employee;
using BrandsWebApp.Services.Validation.EmployeeValidation;
using Dapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Brands.DataModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using BrandsWebApp.Services;
using Brands.DAL;
using BrandsWebApp.Services.Employee;
using BrandsWebApp.Models.Employee.Taxes;

namespace BrandsWebApp.Api.Employee
{
    [ApiController]
    [Route("api/Employees/{empnum}/Taxes")]
    [Authorize(Policy = nameof(Permission.ManageEmployees))]
    public class EmployeeTaxesController : ControllerBase
    {
        private readonly EPDATAContext ePDATAContext;
        private readonly ILogger<EmployeeController> logger;
        private readonly ISqlConnectionService sqlConnectionService;
        private readonly EmployeeService employeeService;
        private readonly StatesService statesService;
        private readonly EmployeeTaxesService employeeTaxesService;

        public EmployeeTaxesController(
            EPDATAContext ePDATAContext,
            ILogger<EmployeeController> logger,
            ISqlConnectionService sqlConnectionService,
            EmployeeService employeeService,
            StatesService statesService,
            EmployeeTaxesService employeeTaxesService)
        {
            this.ePDATAContext = ePDATAContext;
            this.logger = logger;
            this.sqlConnectionService = sqlConnectionService;
            this.employeeService = employeeService;
            this.statesService = statesService;
            this.employeeTaxesService = employeeTaxesService;
        }

        [HttpPost("Federal")]
        public async Task<IActionResult> UpdateFederalTaxes(int empnum, [FromBody] EmployeeW4 employeeW4, [FromServices] EmployeeW4Validation validations)
        {
            try
            {
                logger.LogDebug("Entering UpdateFederalTaxes {empnum} {@employeeW4}", empnum, employeeW4);
                var validationResult = await validations.ValidateAsync(employeeW4);
                if (!validationResult.IsValid)
                {
                    return BadRequest(validationResult);
                }
                Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);
                employeeW4.Copy(employee);
                await ePDATAContext.SaveChangesAsync();
                var updatedEmployeeW4 = new EmployeeW4(employee);
                return Ok(updatedEmployeeW4);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdateFederalTaxes");
                throw;
            }
        }


        [HttpGet("State")]
        public async Task<IActionResult> GetStateTax(int empnum)
        {
            logger.LogDebug("Entering GetStateTax. empnum: {empnum}", empnum);
            try
            {
                await using var con = sqlConnectionService.GetSqlConnection();
                var query = @"--DECLARE @conum DECIMAL(6,0) = 812, @Empnum DECIMAL(6,0) = 211
                    ;WITH states AS (
	                            SELECT 
		                            StateCode = glt.list_value,
		                            StateName = glt.list_subvalue  
	                            FROM global_lists_t glt 
	                            WHERE glt.list_name = 'US State List'
                            )
                            SELECT TOP 1
	                            UnemploymentState = e.UCI_STATE,
	                            UnemploymentExempt = e.SUTA_EXE_FG,
	                            WorkState = work.STATE,
	                            ResidenceState = res.STATE
                            FROM EMPLOYEE_T e 
                            LEFT JOIN STATE_EE_INFO_T work ON e.CONUM = work.CONUM AND e.EMPNUM = work.EMPNUM AND work.DEFAULT_WORK = 'YES' and ISNULL(work.[STATE], '') <> ''
                            LEFT JOIN STATE_EE_INFO_T res ON e.CONUM = res.CONUM AND e.EMPNUM = res.EMPNUM AND res.DEFAULT_RES = 'YES' and ISNULL(res.[STATE], '') <> ''
                            WHERE e.CONUM = @Conum AND e.EMPNUM = @Empnum";
                var empStates = await con.QuerySingleAsync<EmployeeStateTax>(query, new { Conum = User.GetConum(), empnum });

                var eligibleWithholdingStates = await statesService.GetEligibleWorkStatesAsync(User.GetConum(), empnum);

                return Ok(new
                {
                    StateTax = empStates,
                    EligibleWithholdingStates = eligibleWithholdingStates,
                    UnemploymentStates = await statesService.GetAvailableStatesAsync(User.GetConum(), true),
                    LocalTaxes = await employeeTaxesService.GetLocalTaxesAsync(User.GetConum(), empnum)
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetStateTax");
                throw;
            }
        }


        [HttpPost("State")]
        public async Task<IActionResult> UpdateStateTax(int empnum, EmployeeStateTax empStateTax, [FromServices] EmployeeStateTaxValidation validations)
        {
            logger.LogInformation("Entering UpdateStateTax {empnum} {@empStateTax}", empnum, empStateTax);
            try
            {
                empStateTax.Empnum = empnum;
                var validationResult = await validations.ValidateAsync(empStateTax);
                if (!validationResult.IsValid)
                {
                    return BadRequest(validationResult);
                }

                var states = await ePDATAContext.StateEmployeeInfos.Where(es => es.Conum == User.GetConum() && es.Empnum == empnum).ToListAsync();

                states.Single(s => s.State == empStateTax.ResidenceState).Default_Res = true.ToYesNo();
                states.Where(s => s.State != empStateTax.ResidenceState).ToList().ForEach(s => s.Default_Res = false.ToYesNo());

                states.Single(s => s.State == empStateTax.WorkState).Default_Work = true.ToYesNo();
                states.Where(s => s.State != empStateTax.WorkState).ToList().ForEach(s => s.Default_Work = false.ToYesNo());

                Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);
                employee.SUTA_EXE_FG = Convert.ToInt32(empStateTax.UnemploymentExempt);
                employee.UCI_STATE = empStateTax.UnemploymentState;

                await ePDATAContext.SaveChangesAsync();

                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdateStateTax");
                throw;
            }
        }

        [HttpGet("State/States/{state}/Options")]
        public async Task<IActionResult> EditEligibleWorkState(int empnum, string state)
        {
            logger.LogDebug("Entering EditState");
            try
            {
                state = state.ToUpper();
                var employeeState = await ePDATAContext.StateEmployeeInfos.SingleOrDefaultAsync(e => e.Conum == User.GetConum() && e.Empnum == empnum && e.State == state);
                if (employeeState == null)
                {
                    return NotFound();
                }

                var results = await GetEmployeeStateTaxOptions(state, employeeState, false);
                return Ok(results);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in EditState");
                throw;
            }
        }

        [HttpGet("State/States/Options")]
        public async Task<IActionResult> GetStatesDropDowns(int empnum)
        {
            logger.LogDebug("Entering GetStates");
            try
            {
                Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);
                var states = (await statesService.GetAvailableStatesAsync(User.GetConum())).Select(s => s.State).ToList();
                var empStates = await ePDATAContext.StateEmployeeInfos.Where(s => s.Conum == User.GetConum() && s.Empnum == empnum).ToListAsync();
                states.RemoveAll(s => empStates.Select(es => es.State).Contains(s));
                var resutls = new List<EmployeeStateOptions>();
                await using var con = sqlConnectionService.GetSqlConnection();
                foreach (var state in states)
                {
                    var stateOptions = await ePDATAContext.StateTaxes.Where(s => s.StateCode == state).ToListAsync();
                    var stateName = (await ePDATAContext.GlobalLists.SingleAsync(l => l.list_name == "US State List" && l.list_value == state)).list_subvalue;
                    var stateMerriedStatuses = (await con.QueryAsync<EmployeeStateStatus>("dbo.EP_State_Statuses", new
                    {
                        State = state
                    }, commandType: System.Data.CommandType.StoredProcedure)).Select(c => c.Code).ToList();
                    var stateEmployeeInfo = await con.QuerySingleAsync<StateEmployeeInfo>("custom.PaydeckNewEmpState", new
                    {
                        conum = User.GetConum(),
                        empnum,
                        state = state
                    }, commandType: System.Data.CommandType.StoredProcedure);
                    stateEmployeeInfo.St_Deps = employee.FedDeps;
                    stateEmployeeInfo.St_Status = GetStateMerriedStatus(stateMerriedStatuses, employee.FedStatus);
                    var options = new EmployeeStateOptions(stateOptions, stateEmployeeInfo, stateName, stateMerriedStatuses, await GetStateNotesAsync(state), false);
                    resutls.Add(options);
                }
                return Ok(resutls);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetStates");
                throw;
            }
        }

        [HttpPost("State/States/{state}")]
        public async Task<IActionResult> UpdateEligibleWorkState(int empnum, string state, [FromBody] StateEmployeeInfo stateEmployeeInfo)
        {
            logger.LogDebug("Entering UpdateEligibleWorkState. {Empnum} {state}", empnum, state);
            if (state != stateEmployeeInfo.State)
            {
                logger.LogWarning("Mismatch state in path and post data");
                return BadRequest();
            }

            if (empnum != stateEmployeeInfo.Empnum)
            {
                logger.LogWarning("Mismatch empnum in path and post data");
                return BadRequest();
            }

            if (User.GetConum() != stateEmployeeInfo.Conum)
            {
                logger.LogWarning("Mismatch conum in path and post data");
                return BadRequest();
            }

            var empState = await ePDATAContext.StateEmployeeInfos.SingleOrDefaultAsync(s => s.Conum == User.GetConum() && s.Empnum == empnum && s.State == stateEmployeeInfo.State);
            var results = await GetEmployeeStateTaxOptions(state, stateEmployeeInfo, true);
            if (empState == null)
            {
                ePDATAContext.StateEmployeeInfos.Add(stateEmployeeInfo);
            }
            else
            {
                ePDATAContext.Entry(empState).CurrentValues.SetValues(stateEmployeeInfo);
            }
            await ePDATAContext.SaveChangesAsync();

            var eligibleWithholdingStates = await statesService.GetEligibleWorkStatesAsync(User.GetConum(), empnum);
            return Ok(new
            {
                EligibleWorkState = eligibleWithholdingStates,
                LocalTaxes = await employeeTaxesService.GetLocalTaxesAsync(User.GetConum(), empnum)
            });
        }

        [HttpGet("State/LocalTaxes/{localId:long}")]
        public async Task<ActionResult<EmployeeLocalOptions>> GetLocalTax(int empnum, long localId)
        {
            var localTax = await employeeTaxesService.GetLocalTaxAsync(User.GetConum(), empnum, localId);
            return Ok(localTax);
        }

        [HttpGet("State/LocalTaxes/Options")]
        public async Task<ActionResult<IEnumerable<EmployeeLocalOptions>>> GetLocalTaxesOptions(int empnum)
        {
            // TODO: replace with filter and reuse for other endpoints
            Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);

            var options = await employeeTaxesService.GetLocalTaxesOptionsAsync(User.GetConum(), empnum);
            return Ok(options);
        }

        [HttpPost("State/LocalTaxes/{localId:long}")]
        public async Task<ActionResult<EmployeeLocalTaxes>> UpdateLocalTax(int empnum, long localId, [FromBody] EmployeeLocalTax localTax)
        {
            localTax.LocalId = localId;
            var localTaxes = await employeeTaxesService.UpdateLocalTaxAsync(User.GetConum(), empnum, localTax);            
            return Ok(localTaxes);
        }

        private string GetStateMerriedStatus(List<string> stateStatuses, string fedStatus)
        {
            if (fedStatus.IsNullOrWhiteSpace())
            {
                return string.Empty;
            }

            if (stateStatuses.Contains(fedStatus))
            {
                return fedStatus;
            }
            else
            {
                foreach (var status in stateStatuses)
                {
                    if (status.StartsWith(fedStatus, true, System.Globalization.CultureInfo.CurrentCulture))
                    {
                        return status;
                    }
                }
            }

            return string.Empty;
        }

        async Task<string> GetStateNotesAsync(string state)
        {
            logger.LogDebug("Entering GetStateNotesAsync. {state}", state);
            try
            {
                var stateInfo = await ePDATAContext.StateInfos.SingleOrDefaultAsync(si => si.StAbbr == state);
                if (stateInfo != null)
                {
                    return stateInfo.ee_wh_notes;
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetStateNotesAsync");
            }
            return string.Empty;
        }

        private async Task<EmployeeStateOptions> GetEmployeeStateTaxOptions(string state, StateEmployeeInfo employeeState, bool isUpdate)
        {
            var stateOptions = await ePDATAContext.StateTaxes.Where(s => s.StateCode == state).ToListAsync();
            var stateName = (await ePDATAContext.GlobalLists.SingleAsync(l => l.list_name == "US State List" && l.list_value == state)).list_subvalue;
            await using var con = sqlConnectionService.GetSqlConnection();
            var stateMerriedStatuses = (await con.QueryAsync<EmployeeStateStatus>("dbo.EP_State_Statuses", new
            {
                State = state
            }, commandType: System.Data.CommandType.StoredProcedure)).Select(c => c.Code).ToList();
            var results = new EmployeeStateOptions(stateOptions, employeeState, stateName, stateMerriedStatuses, await GetStateNotesAsync(state), isUpdate);
            return results;
        }
    }
}
