﻿using Brands.DAL;
using BrandsWebApp.Authentication;
using BrandsWebApp.Models;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/Log")]
    [ApiController]
    public class LogController : ControllerBase
    {
        private readonly EPDATAContext _dbContext;
        private readonly ILogger<FeedbackController> _logger;

        public LogController(EPDATAContext dbContext, ILogger<FeedbackController> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        [HttpPost("push")]
        public async Task<ActionResult> PushAsync([FromBody] BrowserError error)
        {
            try
            {
                AuthenticateResult result = await HttpContext.AuthenticateAsync();
                if (result.Succeeded)
                {
                    var userInfo = new
                    {
                        Conum = result.Principal.GetConum(),
                        EmpConum = result.Principal.GetEmpConum(),
                        Empnum = result.Principal.GetEmpnum(),
                        UserId = result.Principal.GetUserId(),
                        Email = result.Principal.GetEmail()
                    };
                    if (error.IsError)
                        _logger.LogError("Client Side Error. {@Error} IsUserLoggedIn: {IsUserLoggedIn} {@UserInfo}", error, result.Succeeded, userInfo);
                    else
                        _logger.LogWarning("Client Side Error. {@Error} IsUserLoggedIn: {IsUserLoggedIn} {@UserInfo}", error, result.Succeeded, userInfo);
                }
                else
                {
                    if (error.IsError)
                        _logger.LogError("Client Side Error. {@Error} IsUserLoggedIn: {IsUserLoggedIn}", error, result.Succeeded);
                    else
                        _logger.LogWarning("Client Side Error. {@Error} IsUserLoggedIn: {IsUserLoggedIn}", error, result.Succeeded);
                }
                return Ok(new
                {
                    error = new { },
                    code = 200
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "Error in PushAsync. {@body}", error);
                return Ok();
            }
        }
    }
}
