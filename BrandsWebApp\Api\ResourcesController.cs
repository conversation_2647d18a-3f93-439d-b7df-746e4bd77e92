﻿using Brands.DAL;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/Resources")]
    [ApiController]
    [Authorize]
    public class ResourcesController : ControllerBase
    {
        private readonly EPDATAContext _dbContext;
        private readonly ILogger<ResourcesController> _logger;

        public ResourcesController(EPDATAContext ePDATAContext, ILogger<ResourcesController> logger)
        {
            _dbContext = ePDATAContext;
            _logger = logger;
        }

        [HttpGet("{pageName}")]
        public async Task<IActionResult> GetResorces(string pageName)
        {
            try
            {
                var resorces = await _dbContext.ResorcesLinks.Where(rl => rl.PageName == pageName).ToListAsync();
                return Ok(resorces);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetResorces. Pagename: {PageName}", pageName);
                throw;
            }
        }

        [HttpGet("HelpCenterLink")]
        public IActionResult GetHelpCenterLink()
        {
            try
            {
                return Ok("https://support.brandspaycheck.com/hc/en-us");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetHelpCenterLink");
                throw;
            }
        }
    }
}
