﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Helpers;
using BrandsWebApp.Hubs;
using BrandsWebApp.Models.Onboarding;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Validation.OnboardingValidation;
using FluentValidation.Results;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.ClientOnboarding
{
    public partial class OnboardingController
    {

        [HttpPost("signpayroll")]
        public async Task<IActionResult> SignPayrollDocument(DocusignInfo docusignInfo, [FromServices] DocuSignValidation validations)
        {
            try
            {
                ValidationResult validationResult = await validations.ValidateAsync(docusignInfo);
                if (!validationResult.IsValid)
                {
                    await onboardingDataService.SaveOnboardingValidationFailedEvent(OnboardingEventType.SendDocForReview, validationResult);
                    return BadRequest(validationResult);
                }

                if (!await IsAllPriorStagesCompleted())
                {
                    await onboardingDataService.SaveFailedEvent(OnboardingEventType.SendDocForReview, new Exception("Required stages are not completed"));
                    return BadRequest(new
                    {
                        Message = "Please complete all required fields before submitting documents."
                    });
                }

                if (!docusignInfo.ShouldSignViaEmail)
                {
                    if (docusignInfo.EmailId.IsNullOrWhiteSpace())
                    {
                        docusignInfo.EmailId = User.GetEmail();
                    }
                }

                docusignInfo.RecipientSignerName = docusignInfo.ShouldSignViaEmail ? docusignInfo.Name : docusignInfo.LegalName;

                var onboardingDetails = await onboardingDataService.GetOnboardingData();
                var stage = Enum.TryParse(onboardingDetails.Stage, out Stage result) ? result : Stage.Unknown;
                var stageData = await onboardingDataService.GetDocusignData(onboardingDetails.Id, stage);
                if (onboardingDetails.DocusignCompletedOn.HasValue)
                {
                    await onboardingDataService.SaveFailedEvent(OnboardingEventType.SendDocForReview, new Exception("Already Signed"));
                    return Conflict("Already Signed");
                }
                else if (!docusignInfo.ShouldSignViaEmail && onboardingDetails.DocusignEnvelopeId != null && stageData?.SigningMethod == "Embedded" && !onboardingDetails.IsDocusignEnvelopeVoided)
                {
                    var signUrl = await onboardingDataService.GetEmbeddedSigningUrl(onboardingDetails.DocusignEnvelopeId.ToString(), docusignInfo);
                    return Ok(new { SigningMethod = "Link", Url = signUrl });
                }

                var (isSuccess, embeddedSigningUrl) = await onboardingDataService.GenerateEnvelopeForSigning(docusignInfo, onboardingDetails);

                if (!isSuccess)
                {
                    await onboardingDataService.SaveFailedEvent(OnboardingEventType.SendDocForReview, new Exception("Already Signed"));
                    return Conflict("Already Signed");
                }

                return Ok(new { SigningMethod = docusignInfo.ShouldSignViaEmail ? "Email" : "Link", Url = embeddedSigningUrl });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in SignPayrollDocument");
                await onboardingDataService.SaveFailedEvent(OnboardingEventType.SendDocForReview, ex);
                throw;
            }
        }

        private async Task<bool> IsAllPriorStagesCompleted()
        {
            var stageProgress = await onboardingDataService.GetProgressWithStages();
            return stageProgress
                .Where(s => s.RequiredForDocumentSigning.GetValueOrDefault())
                .ToList()
                .TrueForAll(stage => stage.Status == StageStatus.Completed.ToString() || stage.Status == StageStatus.Disabled.ToString());
        }

        [AllowAnonymous]
        [HttpPost("payrolldocsigned")]
        public async Task<IActionResult> PayrollDocSignedEventHook([FromServices] IHubContext<Hubs.MainHub> mainHubContext)
        {
            logger.LogDebug("Entering PayrollDocSignedEventHook");
            try
            {
                var body = string.Empty;
                using (var reader = new StreamReader(Request.Body, encoding: Encoding.UTF8))
                {
                    body = await reader.ReadToEndAsync();
                }
                bool isSenderValid = onboardingDataService.IsDocusignHookRequestHashValid(body);
                logger.LogDebug("PayrollDocSignedEventHook isSenderValid: {isSenderValid}", isSenderValid);

                if (!isSenderValid)
                {
                    return Unauthorized();
                }

                var value = JsonConvert.DeserializeObject<DocusignStatusInfo>(body);
                if (value.CompletedDateTime.HasValue)
                {
                    value.CompletedDateTime = value.CompletedDateTime.Value.ToLocalTime();
                }

                //NOTE. for this to work correctly, you most use "Event Message Delivery Mode" = "Aggregate" - otherwise the payload json has a diff structure.
                logger.LogDebug("PayrollDocSignedEventHook status: {status}", value.Status);
                if (value.Status == "completed")
                {
                    var onboarding = await onboardingDataService.SaveDocusignReviewCompletedStatus(value).ConfigureAwait(false);
                    if (onboarding != null)
                    {
                        await SignalROnboardingSignedUpdate(onboarding.Conum.ToString(), mainHubContext);
                    }
                    else
                    {
                        logger.LogWarning("onboarding is null - not triggering SignalR");
                    }
                }
                else if (value.Status.IsNullOrWhiteSpace())
                {
                    logger.LogWarning("//NOTE. for this to work correctly, you most use \"Event Message Delivery Mode\" = \"Aggregate\" - otherwise the payload json has a diff structure.");
                }
                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in PayrollDocSignedEventHook");
                throw;
            }
        }

        //[AllowAnonymous]
        //[HttpPost]
        //public async Task<IActionResult> Test(string conum, [FromServices] IHubContext<Hubs.MainHub> mainHubContext)
        //{
        //    try
        //    {
        //        await SignalROnboardingSignedUpdate(conum, mainHubContext);
        //        return Ok();
        //    }
        //    catch (Exception ex)
        //    {

        //        throw;
        //    }
        //}

        private async Task SignalROnboardingSignedUpdate(string conum, IHubContext<MainHub> mainHubContext)
        {
            logger.LogDebug("Entering SignalROnboardingSignedUpdate. {conum}", conum);
            var connections = MainHub.Connections.Values.Where(c => c.Conum == conum);
            logger.LogDebug("There's {count} connections matching {conum}", connections.Count(), conum);
            foreach (var connection in connections)
            {
                try
                {
                    logger.LogDebug("Pushing signalR Update to {ConnectionId} {conum}", connection.ConnectionId, conum);
                    await mainHubContext.Clients.Client(connection.ConnectionId).SendAsync("OnboardingSigned");
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error in SignalROnboardingSignedUpdate. {conum} {ConnectionId}", conum, connection.ConnectionId);
                    throw;
                }
            }
        }

        [HttpPost("confirmdocusign")]
        public async Task<IActionResult> ConfirmDocusign()
        {
            try
            {
                var result = await onboardingDataService.SaveDocusignUserConfirmedStage();
                if (result == true)
                {
                    return Ok();
                }
                else
                {
                    return BadRequest(new
                    {
                        Message = "Failed to update stage to DocuSign confirmed"
                    });
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in ConfirmDocusign");
                await onboardingDataService.SaveFailedEvent("ConfirmDocusign", ex);
                throw;
            }
        }

        [HttpPost("create_custom_fields")]
        [AllowAnonymous]
        public async Task<IActionResult> CreateCustomFields([FromServices] DocusignHelper docusignHelper, [FromServices] SecurityUtils securityUtils)
        {
            try
            {
                if (!await securityUtils.IsInternalIpAddressAsync())
                {
                    return BadRequest();
                }
                await docusignHelper.CreateCustomFields();
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }
            return Ok();
        }
    }
}
