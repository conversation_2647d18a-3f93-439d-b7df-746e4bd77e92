﻿using Brands.DAL;
using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Services;
using BrandsWebApp.Services.EmployeeDeck;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [ApiController]
    [Route("api/SwipeClock")]
    public class SwipeClockController : ControllerBase
    {
        private readonly ILogger<SwipeClockController> logger;
        private readonly SwipeClockApiService swipeClockApiService;
        private readonly EPDATAContext dataContext;
        private readonly EssProfileService essProfileService;

        public SwipeClockController(ILogger<SwipeClockController> logger, SwipeClockApiService swipeClockApiService, EPDATAContext dataContext, EssProfileService essProfileService)
        {
            this.logger = logger;
            this.swipeClockApiService = swipeClockApiService;
            this.dataContext = dataContext;
            this.essProfileService = essProfileService;
        }

        //[HttpGet("token")]
        //public async Task<IActionResult> GetToken(int siteId, string SwipeClockLoginName, string product, string sub)
        //{
        //    var internalToken = swipeClockJwtHandler.IssueToken(SwipeClockLoginName, siteId, product, sub);
        //    return Ok(internalToken);
        //}

        [Authorize(Policy = AuthPolicies.IsCompanyOrEssUser)]
        [HttpGet("SSO")]
        public async Task<IActionResult> GetSwipeClockSSOUrl()
        {
            logger.LogDebug("Entering GetSwipeClockToken");
            try
            {
                var isEssUser = User.GetEssProfileId() != null;
                int? siteId;
                string swipeClockLoginName;
                string swipeClockProduct;
                string userType;
                if (isEssUser)
                {
                    swipeClockLoginName = User.GetEssEmpNum().Value.ToString();
                    swipeClockProduct = "twpemp";
                    userType = "empcode";
                    siteId = await swipeClockApiService.GetSwipeClockSiteId(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value);
                }
                else
                {
                    var userEmployee = await dataContext.BrandsUserEmployees.SingleOrDefaultAsync(u => u.UserId == User.GetPaydeckUserId() && u.Conum == User.GetConum());
                    if (userEmployee == null)
                    {
                        logger.LogError("UserEmployee was somehow null.");
                        return BadRequest();
                    }

                    swipeClockLoginName = userEmployee.SwipeClockLoginName;
                    swipeClockProduct = "twplogin";
                    userType = "login";
                    siteId = await swipeClockApiService.GetSwipeClockSiteId(User.GetConum());
                }
                    
                if (!siteId.HasValue)
                {
                    logger.LogWarning("Company SiteId missing.");
                    return BadRequest();
                }
                
                var token = await swipeClockApiService.GetSwipeClockToken(siteId.Value, userType, swipeClockLoginName, swipeClockProduct, "partner");
                return Ok(new
                {
                    url = isEssUser ? $"https://clock.payrollservers.us/ess/main/?jwt={token}" : $"https://payrollservers.us/pg/login.aspx?jwt={token}"
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetSwipeClockToken");
                return BadRequest();
            }
        }

        [Authorize(Policy = AuthPolicies.IsCompanyUser)]
        [HttpGet("logins")]
        public async Task<IActionResult> GetSwipeClockUsers()
        {
            logger.LogDebug("Entering GetSwipeClockUsers");
            try
            {
                return Ok(await swipeClockApiService.GetUsersAsync(User.GetConum()));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetSwipeClockUsers");
                return BadRequest();
            }
        }
    }
}
