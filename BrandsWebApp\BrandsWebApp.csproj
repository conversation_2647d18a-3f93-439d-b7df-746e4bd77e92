﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk.Web">
	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<IsPackable>false</IsPackable>
		<SpaRoot>ClientApp\</SpaRoot>
		<UserSecretsId>63a47cf1-026d-4caf-b635-be8eb3fff527</UserSecretsId>
		<Configurations>Debug;API;Release</Configurations>
		<EnableMSDeployAppOffline>true</EnableMSDeployAppOffline>
		<NoWarn>NU1603</NoWarn>
		<DisableImplicitNuGetFallbackFolder>true</DisableImplicitNuGetFallbackFolder>
		<VersionPrefix>25.9.0</VersionPrefix>
		<VersionSuffix></VersionSuffix>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DefineConstants>TRACE</DefineConstants>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='API|AnyCPU'">
		<DefineConstants>DEBUG;TRACE;API</DefineConstants>
		<DocumentationFile>bin\API\net5.0\BrandsWebApp.xml</DocumentationFile>
		<NoWarn>1701;1702;1705;1591</NoWarn>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile>bin\Debug\$(TargetFramework)\$(MSBuildProjectName).xml</DocumentationFile>
		<NoWarn>1701;1702;1705;1591</NoWarn>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<DocumentationFile>bin\Release\$(TargetFramework)\$(MSBuildProjectName).xml</DocumentationFile>
		<NoWarn>1701;1702;1705;1591</NoWarn>
	</PropertyGroup>
	<ItemGroup>
		<Content Remove="$(SpaRoot)**" />
		<None Include="$(SpaRoot)**" Exclude="$(SpaRoot)**\node_modules\**" />
	</ItemGroup>
	<ItemGroup>
		<Compile Remove="Api\PrimaryReportsController.cs" />
		<Compile Remove="Api\RunReportsController.cs" />
		<Compile Remove="Authentication\IdentityDataContextFactory.cs" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="AspNetSerilog" Version="1.8.0" />
		<PackageReference Include="CsvHelper" Version="33.1.0" />
		<PackageReference Include="Dapper" Version="2.1.66" />
		<PackageReference Include="Destructurama.JsonNet" Version="4.0.2" />
		<PackageReference Include="DevExtreme.AspNet.Data" Version="5.1.0" />
		<PackageReference Include="DistributedLock.SqlServer" Version="1.0.6" />
		<PackageReference Include="DocuSign.eSign.dll" Version="8.2.0" />
		<PackageReference Include="DevExpress.Document.Processor" Version="24.2.7" />
		<PackageReference Include="EasyNetQ" Version="7.8.0" />
		<PackageReference Include="FluentValidation" Version="12.0.0" />
		<PackageReference Include="Flurl.Http" Version="4.0.2" />
		<PackageReference Include="Going.Plaid" Version="6.44.0" />
		<PackageReference Include="Handlebars.Net" Version="2.1.6" />
		<PackageReference Include="Humanizer" Version="2.14.1" />
		<PackageReference Include="IPAddressRange" Version="6.2.0" />
		<PackageReference Include="JSNLog" Version="3.0.3" />
		<PackageReference Include="LazyCache.AspNetCore" Version="2.4.0" />
		<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
		<PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.6" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.6" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.6" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson" Version="9.0.6" />
		<PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="9.0.6" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.6" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="9.0.6" />
		<PackageReference Include="Microsoft.Extensions.Identity.Core" Version="9.0.6" />
		<PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="9.0.6" />
		<PackageReference Include="Microsoft.Extensions.Logging.ApplicationInsights" Version="2.23.0" />
		<PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="PasswordGenerator" Version="2.1.0" />
		<PackageReference Include="Sentry.AspNetCore" Version="5.10.0" />
		<PackageReference Include="Sentry.Serilog" Version="5.10.0" />
		<PackageReference Include="Serilog" Version="4.3.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
		<PackageReference Include="Serilog.Enrichers.Process" Version="3.0.0" />
		<PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
		<PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
		<PackageReference Include="Serilog.Exceptions.EntityFrameworkCore" Version="8.4.0" />
		<PackageReference Include="Serilog.Exceptions.MsSqlServer" Version="8.4.0" />
		<PackageReference Include="Serilog.Exceptions.SqlServer" Version="8.4.0" />
		<PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.Seq" Version="9.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="8.1.4" />
		<PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="8.1.4" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="8.1.4" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="8.1.4" />
		<PackageReference Include="Microsoft.AspNet.SignalR" Version="2.4.3" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.0" />
		<PackageReference Include="Twilio" Version="7.11.1" />
		<PackageReference Include="UAParser" Version="3.1.47" />
		<PackageReference Include="ZendeskApi_v2" Version="3.12.4" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\Brands.DAL\Brands.DAL.csproj" />
		<ProjectReference Include="..\Brands.DataModels\Brands.DataModels.csproj" />
		<ProjectReference Include="..\QueueBuilder\QueueBuilder.csproj" />
		<ProjectReference Include="..\PayrollUtility\PayrollUtility.csproj" />
		<ProjectReference Include="..\RpcMessagingModels\RpcMessagingModels.csproj" />
	</ItemGroup>
	<ItemGroup>
		<WCFMetadata Include="Connected Services" />
	</ItemGroup>
	<ItemGroup>
	  <Folder Include="Api\CPA\" />
	</ItemGroup>
	<Target Name="DebugEnsureNodeEnv" BeforeTargets="Build" Condition=" '$(Configuration)' == 'Debug' And !Exists('$(SpaRoot)node_modules') And '$(build_npm)' != 'NO'">
		<!-- Ensure Node.js is installed -->
		<Exec Command="node --version" ContinueOnError="true">
			<Output TaskParameter="ExitCode" PropertyName="ErrorCode" />
		</Exec>
		<Error Condition="'$(ErrorCode)' != '0'" Text="Node.js is required to build and run this project. To continue, please install Node.js from https://nodejs.org/, and then restart your command prompt or IDE." />
		<Message Importance="high" Text="Restoring dependencies using 'npm'. This may take several minutes..." />
		<Exec WorkingDirectory="$(SpaRoot)" Command="npm install" />
	</Target>
	<Target Name="PublishRunWebpack" AfterTargets="ComputeFilesToPublish" Condition=" '$(build_npm)' != 'NO'">
		<!--As part of publishing, ensure the JS resources are freshly built in production mode-->
		<Exec WorkingDirectory="$(SpaRoot)" Command="npm install" />
		<Exec WorkingDirectory="$(SpaRoot)" Command="npm version $(buildNumber_npm)" Condition=" '$(Configuration)' == 'Release' And $(buildNumber) != ''" />
		<Message Importance="high" Text="(print: npm run build): ./node_modules/.bin/cross-env &quot;REACT_APP_ENV=$(REACT_APP_ENV)&quot; &quot;REACT_APP_HOST_URL=$(host_url)&quot; npm run build$(ScriptName)" />
		<Exec WorkingDirectory="$(SpaRoot)" Command="./node_modules/.bin/cross-env &quot;REACT_APP_ENV=$(REACT_APP_ENV)&quot; &quot;REACT_APP_HOST_URL=$(host_url)&quot; npm run build$(ScriptName)" />
		<Message Importance="high" Text="REACT_APP_ENV=$(REACT_APP_ENV)" />
		<!--Include the newly-built files in the publish output-->
		<ItemGroup>
			<DistFiles Include="$(SpaRoot)build\**" />
			<ResolvedFileToPublish Include="@(DistFiles->'%(FullPath)')" Exclude="@(ResolvedFileToPublish)">
				<RelativePath>%(DistFiles.Identity)</RelativePath>
				<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
			</ResolvedFileToPublish>
		</ItemGroup>
	</Target>
</Project>