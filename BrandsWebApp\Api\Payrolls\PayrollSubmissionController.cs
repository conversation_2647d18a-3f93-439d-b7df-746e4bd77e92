﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Filters;
using BrandsWebApp.Helpers;
using BrandsWebApp.Models.Payroll;
using BrandsWebApp.Services.Payroll;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using RpcMessagingModels.SubmitPayroll;
using System;
using System.Threading.Tasks;
using Dapper;

namespace BrandsWebApp.Api.Payrolls
{
    [Route("api/Payrolls")]
    [ApiController]
    public class PayrollSubmissionController : ControllerBase
    {
        private readonly PayrollsService payrollsService;
        private readonly PayrollSummaryService payrollSummaryService;
        private readonly PayrollChecksService checksService;
        private readonly PayrollHelper payrollHelper;
        private readonly PayrollSignalRUpdateService payrollSignalRUpdateService;
        private readonly ILogger<PayrollSubmissionController> logger;

        public PayrollSubmissionController(
            PayrollsService payrollsService,
            ILogger<PayrollSubmissionController> logger,
            PayrollChecksService checksService,
            PayrollSignalRUpdateService payrollSignalRUpdateService,
            PayrollSummaryService payrollSummaryService,
            PayrollHelper payrollHelper)
        {
            this.payrollsService = payrollsService;
            this.logger = logger;
            this.checksService = checksService;
            this.payrollSignalRUpdateService = payrollSignalRUpdateService;
            this.payrollSummaryService = payrollSummaryService;
            this.payrollHelper = payrollHelper;
        }

        /// <summary>
        /// Gets payroll summary
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [HttpGet("{payrollNumber}/Summary")]
        public async Task<IActionResult> GetPayrollSummary([FromRoute] decimal payrollNumber)
        {
            var result = await payrollSummaryService.GetPayrollSummaryAsync(User.GetConum(), payrollNumber);
            return Ok(result);
        }

        /// <summary>
        /// Gets payroll status
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [HttpGet("{payrollNumber}/Status")]
        public async Task<IActionResult> GetPayrollStatus([FromRoute] decimal payrollNumber)
        {
            var payrollStatus = await payrollHelper.GetPayrollStatusUpdateAsync(User.GetConum(), payrollNumber, true);
            return Ok(payrollStatus);
        }

        /// <summary>
        /// Gets payroll totals
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [HttpGet("{payrollNumber}/Totals")]
        public async Task<IActionResult> GetPayrollTotals([FromRoute] decimal payrollNumber)
        {
            var result = await payrollSummaryService.GetPayrollTotalsAsync(User.GetConum(), payrollNumber);
            return Ok(result);
        }

        /// <summary>
        /// Validates payroll
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpPost("{payrollNumber}/Validation")]
        public async Task<IActionResult> ValidatePayroll([FromRoute] decimal payrollNumber)
        {
            await payrollSummaryService.ValidatePayrollAsync(User.GetConum(), payrollNumber);
            return NoContent();
        }

        /// <summary>
        /// Submits payroll
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpPost("{payrollNumber}")]
        public async Task<IActionResult> SubmitPayroll([FromRoute] decimal payrollNumber)
        {
            var areChecksCreated = await checksService.AreAllChecksCreatedAsync(User.GetConum(), payrollNumber, true);
            if (!areChecksCreated)
            {
                return BadRequest("Payroll cannot be submitted while checks are still being created");
            }
            await payrollsService.SubmitPayrollAsync(User.GetConum(), payrollNumber, User.GetFullName(), User.GetEmpnum());
            return NoContent();
        }

        /// <summary>
        /// Sends payroll feedback
        /// </summary>
        [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
        [HttpPost("{payrollNumber}/Feedback")]
        public async Task<IActionResult> SendPayrollFeedback([FromRoute] decimal payrollNumber, [FromBody] PayrollFeedback feedback)
        {
            await payrollsService.SendPayrollFeedbackAsync(User.GetConum(), payrollNumber, User.GetPaydeckUserId(), feedback);
            return NoContent();
        }

        /// <summary>
        /// Pushes submit payroll status to active SignalR connections
        /// </summary>
        [AllowAnonymous]
        [HttpPost("SubmitPayrollStatusUpdate")]
        public async Task<IActionResult> SubmitPayrollStatusUpdate([FromBody] SubmitPayrollResponse submitPayrollResponse)
        {
            logger.LogDebug("Entering SubmitPayrollStatusUpdate");
            if (!HttpContext.Request.CheckSignalRHeader())
            {
                logger.LogWarning("Unauthorized in SubmitPayrollStatusUpdate");
                return Unauthorized();
            }

            await payrollSignalRUpdateService.SubmitPayrollStatusUpdateAsync(submitPayrollResponse);
            return NoContent();
        }

        /// <summary>
        /// Gets the minimum submission date for payroll
        /// </summary>
        [HttpGet("MinSubmissionDate")]
        public async Task<IActionResult> GetMinSubmissionDate()
        {
            var conum = User.GetConum();
            var minSubmissionDate = await payrollHelper.GetNextSubmissionDateAsync(conum);
            return Ok(minSubmissionDate);
        }
    }
}
