﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Services.EmployeeDeck;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.EmployeeDeck
{
    [Authorize(Policy = AuthPolicies.IsEssUser)]
    [ApiController]
    [Route("api/employee-deck/my/dashboard")]
    public class EssDashboardController : ControllerBase
    {
        private readonly EssDashboardService essDashboardService;

        public EssDashboardController(EssDashboardService essDashboardService)
        {
            this.essDashboardService = essDashboardService;
        }

        [HttpGet]
        public async Task<IActionResult> GetDashboardData()
        {
            var data = await essDashboardService.GetDashboardDataAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value);
            return Ok(data);
        }
    }
}
