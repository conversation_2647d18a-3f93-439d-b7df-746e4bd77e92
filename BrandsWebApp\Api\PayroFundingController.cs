﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Services;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Authorize]
    [ApiController]
    [Route("api/PayroFunding")]
    [Authorize(Policy = nameof(Permission.PayrollFunding))]
    public class PayroFundingController : ControllerBase
    {
        private readonly ILogger<PayroFundingController> logger;
        private readonly ISqlConnectionService sqlConnectionService;

        public PayroFundingController(ILogger<PayroFundingController> logger, ISqlConnectionService sqlConnectionService)
        {
            this.logger = logger;
            this.sqlConnectionService = sqlConnectionService;
        }

        [HttpGet("Payrolls")]
        public async Task<IActionResult> GetPayrollsAsync()
        {
            logger.LogDebug("Entering GetPayrollsAsync");
            try
            {
                await using var con = sqlConnectionService.GetSqlConnection();
                var payrolls = await con.QueryAsync<Models.PayrolFunding>("[custom].[prc_PaydeckServicePayroFundingPage]", new
                {
                    conum = User.GetConum()
                }, commandType: System.Data.CommandType.StoredProcedure);

                var balance = await con.QuerySingleAsync<Balance>(@"select 
                    isnull(max(pf.FundingAmount), 0) TotalCreditLine, 
                    isnull(custom.fn_PayrollFundingAvail(@conum, getdate(), null), 0) AvailableBalance
                    from custom.PayrollFunding pf
                    where conum = @conum and status = 'Approved'", new { conum = User.GetConum() });

                return Ok(new
                {
                    balance.AvailableBalance,
                    balance.TotalCreditLine,
                    Payroll = payrolls
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetPayrolls");
                return BadRequest();
            }
        }

        [HttpPost("UpdatePaybackDate")]
        public async Task<IActionResult> UpdatePaybackDateAsync(UpdatePaybackDateModel updatePaybackDateModel)
        {
            logger.LogDebug("Entering UpdatePaybackDate. {@updatePaybackDateModel}", updatePaybackDateModel);
            try
            {
                await using var con = sqlConnectionService.GetSqlConnection();
                var results = await con.QueryAsync<UpdateFundingResults>("[custom].[prc_PayroFundingRequest]", new
                {
                    conum = User.GetConum(),
                    prnum = updatePaybackDateModel.Prnum,
                    FundingAmt = (int?)null,
                    PayBackDate = updatePaybackDateModel.PaybackDate
                }, commandType: System.Data.CommandType.StoredProcedure);
                return Ok(results);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdatePaybackDate");
                return BadRequest();
            }
        }

        [HttpPost("FundPayroll")]
        public async Task<IActionResult> FundPayrollAsync(FundPayrollModel fundPayrollModel)
        {
            logger.LogDebug("Entering FundPayroll. {@fundPayrollModel}", fundPayrollModel);
            try
            {
                await using var con = sqlConnectionService.GetSqlConnection();
                var results = await con.QueryAsync<UpdateFundingResults>("[custom].[prc_PayroFundingRequest]", new
                {
                    conum = User.GetConum(),
                    prnum = fundPayrollModel.Prnum,
                    PayBackDate = fundPayrollModel.PaybackDate,
                    FundingAmt = fundPayrollModel.Amount
                }, commandType: System.Data.CommandType.StoredProcedure);
                return Ok(results);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdatePaybackDate");
                return BadRequest();
            }
        }

        public class UpdatePaybackDateModel
        {
            public int Prnum { get; set; }
            public DateTime PaybackDate { get; set; }
        }

        public class FundPayrollModel
        {
            public int Prnum { get; set; }
            public DateTime PaybackDate { get; set; }
            public decimal Amount { get; set; }
        }

        public class Balance
        {
            public decimal TotalCreditLine { get; set; }
            public decimal AvailableBalance { get; set; }
        }

        public class UpdateFundingResults
        {
            public bool Success { get; set; }
            public string Message { get; set; }
        }
    }
}
