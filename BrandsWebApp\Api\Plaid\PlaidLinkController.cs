﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.Plaid;
using BrandsWebApp.Services;
using Going.Plaid;
using Going.Plaid.Accounts;
using Going.Plaid.Auth;
using Going.Plaid.Entity;
using Going.Plaid.Item;
using Going.Plaid.Link;
using Going.Plaid.WebhookVerificationKey;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Plaid
{
    [Route("api/PlaidLink")]
    [ApiController]
    [Authorize(Policy = nameof(AuthPolicies.OnboardingOrPayroll))]
    public class PlaidLinkController : ControllerBase
    {
        private readonly ILogger<PlaidLinkController> logger;
        private readonly PlaidClient plaidClient;
        private readonly PlaidConfigOptions plaidOptions;
        private readonly PlaidService plaidService;
        private readonly IHttpContextAccessor httpContextAccessor;

        public PlaidLinkController(
            ILogger<PlaidLinkController> logger,
            PlaidClient plaidClient,
            IOptionsSnapshot<PlaidConfigOptions> plaidOptions,
            PlaidService plaidService,
            IHttpContextAccessor httpContextAccessor)
        {
            this.logger = logger;
            this.plaidClient = plaidClient;
            this.plaidOptions = plaidOptions.Value;
            this.plaidService = plaidService;
            this.httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// Gets link token
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> CreateLinkToken()
        {
            logger.LogInformation($"CreateLinkToken");

            var plaidCreds = await plaidService.GetPlaidCredentialsAsync();
            var response = await plaidClient.LinkTokenCreateAsync(new LinkTokenCreateRequest()
            {
                User = new LinkTokenCreateRequestUser { ClientUserId = User.GetPaydeckUserId().ToString() },
                ClientName = plaidOptions.ClientName,
                Products = plaidOptions.Products.Split(',').Select(p => Enum.Parse<Products>(p, true)).ToArray(),
                Language = Language.English,
                CountryCodes = plaidOptions.CountryCodes.Split(',').Select(p => Enum.Parse<CountryCode>(p, true)).ToArray(),
                ClientId = plaidCreds.ClientId,
                Secret = plaidCreds.Secret,
                Auth = new LinkTokenCreateRequestAuth { AutomatedMicrodepositsEnabled = true },
                Webhook = $"{httpContextAccessor.HttpContext.Request.Scheme}://{httpContextAccessor.HttpContext.Request.Host}/api/PlaidLink/Update"
            });

            if (response.Error != null)
            {
                logger.LogError(response.Error.ErrorMessage);
                return BadRequest();
            }

            logger.LogInformation("CreateLinkToken OK: {linkToken}", response.LinkToken);
            return Ok(new { response.LinkToken });
        }

        /// <summary>
        /// Adds plaid token, accounts and balance
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> ExchangePublicToken([FromBody] ExchangePublicToken exchange)
        {
            logger.LogInformation("ExchangePublicToken: {token}", exchange.PublicToken);

            var plaidCreds = await plaidService.GetPlaidCredentialsAsync();
            var request = new ItemPublicTokenExchangeRequest()
            {
                ClientId = plaidCreds.ClientId,
                Secret = plaidCreds.Secret,
                PublicToken = exchange.PublicToken
            };

            var response = await plaidClient.ItemPublicTokenExchangeAsync(request);

            if (response.Error != null)
            {
                logger.LogError(response.Error.ErrorMessage);
                return BadRequest();
            }

            logger.LogInformation("ExchangePublicToken OK: ItemId {itemId}, AccessToken {token}", response.ItemId, response.AccessToken);

            int plaidId = await plaidService.AddPlaidTokenAsync(User.GetConum(), response.AccessToken, response.ItemId);
            var accounts = await GetPlaidAccounts(response.AccessToken, plaidCreds);
            await plaidService.UpdateBankAccountsAsync(plaidId, accounts);
            
            var balances = await GetPlaidBalances(response.AccessToken, plaidCreds);
            await plaidService.UpdateAccountsBalanceAsync(plaidId, balances);

            return NoContent();
        }

        /// <summary>
        /// Adds plaid update (used by plaid webhooks)
        /// </summary>
        [AllowAnonymous]
        [HttpPost("Update")]
        public async Task<IActionResult> PlaidUpdate()
        {
            string rawContent = string.Empty;
            using (var reader = new StreamReader(Request.Body, encoding: Encoding.UTF8, detectEncodingFromByteOrderMarks: false))
            {
                rawContent = await reader.ReadToEndAsync();
            }

            if (!HttpContext.Request.Headers.TryGetValue("plaid-verification", out var plaidToken))
            {
                logger.LogWarning("Unauthorized in PlaidUpdate: plaid-verification header missing");
                return Unauthorized();
            }

            var verified = await VerifyPlaidWebHookRequest(rawContent, plaidToken);
            if (!verified)
            {
                logger.LogWarning("Unauthorized in PlaidUpdate: invalid request");
                return Unauthorized();
            }

            await plaidService.AddPlaidUpdateAsync(rawContent);
            return NoContent();
        }

        private async Task<IEnumerable<PlaidBankAccount>> GetPlaidAccounts(string accessToken, PlaidCredentials plaidCredentials)
        {
            var authRequest = new AuthGetRequest();
            authRequest.AccessToken = accessToken;
            authRequest.ClientId = plaidCredentials.ClientId;
            authRequest.Secret = plaidCredentials.Secret;

            var authResponse = await plaidClient.AuthGetAsync(authRequest);
            if (authResponse.Error != null)
            {
                logger.LogError(authResponse.Error.ErrorMessage);
                throw new Exception();
            }

            Account AccountFor(string id) => authResponse.Accounts.Where(x => x.AccountId == id).SingleOrDefault();

            var accounts = authResponse.Numbers.Ach
                .Select(x => new PlaidBankAccount
                {
                    Name = AccountFor(x.AccountId)?.Name ?? String.Empty,
                    Balance = AccountFor(x.AccountId)?.Balances?.Current,
                    Number = x.Account,
                    RoutingNumber = x.Routing,
                    Id = x.AccountId,
                    Mask = AccountFor(x.AccountId)?.Mask,
                    Subtype = AccountFor(x.AccountId)?.Subtype.ToString().FirstOrDefault()
                });

            return accounts;
        }

        private async Task<IEnumerable<PlaidAccountBalance>> GetPlaidBalances(string accessToken, PlaidCredentials plaidCredentials)
        {
            var request = new AccountsBalanceGetRequest();
            request.AccessToken = accessToken;
            request.ClientId = plaidCredentials.ClientId;
            request.Secret = plaidCredentials.Secret;

            var response = await plaidClient.AccountsBalanceGetAsync(request);
            if (response.Error != null)
            {
                logger.LogError(response.Error.ErrorMessage);
                throw new Exception();
            }

            Account AccountFor(string id) => response.Accounts.Where(x => x.AccountId == id).SingleOrDefault();

            var balances = response.Accounts
                .Select(x => new PlaidAccountBalance
                {
                    Id = x.AccountId,
                    Name = AccountFor(x.AccountId)?.Name ?? String.Empty,
                    Balance = AccountFor(x.AccountId)?.Balances?.Current,
                });

            return balances;
        }

        private async Task<bool> VerifyPlaidWebHookRequest(string body, string plaidToken)
        {
            logger.LogInformation("Entering VerifyPlaidWebHookRequest");
            var handler = new JwtSecurityTokenHandler();
            var jwtToken = handler.ReadJwtToken(plaidToken);
            if (jwtToken.SignatureAlgorithm != "ES256")
            {
                logger.LogInformation("Invalid signature algorithm");
                return false;
            }

            var plaidCreds = await plaidService.GetPlaidCredentialsAsync();
            var verificationRequest = new WebhookVerificationKeyGetRequest()
            {
                KeyId = jwtToken.Header.Kid,
                ClientId = plaidCreds.ClientId,
                Secret = plaidCreds.Secret
            };

            var verificationResponse = await plaidClient.WebhookVerificationKeyGetAsync(verificationRequest);  
            var webkey = new JsonWebKey()
            {
                Alg = verificationResponse.Key.Alg,
                Crv = verificationResponse.Key.Crv,
                Kty = verificationResponse.Key.Kty,
                Use = verificationResponse.Key.Use,
                X = verificationResponse.Key.X,
                Y = verificationResponse.Key.Y,
            };
                
            var validateToken = await handler.ValidateTokenAsync(plaidToken, new TokenValidationParameters
            {
                IssuerSigningKey = webkey,
                ValidateLifetime = false,
                ValidateAudience = false,
                ValidateIssuer = false,
                ValidateActor = false
            });
            if (!validateToken.IsValid)
            {
                logger.LogInformation("Invalid token");
                return false;
            }

            var expectedBodySha256 = validateToken.Claims.First(c => c.Key == "request_body_sha256").Value.ToString();
            string actualBodySha256;
            using (var hash = SHA256.Create())
            {
                var byteArray = hash.ComputeHash(Encoding.UTF8.GetBytes(body));
                actualBodySha256 = Convert.ToHexString(byteArray).ToLower();
            }

            if (expectedBodySha256 != actualBodySha256)
            {
                logger.LogInformation("Body SHA256 does not match");
                return false;
            }

            logger.LogInformation("Plaid web hook verified successfully");
            return true;
        }
    }
}
