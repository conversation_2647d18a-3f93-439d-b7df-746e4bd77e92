﻿using Brands.DAL;
using Brands.DataModels;
using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Services.Auth;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/banner")]
    [ApiController]
    public class BannerController : ControllerBase
    {
        private readonly EPDATAContext ePDATAContext;
        private readonly ILogger<BannerController> logger;
        private readonly UserManager<BrandsAuthUser> userManager;
        private readonly UserEventService userEventService;

        public BannerController(EPDATAContext ePDATAContext, ILogger<BannerController> logger, UserManager<BrandsAuthUser> userManager, UserEventService userEventService)
        {
            this.ePDATAContext = ePDATAContext;
            this.logger = logger;
            this.userManager = userManager;
            this.userEventService = userEventService;
        }

        [HttpGet("active")]
        [Authorize]
        public async Task<IActionResult> GetActiveBanner(bool forceShowLastBanner = false)
        {
            try
            {
                logger.LogDebug("Entering GetActiveBanner");

                BrandsAuthUser user = await userManager.FindByIdAsync(HttpContext.User.GetPaydeckUserId().ToString()).ConfigureAwait(false);
                var viewingIndicator = user.DismissedBannerId - Math.Truncate(user.DismissedBannerId.GetValueOrDefault());

                var banner = await ePDATAContext.PaydeckBanners
                    .Where(b => b.StartDate < DateTime.Now && b.EndDate > DateTime.Now)
                    .Where(b => b.BannerType != "Dashboard")
                    .Where(b => !user.DismissedBannerId.HasValue || (user.DismissedBannerId.HasValue && b.Id >= (int?)user.DismissedBannerId))
                    .Where(b => forceShowLastBanner || (viewingIndicator == null || b.Id > (int?)user.DismissedBannerId || viewingIndicator < 0.3m))
                    .Where(b => !b.CoFilter.HasValue || b.CoFilter == HttpContext.User.GetConum())
                    .OrderBy(b => b.StartDate)
                    .FirstOrDefaultAsync()
                    .ConfigureAwait(false);

                if (banner != null)
                {
                    logger.LogDebug("Matched banner {BannerId}, DismissedBannerId: {DismissedBannerId}", banner.Id, user.DismissedBannerId);
                    if ((int?)user.DismissedBannerId == banner.Id)
                    {
                        user.DismissedBannerId += 0.1m;
                    }
                    else
                    {
                        user.DismissedBannerId = banner.Id;
                    }
                    logger.LogDebug("Updating DismissedBannerId to: {DismissedBannerId}", user.DismissedBannerId);
                    userEventService.AddUserEvent(user, "VIEWED_BANNER", true, null, $"Id: {user.DismissedBannerId}");
                    var results = await userManager.UpdateAsync(user).ConfigureAwait(false);
                }
                else
                {
                    logger.LogDebug("no banner founb. DismissedBannerId: {DismissedBannerId}", user.DismissedBannerId);
                }

                return Ok(banner);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetActiveBanner");
                return BadRequest();
            }
        }

        [HttpGet("dashboard")]
        [Authorize]
        public async Task<IActionResult> GetDashboardBanner()
        {
            try
            {
                logger.LogDebug("Entering GetDashboardBanner");

                BrandsAuthUser user = await userManager.FindByIdAsync(HttpContext.User.GetUserId()).ConfigureAwait(false);
               
                var banner = await ePDATAContext.PaydeckBanners
                 .Where(b=>b.BannerType == "Dashboard")
                 .Where(b => b.StartDate < DateTime.Now && b.EndDate > DateTime.Now)
                 .Where(b => !b.CoFilter.HasValue || b.CoFilter == HttpContext.User.GetConum())
                 .OrderBy(b => b.StartDate)
                 .FirstOrDefaultAsync()
                 .ConfigureAwait(false);

                if (banner is null)
                {
                    logger.LogDebug("No dashboard banner was found");
                }

                return Ok(banner);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetDashboardBanner");
                return BadRequest();
            }
        }
    }
}
