﻿using Brands.DataModels;
using BrandsWebApp.Authentication;
using BrandsWebApp.Exceptions;
using BrandsWebApp.Hubs;
using BrandsWebApp.Models;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.PowerImports;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Auth;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using QueueBuilder;
using System;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/PowerImports")]
    [ApiController]
    public class PowerImportsController : ControllerBase
    {
        private readonly ILogger<PowerImportsController> logger;
        private readonly PowerImportsService powerImportsService;
        private readonly ReportQueuePathCredentials reportQueueCreds;
        private readonly UserEventService userEventService;
        private readonly UserManager<BrandsAuthUser> userManager;
        private readonly IConfiguration configuration;
        private readonly QueueBuilder.QueueBuilder queueBuilder;
        private const string eventType = "UPLOAD_POWER_IMPORT";

        public PowerImportsController(
            ILogger<PowerImportsController> logger,
            PowerImportsService powerImportsService,
            IOptionsSnapshot<ReportQueuePathCredentials> reportQueueCreds,
            UserEventService userEventService,
            UserManager<BrandsAuthUser> userManager,
            IConfiguration configuration,
            QueueBuilder.QueueBuilder queueBuilder)
        {
            this.logger = logger;
            this.powerImportsService = powerImportsService;
            this.reportQueueCreds = reportQueueCreds.Value;
            this.userEventService = userEventService;
            this.userManager = userManager;
            this.configuration = configuration;
            this.queueBuilder = queueBuilder;
        }


        [Authorize(Policy = nameof(AuthPolicies.PaydeckPayrollOrPowerImports))]
        [HttpGet("Templates")]
        public async Task<IActionResult> GetTemplatesAsync()
        {
            var result = await powerImportsService.GetTemplatesAsync(User.GetConum());
            return Ok(result);
        }

        [Authorize(Policy = nameof(AuthPolicies.PaydeckPayrollOrPowerImports))]
        [HttpGet("Templates/SilverGrid")]
        public async Task<IActionResult> GetSilverGridTemplateAsync()
        {
            var result = await powerImportsService.GetSilverGridTemplateAsync(User.GetConum());
            return Ok(result);
        }

        [Authorize(Policy = AuthPolicies.PaydeckPayrollOrPowerImports)]
        [HttpGet]
        public async Task<IActionResult> GetImportsAsync()
        {
            var imports = await powerImportsService.GetImportsAsync(User.GetConum());
            return Ok(imports);
        }

        [Authorize(Policy = AuthPolicies.PaydeckPayrollOrPowerImports)]
        [HttpGet("{importId}/Errors")]
        public async Task<IActionResult> GetImportErrorsAsync([FromRoute] int importId)
        {
            var errors = await powerImportsService.GetImportErrorsAsync(importId);
            var lineErrors = await powerImportsService.GetImportLineErrorsAsync(importId);

            return Ok(new
            {
                Errors = errors,
                LineErrors = lineErrors
            });
        }

        [Authorize(Policy = AuthPolicies.PaydeckPayrollOrPowerImports)]
        [HttpGet("{importId}/Results")]
        public async Task<IActionResult> GetImportResultsAsync([FromRoute] int importId)
        {
            var results = await powerImportsService.GetImportResultsAsync(importId);
            return Ok(results);
        }

        [Authorize(Policy = nameof(AuthPolicies.PaydeckPayrollOrPowerImports))]
        [HttpPost("Complete/{importId}")]
        public async Task<IActionResult> CompleteImportAsync([FromRoute] int importId)
        {
            var user = await userManager.FindByIdAsync(User.GetPaydeckUserId().ToString());
            var eventType = "COMPLETE_POWER_IMPORT";

            try
            {
                await powerImportsService.CompleteImportAsync(User.GetConum(), importId);

                queueBuilder.WithConum(HttpContext.User.GetConum())
                    .WithDescription("PP Import")
                    .WithRequesterInfo(HttpContext.User.GetEmail(), "PPX_WebUser", HttpContext.User.GetEmpConum(), HttpContext.User.GetEmpnum());
                queueBuilder.EnqueuePPImport(importId, null);
                await queueBuilder.SaveAndPublishAsync();

                await userEventService.SaveUserEvent(user, eventType, true, User.GetConum());
            }
            catch (DatabaseRecordNotFoundException e)
            {
                logger.LogWarning(e.Message);
                await userEventService.SaveUserEvent(user, eventType, false, User.GetConum(), e.Message);
                return NotFound(e.Message);
            }
            catch (InvalidOperationException e)
            {
                logger.LogWarning(e.Message);
                await userEventService.SaveUserEvent(user, eventType, false, User.GetConum(), e.Message);
                return BadRequest(e.Message);
            }

            return NoContent();
        }

        [Authorize(Policy = nameof(AuthPolicies.PaydeckPayrollOrPowerImports))]
        [HttpGet("{importId}")]
        public async Task<IActionResult> DownloadImportAsync([FromRoute] int importId)
        {
            logger.LogDebug("Entering DownloadPowerImportAsync.");

            try
            {
                var fileName = await powerImportsService.GetImportFilePathAsync(importId);

                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Add("ApiKey", reportQueueCreds.ReportsUrlApiKey ?? throw new ArgumentNullException("ReportsUrlApiKey", "ReportsUrlApiKey is null"));
                    string powerImportsUrl = configuration.GetValue<string>("PowerImportsUrl");
                    var result = await client.GetStreamAsync($"{powerImportsUrl}/{fileName}");

                    string contentType;
                    new FileExtensionContentTypeProvider().TryGetContentType(fileName, out contentType);
                    return File(result, contentType);
                }
            }
            catch (DatabaseRecordNotFoundException e)
            {
                logger.LogWarning(e.Message);
                return NotFound(e.Message);
            }
        }

        [Authorize(Policy = nameof(AuthPolicies.PaydeckPayrollOrPowerImports))]
        [HttpPost]
        public async Task<IActionResult> UploadImportAsync([FromForm] AddPowerImport powerImport)
        {
            logger.LogDebug("Entering UploadImportAsync. {@powerImport}", powerImport);
            string result;
            BrandsAuthUser user = await userManager.FindByIdAsync(User.GetPaydeckUserId().ToString());

            string powerImportsUrl = configuration.GetValue<string>("PowerImportsUrl");
            var apiKey = reportQueueCreds.ReportsUrlApiKey ?? throw new ArgumentNullException("ReportsUrlApiKey", "ReportsUrlApiKey is null");
            logger.LogDebug("PowerImportsUrl: {PowerImportsUrl} {ApiKey}", powerImportsUrl, apiKey);
            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("ApiKey", apiKey);
                var response = await client.PostAsync(powerImportsUrl, new MultipartFormDataContent
                {
                    {
                        new StreamContent(powerImport.FormFile.OpenReadStream()),
                        "powerImport",
                        powerImport.FormFile.FileName
                    }
                });
                result = await response.Content.ReadAsStringAsync();
                logger.LogDebug("PowerImportsApi Response. {result} {StatusCode}", result, response.StatusCode);

                if (!response.IsSuccessStatusCode)
                {
                    await userEventService.SaveUserEvent(user, eventType, false, User.GetConum(), result);
                    return StatusCode((int)response.StatusCode, result);
                }
            }

            try
            {
                await powerImportsService.AddImportAsync(new PowerImportsService.AddPowerImport(powerImport.ImportTemplateId, powerImport.ImportTemplateCategory, User.GetConum(), powerImport.PowerGridName, result));
            }
            catch (Exception e)
            {
                logger.LogError(e, "Error in UploadImportAsync");
                await userEventService.SaveUserEvent(user, eventType, false, User.GetConum(), e.Message);
                return BadRequest();
            }
            await userEventService.SaveUserEvent(user, eventType, true, User.GetConum());
            return NoContent();
        }

        [Authorize(Policy = AuthPolicies.PaydeckPayrollOrPowerImports)]
        [HttpDelete("{importId}")]
        public async Task<IActionResult> DeleteImportAsync([FromRoute] int importId)
        {
            logger.LogDebug("Entering DeleteImportAsync.");
            var user = await userManager.FindByIdAsync(User.GetPaydeckUserId().ToString());
            var eventType = "DELETE_POWER_IMPORT";

            try
            {
                await powerImportsService.DeleteImportAsync(User.GetConum(), importId, User.GetPaydeckUserId());
                await userEventService.SaveUserEvent(user, eventType, true, User.GetConum());
                return NoContent();
            }
            catch (DatabaseRecordNotFoundException e)
            {
                logger.LogError(e, "Error in DeleteImportAsync");
                await userEventService.SaveUserEvent(user, eventType, false, User.GetConum(), e.Message);
                return NotFound(e.Message);
            }
            catch (InvalidOperationException e)
            {
                logger.LogError(e, "Error in DeleteImportAsync");
                await userEventService.SaveUserEvent(user, eventType, false, User.GetConum(), e.Message);
                return BadRequest(e.Message);
            }
        }

        [HttpPost("{importSessionId}/Updates")]
        [AllowAnonymous]
        public async Task<IActionResult> ImportUpdate([FromRoute] int importSessionId, [FromBody] PowerImportStatus status, [FromServices] IHubContext<MainHub> mainHubContext)
        {
            logger.LogDebug("Entering ImportUpdate. {importSessionId}", importSessionId);
            if (!HttpContext.Request.Headers.TryGetValue("secret_key", out var key) || !key.All(v => v == "af25afs14365IOUIHUINJMNQSLDFJsaf465afs465a54s231as321654f8465fds231zsdfasFDSD2543fs65a4312s213af465a65d4sas"))
            {
                logger.LogWarning("Unauthorized in ImportUpdate");
                return Unauthorized();
            }

            var importSession = await powerImportsService.GetImportSessionAsync(importSessionId);
            logger.LogDebug("Entering ImportUpdate. {importSessionId} Co#: {conum} Status: {Status} InQueue: {InQueue}", importSessionId, importSession.CoNum, importSession.Status, importSession.InQueue);
            if (importSession.Status != status.Status || importSession.InQueue != status.InQueue)
            {
                logger.LogWarning("SingalR status & table mismatch. SignalR: {SRstatus} {SRInQueue} Table: {TblStatus} {TblInQueue}", status.Status, status.InQueue, importSession.Status, importSession.InQueue);
            }
            var connections = MainHub.Connections.Values.Where(c => decimal.TryParse(c.Conum, out decimal conumInt) && conumInt == importSession.CoNum);
            logger.LogDebug("There's {count} connections matching Co#: {conum} Status: {Status} InQueue: {InQueue}", connections.Count(), importSession.CoNum, importSession.Status, importSession.InQueue);

            object payload = null;
            if (importSession.Download)
            {
                payload = importSession;
            }
            else
            {
                var import = await powerImportsService.GetImportsAsync(importSession.CoNum, importSessionId);
                if (import?.Any() == false)
                {
                    logger.LogWarning("No imports found matching {CoNum} {ImportTemplateId} {Category}", importSession.CoNum, importSession.ImportTemplateId, importSession.Category);
                }
                else if (import.Count() > 1)
                {
                    logger.LogWarning("Multiple import templates found matching {CoNum} {ImportTemplateId} {Category}", importSession.CoNum, importSession.ImportTemplateId, importSession.Category);
                }
                else
                {
                    payload = import.Single();
                }
            }

            if (payload != null)
            {
                foreach (var connection in connections)
                {
                    try
                    {
                        logger.LogDebug("Pushing signalR [PowerImportUpdate] Update to {ConnectionId} {conum} {UserName} {UserId}", connection.ConnectionId, importSession.CoNum, connection.UserName, connection.UserId);
                        await mainHubContext.Clients.Client(connection.ConnectionId).SendAsync("PowerImportUpdate", payload);
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Error in ImportUpdate. {conum} {ConnectionId}", importSession.CoNum, connection.ConnectionId);
                        throw;
                    }
                }
            }

            return NoContent();
        }

        [Authorize(Policy = nameof(AuthPolicies.PaydeckPayrollOrPowerImports))]
        [HttpGet("download/{id}")]
        public async Task<IActionResult> DownloadQbSilverGrid([FromRoute] int id)
        {
            logger.LogDebug("Entering DownloadQbReport. {conum} {id}", User.GetConum(), id);
            try
            {
                var extractLog = await powerImportsService.GetQbExtractLogById(id);
                if (extractLog == null)
                {
                    logger.LogWarning("DownloadQbSilverGrid Id: {id} not found", id);
                    return NotFound();
                }
                else if (extractLog.CoNum != User.GetConum())
                {
                    logger.LogWarning("MISMATCH CONUM. DownloadQbSilverGrid fileId: {id} Route Conum: {conum} File Conum {FileConum}", id, User.GetConum(), extractLog.CoNum);
                    return NotFound();
                }

                string qbReportsUrl = configuration.GetValue<string>("QbReportsUrl");
                var apiKey = reportQueueCreds.ReportsUrlApiKey ?? throw new ArgumentNullException("ReportsUrlApiKey", "ReportsUrlApiKey is null");
                logger.LogDebug("QbReportsUrl: {QbReportsUrl} {ApiKey}", qbReportsUrl, apiKey);
                string fileName = System.IO.Path.GetFileName(extractLog.FileName);
                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Add("ApiKey", apiKey);
                    var result = await client.GetStreamAsync($"{qbReportsUrl}/{fileName}");
                    logger.LogDebug("PowerImportsApi Response. {result}", result);
                    new FileExtensionContentTypeProvider().TryGetContentType(fileName, out string contentType);
                    this.Response.Headers.Add("X-Filename", fileName);
                    return File(result, contentType ?? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=utf-8", fileDownloadName: fileName);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in DownloadQbReport");
                throw;
            }
        }

        [Authorize(Policy = nameof(AuthPolicies.PaydeckPayrollOrPowerImports))]
        [HttpPost("GenerateAndDownloadFile")]
        public async Task<IActionResult> GenerateAndDownloadFile(int importTemplateId, string category)
        {
            logger.LogDebug("Entering GenerateAndDownloadFile. {importTemplateId} {category}", importTemplateId, category);
            try
            {
                await powerImportsService.AddImportAsync(new PowerImportsService.AddPowerImport(importTemplateId, category, User.GetConum(), null, null, true));
                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GenerateAndDownloadFile");
                return BadRequest();
            }
        }
    }

    public record PowerImportStatus(string Status, bool InQueue);
}
