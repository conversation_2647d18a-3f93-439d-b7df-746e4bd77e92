﻿using Brands.DAL;
using Brands.DataModels;
using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.Employee;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Validation.EmployeeValidation;
using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Employee
{
    [ApiController]
    [Route("api/employees")]
    [Authorize(Policy = AuthPolicies.ManageEmployeesOrPaydeckPayroll)]
    public class EmployeeController : ControllerBase
    {
        private readonly EPDATAContext ePDATAContext;
        private readonly ILogger<EmployeeController> logger;
        private readonly UserManager<BrandsAuthUser> userManager;
        private readonly EmployeeService employeeService;
        private readonly StatesService statesService;

        public EmployeeController(
            EPDATAContext ePDATAContext,
            ILogger<EmployeeController> logger,
            UserManager<BrandsAuthUser> userManager,
            EmployeeService employeeService,
            StatesService statesService)
        {
            this.ePDATAContext = ePDATAContext;
            this.logger = logger;
            this.userManager = userManager;
            this.employeeService = employeeService;
            this.statesService = statesService;
        }

        [HttpPost]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<EmployeeList>> GetEmployees([FromBody] EmployeeListFilter employeeListFilter, [FromServices] EmployeeListFilterValidation validator)
        {
            var validationResult = await validator.ValidateAsync(employeeListFilter);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            var employeeList = await employeeService.GetEmployeeListAsync(User.GetConum(), User.GetPaydeckUserId(), employeeListFilter);
            return Ok(employeeList);
        }

        [HttpGet("Grid")]
        public async Task<IActionResult> GetEmployeesGridAsync([FromQuery] decimal? conum)
        {
            if (conum.HasValue && conum.Value != User.GetConum())
            {
                logger.LogWarning("User with {userId} not allowed to access employees of company {conum}", User.GetPaydeckUserId(), conum);
                return StatusCode(403, "Access to employees of selected company is not allowed");
            }

            var loadOptions = new DataSourceLoadOptionsBase();
            DataSourceLoadOptionsParser.Parse(loadOptions, key =>
                Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);
            loadOptions.PrimaryKey = new[] { "Conum", "Empnum" };
            loadOptions.PaginateViaPrimaryKey = true;

            ePDATAContext.ChangeTracker.LazyLoadingEnabled = true;
            ePDATAContext.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

            var employees = ePDATAContext.Employees
                .Include(e => e.Division)
                .Include(e => e.Department_T)
                .Include(e => e.Company)
                .ThenInclude(c => c.CoOptionPayroll)
                .AsQueryable();

            if (conum.HasValue)
            {
                employees = employees.Where(e => e.Conum == conum);
            }
            else
            {
                var userConums = (await userManager.FindByIdAsync(User.GetPaydeckUserId().ToString())).BrandsUserEmployees
                    .Where(ue => ue.IsActive)
                    .SelectMany(ue => ue.BrandsAuthUserClaims)
                    .Where(uc => uc.ClaimValue == Permission.ManageEmployees)
                    .Select(uc => uc.Conum);
                employees = employees.Where(e => userConums.Contains(e.Conum));
            }

            var result = employees.Select(e => new EmployeeListItem
            {
                Empnum = e.Empnum,
                FirstName = e.FirstName,
                MiddleName = e.MiddleName,
                LastName = e.LastName,
                Conum = e.Conum,
                CompanyName = (e.Company.CoOptionPayroll != null && e.Company.CoOptionPayroll.UseDbaInCompanySelection) == true ? e.Company.CoDba : e.Company.CoName,
                StartDate = e.StartDate,
                Division = e.DivNum + " - " + e.Division.DDIVNAME,
                DivNum = e.DivNum,
                Department = e.DeptNum + " - " + e.Department_T.DEPT_DESC,
                DeptNum = e.DeptNum,
                PhoneNumber = e.PhoneNumber,
                UserEmail = e.UserEmail,
                TermDate = e.TermDate,
                EmpType = e.EmpType,
            });

            return Ok(await DataSourceLoader.LoadAsync(result, loadOptions));
        }

        [HttpGet("fields")]
        public async Task<IActionResult> GetEmployeeFieldNamesAsync()
        {
            logger.LogDebug("Entering GetEmployeeFieldNamesAsync");
            var filterNames = await employeeService.GetEmployeeFieldNames(User.GetConum());
            return Ok(filterNames);
        }

        [HttpGet("DivisionsAndDepartments")]
        public async Task<IActionResult> GetDivisionsAndDepartments([FromQuery] string filter)
        {
            var result = await employeeService.GetDivisionsAndDepartments(User.GetConum(), filter);
            return Ok(result);
        }

        [HttpGet("Divisions")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<List<Division>>> GetDivisions()
        {
            logger.LogDebug("Entering GetDivisions");
            try
            {
                var divisions = await ePDATAContext.Divisions
                    .Where(d => d.CONUM == User.GetConum())
                    .Select(d => new Division
                    {
                        Value = d.DDIVNUM,
                        Label = $"{d.DDIVNUM} - {d.DDIVNAME}"
                    })
                    .ToListAsync();
                var deps = await ePDATAContext.Department_Ts.Where(d => d.CONUM == User.GetConum() && d.DPACTIVE == "YES").ToListAsync();
                foreach (var div in divisions)
                {
                    div.Departments = deps.Where(d => d.DIVNUMD == div.Value).Select(d => EmployeeDropDown.Create(d.DEPTNUM, $"{d.DEPTNUM} - {d.DEPT_DESC}")).ToList();
                }
                return Ok(divisions);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetDivisions");
                throw;
            }
        }

        [HttpGet("Departments")]
        public async Task<IActionResult> GetDepartments([FromQuery] int? divisionNumber)
        {
            logger.LogDebug("Entering GetDepartments. divisionNumber: {divisionNumber}", divisionNumber);
            try
            {
                var departments = await ePDATAContext.Department_Ts
                    .Where(d => d.CONUM == User.GetConum() && (divisionNumber == null || d.DIVNUMD == divisionNumber) && d.DPACTIVE == "YES")
                    .Select(d => EmployeeDropDown.Create(d.DEPTNUM, $"{d.DEPTNUM} - {d.DEPT_DESC}"))
                    .ToListAsync();
                return Ok(departments);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetDepartments");
                throw;
            }
        }

        [HttpGet("EmployeePersonalInfo/Options")]
        public async Task<IActionResult> GetEmployeePersonalInfoDropdowns()
        {
            try
            {
                logger.LogDebug("Entering GetEmployeePersonalInfoDropdowns");
                var response = new
                {
                    Genders = new Dictionary<int, string>() { { 0, "Male" }, { 1, "Female" }, { 3, "UnSpecified" } },
                    States = await ePDATAContext.Zips.Select(z => z.State).Distinct().OrderBy(s => s).ToListAsync(),
                    EmployeeType = new[] { "Regular", "Contract", "Household", "Agriculture" },
                    FederalStatus = new[] { "Single", "Married", "Head of Household" },
                    WorkAndResidenceStates = await statesService.GetAvailableStatesAsync(User.GetConum()),
                    UnemploymentStates = await statesService.GetAvailableStatesAsync(User.GetConum(), true),
                    W4Version = new Dictionary<int, string>() { { 0, "Pre-2020 (old version)" }, { 1, "2020 (new version)" } },
                    EmployeeFieldNames = await employeeService.GetEmployeeFieldNames(User.GetConum()),
                    SmallestAvailableEmployeeNumber = await GetSmallestAvailableEmployeeNumberAsync(),
                    Salutations = await employeeService.GetEmployeeSalutations(User.GetConum()),
                };
                return Ok(response);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetEmployeePersonalInfoDropdowns");
                throw;
            }
        }

        [HttpGet("EmployeeJob/Options")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<EmployeeJobDropdowns>> GetEmployeeJobDropdowns()
        {
            logger.LogDebug("Entering GetEmployeeJobDropdowns");
            try
            {
                var response = new EmployeeJobDropdowns
                {
                    Divisions = await ePDATAContext.Divisions.Where(d => d.CONUM == User.GetConum()).Select(d => EmployeeDropDown.Create(d.DDIVNUM, $"{d.DDIVNUM} - {d.DDIVNAME}")).ToListAsync(),
                    Positions = await ePDATAContext.Employees.Where(e => e.Conum == User.GetConum() && e.Position != null).Select(d => d.Position).Distinct().ToListAsync(),
                    Jobs1 = await ePDATAContext.CoJobs.Where(j => j.Conum == User.GetConum() && j.active == "YES").Select(j => EmployeeDropDown.Create(j.JobId, $"{j.JobId} - {j.JobDescr}")).ToListAsync(),
                    Job2 = await ePDATAContext.Jobs2.Where(j => j.Conum == User.GetConum() && j.ActiveJob == "YES").Select(j => EmployeeDropDown.Create(j.JobCode, $"{j.JobCode} - {j.JobDesc}")).ToListAsync(),
                    Job3 = await ePDATAContext.Jobs3.Where(j => j.Conum == User.GetConum() && j.ActiveJob == "YES").Select(j => EmployeeDropDown.Create(j.JobCode, $"{j.JobCode} - {j.JobDesc}")).ToListAsync(),
                    Job4 = await ePDATAContext.Jobs4.Where(j => j.Conum == User.GetConum() && j.ActiveJob == "YES").Select(j => EmployeeDropDown.Create(j.JobCode, $"{j.JobCode} - {j.JobDesc}")).ToListAsync(),
                    Job5 = await ePDATAContext.Jobs5.Where(j => j.Conum == User.GetConum() && j.ActiveJob == "YES").Select(j => EmployeeDropDown.Create(j.JobCode, $"{j.JobCode} - {j.JobDesc}")).ToListAsync(),
                    ManagedBy = await ePDATAContext.Employees
                        .Where(e => e.Conum == User.GetConum() && !e.TermDate.HasValue)
                        .Select(e => EmployeeDropDown.Create(e.Empnum, $"{e.FirstName} {e.LastName}"))
                        .ToListAsync(),
                    WorkmansComp = await ePDATAContext.WorkmansComps.Where(w => w.Conum == User.GetConum()).Select(w => w.WCode).Distinct().ToListAsync()
                };
                return Ok(response);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error GetEmployeeJobDropdowns");
                throw;
            }
        }

        [HttpGet("EmployeePay/Options")]
        public IActionResult GetEmployeePayDropdowns()
        {
            logger.LogDebug("Entering GetEmployeePayDropdowns");
            try
            {
                var response = new
                {
                    PayFrequencies = Models.Constants.PAY_FREQUENCIES,
                    AcaStatuses = Models.Constants.ACA_STATUSES,
                    PayStatuses = Models.Constants.PAY_STATUSES
                };
                return Ok(response);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error GetEmployeePayDropdowns");
                throw;
            }
        }

        private async Task<int> GetSmallestAvailableEmployeeNumberAsync()
        {
            var employeeNumber = await ePDATAContext.Employees
                .Where(e => e.Conum == User.GetConum())
                .Select(e => e.Empnum)
                .OrderByDescending(n => n)
                .FirstOrDefaultAsync();

            if (employeeNumber == 0)
            {
                return 1;
            }

            return (int)employeeNumber + 1;
        }
    }
}
