﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Plaid
{
    [Route("api/BankAccounts")]
    [ApiController]
    [Authorize(Policy = nameof(AuthPolicies.OnboardingOrPayroll))]
    public class BankAccountsController : ControllerBase
    {
        private readonly PlaidService plaidService;

        public BankAccountsController(PlaidService plaidService)
        {
            this.plaidService = plaidService;
        }

        /// <summary>
        /// Gets bank accounts
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetBankAccounts([FromQuery] bool listByDivision)
        {
            if (listByDivision)
            {
                var accountsByDivision = await plaidService.GetAccountsByDivisionAsync(User.GetConum());
                return Ok(accountsByDivision);
            }

            var accounts = await plaidService.GetAccountsAsync(User.GetConum());
            return Ok(accounts);
        }

        // Future release
        //[HttpPost("Primary/{plaidAuthId}")]
        //public async Task<IActionResult> SetPrimaryBankAccount([FromRoute] int plaidAuthId)
        //{
        //    await plaidService.SetPrimaryAccountAsync(User.GetConum(), plaidAuthId);
        //    return NoContent();
        //}

        /// <summary>
        /// Deletes bank account
        /// </summary>
        [HttpDelete("{plaidAuthId}")]
        public async Task<IActionResult> DeleteBankAccount([FromRoute] int plaidAuthId)
        {
            var result = await plaidService.DeleteAccountAsync(User.GetConum(), plaidAuthId);
            return Ok(result);
        }

        /// <summary>
        /// Dismisses connect bank account reminder
        /// </summary>
        [HttpPost("Reminders/Dismissed")]
        public async Task<IActionResult> DismissConnectBankAccountReminder()
        {
            await plaidService.DismissConnectBankAccountReminder(User.GetPaydeckUserId());
            return NoContent();
        }
    }
}
