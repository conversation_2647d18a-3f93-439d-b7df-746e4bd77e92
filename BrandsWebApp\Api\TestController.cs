﻿using BrandsWebApp.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Sentry;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/test")]
    [ApiController]
    public class TestController : ControllerBase
    {
        private readonly ExecuPayApiClient execuPayApiClient;
        private readonly ILogger<TestController> logger;

        public TestController(ExecuPayApiClient execuPayApiClient, ILogger<TestController> logger)
        {
            this.execuPayApiClient = execuPayApiClient;
            this.logger = logger;
        }

        [HttpGet("test")]
        public async Task<IActionResult> TestException([FromQuery] Test test)
        {
            return Ok();
        }

        [HttpGet("sentry")]
        public IActionResult TestSentry()
        {
            // Test Serilog -> Sentry integration
            logger.LogWarning("This is a test warning message that should appear in Sentry");
            logger.LogError("This is a test error message that should appear in Sentry");

            // Test direct Sentry calls
            SentrySdk.CaptureMessage("Direct Sentry message from TestController", SentryLevel.Info);

            try
            {
                throw new InvalidOperationException("Test exception for Sentry integration");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Test exception logged through Serilog -> should appear in Sentry");
                SentrySdk.CaptureException(ex);
            }

            return Ok(new { message = "Sentry test messages sent. Check your Sentry dashboard." });
        }

        public class Test
        {
            public int Id { get; set; }
            public string Yes { get; set; }
        }
    }
}
