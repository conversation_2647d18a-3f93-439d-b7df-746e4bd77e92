﻿using BrandsWebApp.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/test")]
    [ApiController]
    public class TestController : ControllerBase
    {
        private readonly ExecuPayApiClient execuPayApiClient;

        public TestController(ExecuPayApiClient execuPayApiClient)
        {
            this.execuPayApiClient = execuPayApiClient;
        }

        [HttpGet("test")]
        public async Task<IActionResult> TestException([FromQuery] Test test)
        {
            return Ok();
        }

        public class Test
        {
            public int Id { get; set; }
            public string Yes { get; set; }
        }
    }
}
