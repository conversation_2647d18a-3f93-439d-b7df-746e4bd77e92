﻿using Brands.DAL;
using BrandsWebApp.Models.Auth;
using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Brands.DataModels;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.InternalAdmin
{
    [Route("api/Internal/PayrollsInProcessMsgTypes")]
    [ApiController]
    [Authorize(Policy = AuthPolicies.InternalPayrollAccess)]
    public class PayrollsInProcessMsgTypesController : ControllerBase
    {
        private readonly EPDATAContext ePDATAContext;

        public PayrollsInProcessMsgTypesController(EPDATAContext ePDATAContext)
        {
            this.ePDATAContext = ePDATAContext;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var loadOptions = new DataSourceLoadOptionsBase
            {
                PrimaryKey = new[] { "MsgType" },
                PaginateViaPrimaryKey = true
            };
            DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);

            var data = ePDATAContext.PayrollsInProcessMsgTypes.AsQueryable();
            var result = await DataSourceLoader.LoadAsync(data, loadOptions);
            return Ok(result);
        }

        [HttpPost]
        public async Task<IActionResult> CreatePriceGroup([FromForm] string key, [FromForm] string values)
        {
            var payrollsInProcessMsgTypes = new PayrollsInProcessMsgType();
            JsonConvert.PopulateObject(values, payrollsInProcessMsgTypes);

            ePDATAContext.PayrollsInProcessMsgTypes.Add(payrollsInProcessMsgTypes);
            await ePDATAContext.SaveChangesAsync();
            return Ok(payrollsInProcessMsgTypes);
        }

        [HttpPut]
        public async Task<IActionResult> UpdatePriceGroup([FromForm] string key, [FromForm] string values)
        {
            var payrollsInProcessMsgTypes = await ePDATAContext.PayrollsInProcessMsgTypes.SingleAsync(r => r.MsgType == key);
            JsonConvert.PopulateObject(values, payrollsInProcessMsgTypes);
            await ePDATAContext.SaveChangesAsync();
            return Ok(payrollsInProcessMsgTypes);
        }

    }
}
