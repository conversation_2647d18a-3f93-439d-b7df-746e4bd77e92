﻿using Brands.DataModels;
using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Auth;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Linq;
using System.Threading.Tasks;
using Brands.DAL;

namespace BrandsWebApp.Api
{
    [Route("api/Zendesk")]
    [ApiController]
    [Authorize]
    public class ZendeskController : ControllerBase
    {
        private readonly ILogger<ZendeskController> logger;
        private readonly JwtFactory jwtFactory;
        private readonly UserManager<BrandsAuthUser> userManager;
        private readonly EPDATAContext dataContext;

        public ZendeskController(
            ILogger<ZendeskController> logger,
            JwtFactory jwtFactory,
            UserManager<BrandsAuthUser> userManager,
            EPDATAContext dataContext)
        {
            this.logger = logger;
            this.jwtFactory = jwtFactory;
            this.userManager = userManager;
            this.dataContext = dataContext;
        }

        [HttpGet("Token")]
        public async Task<ActionResult> GetToken()
        {
            logger.LogDebug("Entering GetToken.");

            string userType = User.GetOnboardingGuid() != null ? "Onboarding" : User.GetCpaFirmId() != null ? "CPA" : "Paydeck";
            BrandsAuthUser user = await userManager.FindByIdAsync(User.GetPaydeckUserId().ToString());
            BrandsUserEmployee activeEmployee = user.BrandsUserEmployees.FirstOrDefault(e => e.IsActive);
            Employee_T employee = activeEmployee != null ? await dataContext.Employees.SingleAsync(e => e.Conum == activeEmployee.EmpConum && e.Empnum == activeEmployee.Empnum) : null;
            Company_T company = await dataContext.Companies.FirstOrDefaultAsync(c => c.Conum == User.GetConum());
            var key = await dataContext.GetUdfValueAsync("Zendesk_Masaging_Secret_key");
            string name = $"{employee?.FirstName} {employee?.LastName}-{userType}-{company?.Conum}-{company?.CoName}";
            try
            {
                name = string.Format(await dataContext.GetUdfValueAsync("Zendesk Chat Name format"), employee?.FirstName, employee?.LastName, userType, company?.Conum, company?.CoName);
            }
            catch (System.Exception ex)
            {
                logger.LogError(ex, "Error constructing name for zendesk chat token");
            }
            string token = jwtFactory.IssueZendeskToken(key, user, name, User.GetProfileType(), User.GetProfileId());
            return Ok(token);
        }

    }
}
