﻿using BrandsWebApp.Models;
using BrandsWebApp.Models.Onboarding;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.ClientOnboarding
{
    public partial class OnboardingController
    {
        [HttpGet("calendly/scheduledcall")]
        public async Task<IActionResult> GetScheduledDate()
        {
            var onboardingDetails = await onboardingDataService.GetOnboardingData();
            CalendlyEventDetails eventDetails = await calendlyService.GetCalendlyEventDetailsAsync(onboardingDetails);

            return Ok(eventDetails);
        }

        [HttpGet("calendly/urls")]
        public IActionResult GetCalendlyUrls([FromServices] IOptionsSnapshot<CalendlyConfiguration> calendlyConfig)
        {
            return Ok(new
            {
                DiscoveryCall = calendlyConfig.Value.DiscoveryCall,
                OnboardingHelp = calendlyConfig.Value.OnboardingHelp
            });
        }
    }
}
