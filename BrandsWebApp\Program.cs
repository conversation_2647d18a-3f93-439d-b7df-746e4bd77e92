using Destructurama;
using DevExpress.Office.Utils;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.ApplicationInsights;
using Sentry;
using Serilog;
using Serilog.Exceptions;
using Serilog.Exceptions.Core;
using Serilog.Exceptions.Destructurers;
using Serilog.Exceptions.EntityFrameworkCore.Destructurers;
using Serilog.Exceptions.MsSqlServer.Destructurers;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Reflection;

namespace BrandsWebApp
{
    public class Program
    {
        public static Guid AppInstanceId = Guid.NewGuid();

        public static int Main(string[] args)
        {
            var destructuringOptions = new DestructuringOptionsBuilder()
                .WithDefaultDestructurers()
                .WithDestructurers(new List<IExceptionDestructurer>
                {
                    new DbUpdateExceptionDestructurer(),
                    new SqlExceptionDestructurer()
                });

            var logConfiguration = new LoggerConfiguration()
               .Destructure.JsonNetTypes()
               .Enrich.WithMachineName()
               .Enrich.WithThreadId()
               .Enrich.WithProcessId()
               .Enrich.WithProperty("AppInstanceId", AppInstanceId)
               .Enrich.WithProperty("AppID", "BrandsWebApp")
               .Enrich.WithProperty("Version", Version)
               .Enrich.FromLogContext()
               .Enrich.WithExceptionDetails(destructuringOptions)
               .MinimumLevel.Verbose()
               .WriteTo.Seq("http://appserver:5341", Serilog.Events.LogEventLevel.Verbose, apiKey: "0dOOt2t6fxVeYYb2xtqQ")
               .WriteTo.Console(Serilog.Events.LogEventLevel.Verbose);

            logConfiguration.MinimumLevel.Override("Microsoft", Serilog.Events.LogEventLevel.Warning);
            if (!System.Diagnostics.Debugger.IsAttached)
            {
            }

            if (Environment.GetEnvironmentVariable("SEQ_LOG_TO_AI") == "1")
            {
                logConfiguration.WriteTo.ApplicationInsights(TelemetryConfiguration.CreateDefault(), TelemetryConverter.Traces);
            }

            Serilog.Debugging.SelfLog.Enable(l =>
            {
                Debug.WriteLine(l);
                Console.WriteLine(l);
            });

            Log.Logger = logConfiguration.CreateLogger();

            try
            {
                Log.Information("Starting web host");
                CreateHostBuilder(args).Build().Run();
                return 0;
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Host terminated unexpectedly");
                return 1;
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host
            .CreateDefaultBuilder(args)
            .UseSerilog()
            .ConfigureWebHostDefaults(webBuilder =>
            {
                webBuilder.UseStartup<Startup>();
                webBuilder.UseSentry(c =>
                {
                    c.Debug = true;
                    c.Release = Version;
                });
            })
            .ConfigureLogging(logging =>
            {
                logging.AddApplicationInsights();
                logging.AddFilter<ApplicationInsightsLoggerProvider>("Microsoft", LogLevel.Warning);
            });

        public static string Version
        {
            get
            {
                var version = Assembly.GetEntryAssembly().GetCustomAttribute<AssemblyInformationalVersionAttribute>().InformationalVersion;
                return version.IsNullOrWhiteSpace() ? string.Empty : version.Split('+')[0];
            }
        }
    }
}