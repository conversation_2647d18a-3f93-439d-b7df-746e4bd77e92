﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Filters;
using BrandsWebApp.Models.Payroll;
using BrandsWebApp.Services.Payroll;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Payrolls
{
    [Route("api/Payrolls")]
    [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
    [ApiController]
    public class PayrollReportsController : ControllerBase
    {
        private readonly PayrollReportsService payrollReportsService;

        public PayrollReportsController(PayrollReportsService payrollReportsService)
        {
            this.payrollReportsService = payrollReportsService;
        }

        /// <summary>
        /// Gets all available summary reports
        /// </summary>
        [HttpGet("SummaryReports")]
        public async Task<IActionResult> GetSummaryReports()
        {
            var result = await payrollReportsService.GetAllAvailableSummaryReportsAsync(User.GetConum());
            return Ok(result);
        }

        /// <summary>
        /// Sets default payroll summary reports
        /// </summary>
        [HttpPost("{payrollNumber}/SummaryReports")]
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        public async Task<IActionResult> SetsPayrollSummaryReports([FromRoute] decimal payrollNumber, [FromBody] SetSummaryReports setSummaryReports)
        {
            setSummaryReports.Prnum = payrollNumber;
            var result = await payrollReportsService.SetSummaryReportsAsync(User.GetConum(), setSummaryReports);
            return Ok(result);
        }
    }
}
