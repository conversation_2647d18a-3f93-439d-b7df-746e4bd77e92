﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Payroll.EmployeeFilter;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Validation.EmployeeFilterValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Payrolls
{
    [Route("api/Payrolls/EmployeeFilters")]
    [ApiController]
    [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
    public class EmployeeFiltersController : ControllerBase
    {
        private readonly EmployeeFilterService employeeFilterService;

        public EmployeeFiltersController(EmployeeFilterService employeeFilterService)
        {
            this.employeeFilterService = employeeFilterService;
        }

        /// <summary>
        /// Gets employee filters
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetEmployeeFilters()
        {
            var filters = await employeeFilterService.GetEmployeeFiltersAsync(User.GetConum());
            return Ok(filters);
        }

        /// <summary>
        /// Gets employee filters options
        /// </summary>
        [HttpGet("Options")]
        public async Task<IActionResult> GetEmployeeFiltersOptions()
        {
            var options = await employeeFilterService.GetEmployeeFilterOptionsAsync(User.GetConum());
            return Ok(options);
        }

        /// <summary>
        /// Gets employee filter by ID
        /// </summary>
        [HttpGet("{filterId}")]
        public async Task<IActionResult> GetEmployeeFilter([FromRoute] int filterId)
        {
            var filter = await employeeFilterService.GetEmployeeFilterAsync(User.GetConum(), filterId);
            return Ok(filter);
        }

        /// <summary>
        /// Adds employee filter
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> AddEmployeeFilter([FromBody] UpsertEmployeeFilter employeeFilter, [FromServices] UpsertEmployeeFilterValidation validation)
        {
            var validationResult = await validation.ValidateAsync(employeeFilter);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            await employeeFilterService.AddEmployeeFilterAsync(User.GetConum(), employeeFilter, User.GetPaydeckUserId(), User.GetFullName());
            return NoContent();
        }

        /// <summary>
        /// Updates employee filter
        /// </summary>
        [HttpPut("{filterId}")]
        public async Task<IActionResult> UpdateEmployeeFilter([FromRoute] int filterId, [FromBody] UpsertEmployeeFilter employeeFilter, [FromServices] UpsertEmployeeFilterValidation validation)
        {
            var validationResult = await validation.ValidateAsync(employeeFilter);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            await employeeFilterService.UpdateEmployeeFilterAsync(filterId, employeeFilter);
            return NoContent();
        }

        /// <summary>
        /// Deletes employee filter
        /// </summary>
        [HttpDelete("{filterId}")]
        public async Task<IActionResult> DeleteEmployeeFilter([FromRoute] int filterId)
        {
            await employeeFilterService.DeleteEmployeeFilterAsync(filterId);
            return NoContent();
        }
    }
}
