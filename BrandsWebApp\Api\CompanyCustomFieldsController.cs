﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Job;
using BrandsWebApp.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [ApiController]
    [Route("api/CompanyCustomFields")]
    [Authorize(Policy = nameof(Permission.ManageContactsAndUsers))]
    public class CompanyCustomFieldsController : ControllerBase
    {
        private readonly CustomFieldsService customFieldsService;

        public CompanyCustomFieldsController(CustomFieldsService customFieldsService)
        {
            this.customFieldsService = customFieldsService;
        }

        /// <summary>
        /// Gets custom fields for current company
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetCustomFields()
        {
            var fields = await customFieldsService.GetCustomFieldsAsync(User.GetConum());
            return Ok(fields);
        }

        /// <summary>
        /// Gets custom field options
        /// </summary>
        [HttpGet("{customFieldId}/Options")]
        public async Task<IActionResult> GetCustomFieldOptions([FromRoute] string customFieldId)
        {
            var options = await customFieldsService.GetCustomFieldOptionsAsync(User.GetConum(), customFieldId);
            return Ok(options);
        }

        /// <summary>
        /// Updates custom field for current company
        /// </summary>
        [HttpPut("{customFieldId}")]
        public async Task<IActionResult> UpdateCustomField([FromRoute] string customFieldId, [FromBody] UpdateCustomField field)
        {
            field.Id = customFieldId;
            await customFieldsService.UpdateCustomFieldAsync(User.GetConum(), field);
            return NoContent();
        }
    }
}
