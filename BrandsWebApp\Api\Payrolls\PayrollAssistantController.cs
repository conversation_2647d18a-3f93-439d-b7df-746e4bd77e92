﻿using Brands.DataModels;
using BrandsWebApp.Authentication;
using BrandsWebApp.Filters;
using BrandsWebApp.Models;
using BrandsWebApp.Models.Payroll;
using BrandsWebApp.Models.PayrollApproval;
using BrandsWebApp.Models.RunReport;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Payroll;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RpcMessagingModels;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Payrolls
{
    [Route("api/PayrollAssistant")]
    [ApiController]
    [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
    public class PayrollAssistantController : ControllerBase
    {
        private readonly PayrollAssistantService payrollAssistantService;
        private readonly PayrollIssuesService payrollIssuesService;
        private readonly RunReportsService runReportsService;
        private readonly ILogger<PayrollAssistantController> logger;

        public PayrollAssistantController(
            PayrollAssistantService payrollAssistantService,
            PayrollIssuesService payrollIssuesService,
            RunReportsService runReportsService,
            ILogger<PayrollAssistantController> logger)
        {
            this.payrollAssistantService = payrollAssistantService;
            this.payrollIssuesService = payrollIssuesService;
            this.runReportsService = runReportsService;
            this.logger = logger;
        }

        /// <summary>
        /// Gets payroll entry issues
        /// </summary>
        [HttpGet("{payrollNumber}/PayrollEntryIssues")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<IEnumerable<PayrollEntryIssue>>> GetPayrollEntryIssues([FromRoute] decimal payrollNumber)
        {
            var issues = await payrollIssuesService.GetPayrollAssistantIssuesAsync(User.GetConum(), payrollNumber);
            return Ok(issues);
        }

        /// <summary>
        /// Approves check issue by type
        /// </summary>
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpPost("{payrollNumber}/Checks/{checkKey}/Issues/{issueType}")]
        public async Task<IActionResult> ApproveCheckIssue([FromRoute] decimal payrollNumber, [FromRoute] string checkKey, [FromRoute] string issueType)
        {
            var key = new CheckKey(checkKey);
            key.Prnum = payrollNumber;
            key.Conum = User.GetConum();
            var result = await payrollAssistantService.ApproveCheckIssueAsync(issueType, key.Prnum, key.Conum, key);
            return Ok(result);
        }

        /// <summary>
        /// Approves payroll issue by type
        /// </summary>
        [HttpPost("{payrollNumber}/Issues/{issueType}")]
        public async Task<IActionResult> ApprovePayrollIssue([FromRoute] int payrollNumber, [FromRoute] string issueType)
        {
            var updatedChecks = await payrollAssistantService.ApprovePayrollIssueAsync(issueType, payrollNumber, User.GetConum());
            return Ok(updatedChecks);
        }

        /// <summary>
        /// Updates check date
        /// </summary>
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpPut("{payrollNumber}/CheckDate")]
        public async Task<IActionResult> UpdateCheckDate([FromRoute] int payrollNumber, [FromBody] UpdatePayrollCheckDate updateCheckDate)
        {
            await payrollAssistantService.UpdateCheckDateAsync(updateCheckDate.CheckDate, payrollNumber, User.GetConum());

            return NoContent();
        }

        /// <summary>
        /// Assigns payroll to Brands
        /// </summary>
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpPost("AssignedToBrands/{payrollNumber}")]
        public async Task<IActionResult> AssignPayrollToBrands([FromRoute] int payrollNumber, AssignPayrollToBrands assignPayroll)
        {
            assignPayroll.Prnum = payrollNumber;
            var payrollStatus = await payrollAssistantService.AssignPayrollToBrandsAsync(assignPayroll, User.GetConum(), User.GetEmpnum(), User.GetFullName());
            return Ok(payrollStatus);
        }

        /// <summary>
        /// Runs payroll reports
        /// </summary>
        [HttpPost("Reports")]
        public async Task<IActionResult> RunReportsAsync([FromBody] RunPayrollReports reports)
        {
            var response = await runReportsService.RunReportsAsync(reports, true);
            return Ok(response);
        }

        /// <summary>
        /// Downloads payroll report
        /// </summary>
        [HttpGet("Reports/{reportQueueId}")]
        public async Task<ActionResult> DownloadPayrollReport([FromRoute] int reportQueueId, [FromServices] IOptionsSnapshot<ReportQueuePathCredentials> reportQCreds)
        {
            ReportQueue report = await runReportsService.UpdateReportDownloadInfoAsync(reportQueueId, User.GetConum(), true);
            DownloadReportFileInfo downloadReportFileInfo = await runReportsService.GetReportFileInfoAsync(reportQueueId, reportQCreds);
            if (reportQCreds.Value.DownloadReportsViaUrl)
            {
                return File(downloadReportFileInfo.Stream, downloadReportFileInfo.ContentType);
            }

            return File(downloadReportFileInfo.Contents, downloadReportFileInfo.ContentType, $"{report.ReportName} {report.ReportParametersDescription}");
        }
    }
}
