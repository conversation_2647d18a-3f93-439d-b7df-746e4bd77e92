﻿using Brands.DAL;
using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Feedback;
using BrandsWebApp.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class FeedbackController : ControllerBase
    {
        private readonly EPDATAContext _dbContext;
        private readonly ILogger<FeedbackController> _logger;

        public FeedbackController(EPDATAContext dbContext, ILogger<FeedbackController> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        [HttpPost("send")]
        public async Task<ActionResult> SendAsync([FromBody]FeedbackModel feedback, [FromServices] ZendeskApiService zendeskApiService)
        {
            try
            {
                var info = $@"
                User: {HttpContext.User.GetFirstName()} {HttpContext.User.GetLastName()},
                Select Co#: {HttpContext.User.GetConum()} 
                Employee - Co#: {HttpContext.User.GetEmpConum()} Emp#: {HttpContext.User.GetEmpnum()},
                Feedback Type: {feedback.Type},
                Feedback: {feedback.Description}";
                _logger.LogDebug("User submitted feedback. {@Feedback}", info);

                var api = await zendeskApiService.GetZendeskApiAsync();

                var ticketResults = await api.Macros.ApplyMacroAsync(Convert.ToInt64(await _dbContext.GetUdfValueAsync("Zendesk_WebsiteFeedback_MacroId")));
                ticketResults.Result.Ticket.Subject = $"New Feedback Submitted by {HttpContext.User.GetFirstName()} {HttpContext.User.GetLastName()}";
                if (ticketResults.Result.Ticket.Comment == null)
                {
                    ticketResults.Result.Ticket.Comment = new ZendeskApi_v2.Models.Tickets.Comment();
                }
                ticketResults.Result.Ticket.Comment.Public = false;
                ticketResults.Result.Ticket.Comment.Body = info;

                await api.Tickets.CreateTicketAsync(ticketResults.Result.Ticket);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating ticket in zendesk");
            }

            return Ok();
        }
    }

}
