﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.Ess.PayHistory;
using BrandsWebApp.Services;
using BrandsWebApp.Services.EmployeeDeck;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.EmployeeDeck
{
    [Authorize(Policy = AuthPolicies.IsEssUser)]
    [ApiController]
    [Route(ControllerPath)]
    public class EssPayHistoryController : ControllerBase
    {
        private const string ControllerPath = "api/employee-deck/my/pay-history";

        private readonly EssPayHistoryService essPayHistoryService;
        private readonly ILogger<EssPayHistoryController> logger;
        private readonly JwtFactory jwtFactory;
        
        public EssPayHistoryController(EssPayHistoryService essPayHistoryService, ILogger<EssPayHistoryController> logger, JwtFactory jwtFactory)
        {
            this.essPayHistoryService = essPayHistoryService;
            this.logger = logger;
            this.jwtFactory = jwtFactory;
        }

        [HttpGet]
        public async Task<IActionResult> GetPayHistory([FromQuery] EssPayHistoryQueryFilter filter)
        {
            var payHistory = await essPayHistoryService.GetPayHistoryAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value, filter.Start, filter.Limit, filter.PayType, filter.FromDate, filter.ToDate);
            var result = new GetEssPayHistory
            {
                PayHist = payHistory,
                MaybeHasMore = payHistory.Count() == filter.Limit
            };

            return Ok(result);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<EssPayHistoryDetails>> GetCheckDetails(string id)
        {
            var result = await essPayHistoryService.GetPayHistoryDetails(id, User.GetEssEmpConum().Value, User.GetEssEmpNum().Value);
            return Ok(result);
        }

        [HttpPost("{id}/download")]
        public ActionResult DownloadURL([FromRoute] string id)
        {
            var filePath = essPayHistoryService.GetDownloadPdfFilePath(id);
            logger.LogDebug("Requesting to download: {filePath}", filePath);

            var urlPath = essPayHistoryService.GetDownloadURLPath(ControllerPath, id);
            var token = jwtFactory.IssueResourceExpiringToken(urlPath, TimeSpan.FromMinutes(15));
            return Ok(new
            {
                DownloadURL = $"/{urlPath}?token={token}",
            });
        }

        [HttpGet("{id}/download.file")]
        [AllowAnonymous]
        public async Task<ActionResult> Download([FromRoute] string id, [FromQuery] string token, [FromServices] IOptionsSnapshot<ReportQueuePathCredentials> reportQCreds)
        {
            try
            {
                logger.LogDebug("Entering download. {checkKey}", id);
                var resourceId = essPayHistoryService.GetDownloadURLPath(ControllerPath, id);
                jwtFactory.ValidateResourceExpiringToken(resourceId, token);

                var filePath = essPayHistoryService.GetDownloadPdfFilePath(id);
                logger.LogDebug("Downloading: " + filePath);

                var fileDownloadName = $"Payroll_{id}.pdf";
                // We need to send the `inline` version of Content-Disposition to
                // specify the filename, so that the browser DISPLAYS the file
                // instead of prompting the user to save it. So, set that here and
                // DO NOT pass it to PhysicalFile, which would override it.
                Response.Headers.Append("Content-Disposition", $"inline; filename=\"{fileDownloadName}\"");
                Response.Headers.Append("X-Filename", fileDownloadName);

                logger.LogDebug("Value of DownloadReportsViaUrl: {value}", reportQCreds.Value.DownloadReportsViaUrl);
                if (reportQCreds.Value.DownloadReportsViaUrl)
                {
                    using (HttpClient client = new HttpClient())
                    {
                        var (coNum, empNum, prNum, checkCounter) = EssPayHistory.ParseId(id);
                        client.DefaultRequestHeaders.Add("ApiKey", reportQCreds.Value.ReportsUrlApiKey);
                        var result = await client.GetStreamAsync($"http://appserver/api/file/stub/{coNum}/{id}");
                        return File(result, "application/pdf", fileDownloadName);
                    }
                }
                else
                {
                    if (!System.IO.File.Exists(filePath))
                    {
                        return NotFound();
                    }

                    ConnectToSharedFolder connectToSharedFolder = GetConnectToSharedFolder(reportQCreds, filePath);
                    logger.LogDebug("Downloading File: {path}", filePath);
                    var fileBytes = await System.IO.File.ReadAllBytesAsync(filePath);
                    connectToSharedFolder?.Dispose();
                    return base.File(fileBytes, "application/pdf", fileDownloadName);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in Download");
                throw;
            }
        }

        private ConnectToSharedFolder GetConnectToSharedFolder(IOptionsSnapshot<ReportQueuePathCredentials> reportQCreds, string filePath)
        {
            if (reportQCreds != null && !string.IsNullOrWhiteSpace(reportQCreds.Value.Username))
            {
                logger.LogDebug("Using ConnectToSharedFolder. Path: {path} username: {username}", filePath, reportQCreds.Value.Username);
                return new ConnectToSharedFolder(filePath, new NetworkCredential(reportQCreds.Value.Username, reportQCreds.Value.Password, reportQCreds.Value.Domain));
            }
            else
            {
                logger.LogDebug("Not using ConnectToSharedFolder");
            }
            return null;
        }
    }
}
