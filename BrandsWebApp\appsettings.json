{"Sentry": {"Dsn": "https://<EMAIL>/4509481566273536", "SendDefaultPii": true, "MaxRequestBodySize": "Always", "MinimumBreadcrumbLevel": "Debug", "MinimumEventLevel": "Warning", "AttachStackTrace": true, "DiagnosticLevel": "Error", "TracesSampleRate": 1.0, "Debug": true}, "Serilog": {"MinimumLevel": {"Default": "Verbose", "Override": {"Microsoft": "Warning"}}, "WriteTo": [{"Name": "Seq", "Args": {"serverUrl": "http://appserver:5341", "apiKey": "0dOOt2t6fxVeYYb2xtqQ", "restrictedToMinimumLevel": "Verbose"}}, {"Name": "<PERSON><PERSON><PERSON>", "Args": {"restrictedToMinimumLevel": "Verbose"}}, {"Name": "Sentry", "Args": {"dsn": "https://<EMAIL>/4509481566273536", "minimumBreadcrumbLevel": "Debug", "minimumEventLevel": "Warning"}}], "Enrich": ["WithMachineName", "WithThreadId", "WithProcessId", "FromLogContext", "WithExceptionDetails"], "Properties": {"AppInstanceId": "auto-generated", "AppID": "BrandsWebApp", "Version": "auto-generated"}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}