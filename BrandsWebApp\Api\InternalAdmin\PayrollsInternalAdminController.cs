﻿using Brands.DAL;
using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Services.Payroll;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.InternalAdmin
{
    [Route("api/Internal/Payrolls")]
    [ApiController]
    [Authorize(Policy = AuthPolicies.InternalPayrollAccess)]
    public class PayrollsInternalAdminController : ControllerBase
    {
        private readonly ExecupayApiService execupayApiService;
        private readonly InternalAdminPayrollsService adminPayrollsService;
        private readonly PayrollsService payrollsService;
        private readonly EPDATAContext ePDATAContext;
        private readonly PayrollIssuesService payrollIssuesService;
        private readonly CalendarsService calendarsService;

        public PayrollsInternalAdminController(
            ExecupayApiService execupayApiService,
            InternalAdminPayrollsService adminPayrollsService,
            PayrollsService payrollsService,
            EPDATAContext ePDATAContext,
            PayrollIssuesService payrollIssuesService,
            CalendarsService calendarsService)
        {
            this.execupayApiService = execupayApiService;
            this.adminPayrollsService = adminPayrollsService;
            this.payrollsService = payrollsService;
            this.ePDATAContext = ePDATAContext;
            this.payrollIssuesService = payrollIssuesService;
            this.calendarsService = calendarsService;
        }

        /// <summary>
        /// Gets payrolls data source
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetPayrollsDataSource()
        {
            var result = await adminPayrollsService.GetPayrollsDataSourceAsync(User.GetConum(), Request.Query);
            return Ok(result);
        }

        /// <summary>
        /// Gets check master batches
        /// </summary>
        [HttpGet("{payrollNumber}/CheckMasterBatches")]
        public async Task<IActionResult> GetCheckMasterBatch([FromRoute] decimal payrollNumber)
        {
            var data = ePDATAContext.CheckMasterBatchItems.Where(c => c.Conum == User.GetConum() && c.Prnum == payrollNumber);
            var result = await adminPayrollsService.GetDataSourceAsync(Request.Query, data, "Id");
            return Ok(result);
        }

        /// <summary>
        /// Updates check master batch
        /// </summary>
        [HttpPut("{payrollNumber}/CheckMasterBatches")]
        public async Task<IActionResult> Update([FromRoute] decimal payrollNumber, [FromForm] int key, [FromForm] string values)
        {
            var data = await ePDATAContext.CheckMasterBatchItems.SingleAsync(c => c.Conum == User.GetConum() && c.Prnum == payrollNumber && c.Id == key);
            JsonConvert.PopulateObject(values, data);
            await ePDATAContext.SaveChangesAsync();
            return Ok(data);
        }

        /// <summary>
        /// Updates payroll
        /// </summary>
        [HttpPut("Update")]
        public async Task<IActionResult> UpdatePayroll([FromForm] Guid key, [FromForm] string values)
        {
            var payroll = await adminPayrollsService.UpdatePayrollAsync(User.GetConum(), key, values);
            return Ok(payroll);
        }

        /// <summary>
        /// Deletes payroll
        /// </summary>
        [HttpDelete("{payrollNumber}")]
        public async Task<IActionResult> DeletePayroll([FromRoute] decimal payrollNumber)
        {
            await payrollsService.DeletePayrollAsync(User.GetConum(), payrollNumber);
            return NoContent();
        }

        /// <summary>
        /// Gets payees
        /// </summary>
        [HttpGet("{payrollNumber}/Earnings")]
        public async Task<IActionResult> GetEarningsDataSource([FromRoute] decimal payrollNumber)
        {
            var result = await adminPayrollsService.GetEarningsDataSourceAsync(User.GetConum(), payrollNumber, Request.Query);
            return Ok(result);
        }

        /// <summary>
        /// Gets payroll issues
        /// </summary>
        [HttpGet("{payrollNumber}/Issues")]
        public async Task<IActionResult> GetIssues([FromRoute] decimal payrollNumber)
        {
            var result = await payrollIssuesService.CheckPayrollIssuesAsync(User.GetConum(), payrollNumber);
            return Ok(result);
        }

        /// <summary>
        /// Gets payroll issues log
        /// </summary>
        [HttpGet("{payrollNumber}/IssuesLog")]
        public async Task<IActionResult> GetIssuesLog([FromRoute] decimal payrollNumber)
        {
            var result = await payrollIssuesService.GetPayrollIssuesLogAsync(User.GetConum(), payrollNumber);
            return Ok(result);
        }

        /// <summary>
        /// Gets payroll status changes
        /// </summary>
        [HttpGet("{payrollNumber}/StatusChanges")]
        public async Task<IActionResult> GetStatusChanges([FromRoute] decimal payrollNumber)
        {
            var result = await adminPayrollsService.GetPayrollStatusChangesAsync(User.GetConum(), payrollNumber);
            return Ok(result);
        }

        /// <summary>
        /// Gets execupay errors
        /// </summary>
        [HttpGet("{payrollNumber}/mpwClientErrors")]
        public async Task<IActionResult> GetmpwClientErrors_T([FromRoute] decimal payrollNumber)
        {
            var errors = await ePDATAContext.MpwClientErrors.Where(e => e.Conum == User.GetConum() && e.Prnum == payrollNumber).ToListAsync();
            return Ok(errors);
        }

        /// <summary>
        /// Gets payrolls in process
        /// </summary>
        [HttpGet("{payrollNumber}/PayrollsInProcess")]
        public async Task<IActionResult> GetPayrollsInProcess([FromRoute] decimal payrollNumber)
        {
            var payrollsInProcess = await adminPayrollsService.GetPayrollsInProcessDataSourceAsync(User.GetConum(), payrollNumber, Request.Query);
            return Ok(payrollsInProcess);
        }

        /// <summary>
        /// Updates payroll in process
        /// </summary>
        [HttpPut("{payrollNumber}/PayrollsInProcess")]
        public async Task<IActionResult> UpdatePayrollsInProcess([FromForm] int key, [FromForm] string values)
        {
            var payrollsInProcess = await adminPayrollsService.UpdatePayrollInProcessAsync(key, values);
            return Ok(payrollsInProcess);
        }

        /// <summary>
        /// Gets deductions
        /// </summary>
        [HttpGet("{payrollNumber}/Deductions")]
        public async Task<IActionResult> GetDeductionsDataSource([FromRoute] decimal payrollNumber)
        {
            var result = await adminPayrollsService.GetDeductionsDataSourceAsync(User.GetConum(), payrollNumber, Request.Query);
            return Ok(result);
        }

        /// <summary>
        /// Gets checks
        /// </summary>
        [HttpGet("{payrollNumber}/Checks")]
        public async Task<IActionResult> GetChecksDataSource([FromRoute] decimal payrollNumber)
        {
            var result = await adminPayrollsService.GetChecksDataSourceAsync(User.GetConum(), payrollNumber, Request.Query);
            return Ok(result);
        }


        /// <summary>
        /// Gets payroll status
        /// </summary>
        [HttpGet("Status")]
        public async Task<IActionResult> GetStatus()
        {
            var result = await execupayApiService.CheckPayrollStatusAsync(User.GetConum());
            return Ok(result);
        }

        /// <summary>
        /// Calculates payroll totals
        /// </summary>
        [HttpPost("{payrollNumber}/Totals")]
        public async Task<IActionResult> CalculateTotals([FromRoute] double payrollNumber)
        {
            await execupayApiService.CalculateTotalsAsync(User.GetConum(), payrollNumber);
            return NoContent();
        }

        /// <summary>
        /// Submit payroll
        /// </summary>
        [HttpPost("{payrollNumber}")]
        public async Task<IActionResult> SubmitPayroll([FromRoute] double payrollNumber)
        {
            await execupayApiService.ProcessPayrollAsync(User.GetConum(), User.GetFullName(), payrollNumber, (double)User.GetEmpnum());
            return NoContent();
        }

        /// <summary>
        /// Gets calendars
        /// </summary>
        [HttpGet("Calendars")]
        public async Task<IActionResult> GetCalendars()
        {
            var calendars = await calendarsService.GetCalendarsAsync(User.GetConum(), true);
            return Ok(calendars);
        }
    }
}
