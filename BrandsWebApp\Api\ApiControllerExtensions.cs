﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Net;

namespace BrandsWebApp.Api
{
    public static class ApiControllerExtensions
    {
        public static BadRequestObjectResult Error(this ControllerBase controller, string code, string description)
        {
            return controller.BadRequest(new
            {
                Code = code,
                Description = description,
            });
        }

        public static bool IsLocal(this HttpRequest req)
        {
            var connection = req.HttpContext.Connection;
            if (connection.RemoteIpAddress != null)
            {
                if (connection.LocalIpAddress != null)
                {
                    return connection.RemoteIpAddress.Equals(connection.LocalIpAddress);
                }
                else
                {
                    return IPAddress.IsLoopback(connection.RemoteIpAddress);
                }
            }

            // for in memory TestServer or when dealing with default connection info
            if (connection.RemoteIpAddress == null && connection.LocalIpAddress == null)
            {
                return true;
            }

            return false;
        }

        public static bool CheckSignalRHeader(this HttpRequest req)
        {
            if (!req.Headers.TryGetValue(Models.Constants.SIGNAL_R_SECRET_KEY_HEADER_NAME, out var key) || !key.All(v => v == Models.Constants.SIGNAL_R_SECRET_KEY_HEADER_VALUE))
            {
                return false;
            }
            return true;
        }

        public static int? GetPaydeckUserIdHeader(this HttpRequest req)
        {
            if (req.Headers.TryGetValue("paydeck_userid", out var key) && int.TryParse(key.FirstOrDefault(), out int paydeckUserId))
            {
                return paydeckUserId;
            }
            return null;
        }
    }
}
