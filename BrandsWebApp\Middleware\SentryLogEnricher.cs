using Sentry;
using Serilog.Core;
using Serilog.Events;
using System;

namespace BrandsWebApp.Middleware
{
    /// <summary>
    /// Serilog enricher that forwards important log events to Sentry
    /// </summary>
    public class SentryLogEnricher : ILogEventEnricher
    {
        public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
        {
            // Only forward Warning, Error, and Fatal level logs to Sentry
            if (logEvent.Level >= LogEventLevel.Warning)
            {
                try
                {
                    var message = logEvent.RenderMessage();
                    var sentryLevel = ConvertToSentryLevel(logEvent.Level);

                    if (logEvent.Exception != null)
                    {
                        // Send exception to Sentry with context
                        SentrySdk.ConfigureScope(scope =>
                        {
                            // Add log context properties to Sentry scope
                            foreach (var property in logEvent.Properties)
                            {
                                if (property.Value is ScalarValue scalarValue)
                                {
                                    scope.SetTag(property.Key, scalarValue.Value?.ToString() ?? "null");
                                }
                            }

                            scope.SetTag("serilog.level", logEvent.Level.ToString());
                            scope.SetTag("serilog.timestamp", logEvent.Timestamp.ToString("O"));
                        });

                        SentrySdk.CaptureException(logEvent.Exception);
                    }
                    else
                    {
                        // Send message to Sentry with context
                        SentrySdk.ConfigureScope(scope =>
                        {
                            // Add log context properties to Sentry scope
                            foreach (var property in logEvent.Properties)
                            {
                                if (property.Value is ScalarValue scalarValue)
                                {
                                    scope.SetTag(property.Key, scalarValue.Value?.ToString() ?? "null");
                                }
                            }

                            scope.SetTag("serilog.level", logEvent.Level.ToString());
                            scope.SetTag("serilog.timestamp", logEvent.Timestamp.ToString("O"));
                        });

                        SentrySdk.CaptureMessage(message, sentryLevel);
                    }
                }
                catch (Exception ex)
                {
                    // Don't let Sentry integration break logging
                    System.Diagnostics.Debug.WriteLine($"Failed to send log to Sentry: {ex.Message}");
                }
            }
        }

        private static SentryLevel ConvertToSentryLevel(LogEventLevel logLevel)
        {
            return logLevel switch
            {
                LogEventLevel.Debug => SentryLevel.Debug,
                LogEventLevel.Information => SentryLevel.Info,
                LogEventLevel.Warning => SentryLevel.Warning,
                LogEventLevel.Error => SentryLevel.Error,
                LogEventLevel.Fatal => SentryLevel.Fatal,
                _ => SentryLevel.Info
            };
        }
    }
}
