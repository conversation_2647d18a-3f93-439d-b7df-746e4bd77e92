﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.PowerGrid;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Payroll;
using BrandsWebApp.Services.Validation.PowerGridValidation;
using FluentValidation.Results;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.InternalAdmin
{
    [Route("api/Internal/PowerGrids")]
    [ApiController]
    [Authorize(Policy = AuthPolicies.InternalPayrollAccess)]
    public class PowerGridsInternalAdminController : ControllerBase
    {
        private readonly ExecupayApiService execupayApiService;
        private readonly PowerGridsService powerGridsService;

        public PowerGridsInternalAdminController(ExecupayApiService execupayApiService, PowerGridsService powerGridsService)
        {
            this.execupayApiService = execupayApiService;
            this.powerGridsService = powerGridsService;
        }

        /// <summary>
        /// Gets power grid data source
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetPowerGridsDataSource()
        {
            var powerGrids = await powerGridsService.GetPowerGridsDataSourceAsync(User.GetConum(), Request.Query);
            return Ok(powerGrids);
        }

        /// <summary>
        /// Gets power grid schemas
        /// </summary>
        [HttpGet("Schemas")]
        public async Task<IActionResult> GetPowerGridSchemas()
        {
            var schemas = await powerGridsService.GetSchemasAsync(User.GetConum());
            return Ok(schemas);
        }

        /// <summary>
        /// Creates power grid
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> CreatePowerGrid([FromBody] CreatePowerGrid createPowerGrid, [FromServices] CreatePowerGridValidation validation)
        {
            ValidationResult validationResult = await validation.ValidateAsync(createPowerGrid);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            var powerGridId = await execupayApiService.CreatePowerGridAsync(User.GetConum(), User.GetFullName(), createPowerGrid);
            return Ok(new { powerGridId });
        }

        /// <summary>
        /// Processes power grid
        /// </summary>
        [HttpPost("{powerGridId}")]
        public async Task<IActionResult> ProcessPowerGrid([FromRoute] Guid powerGridId, [FromBody] ProcessPowerGrid processPowerGrid, [FromServices] ProcessPowerGridValidation validation)
        {
            processPowerGrid.PowerGridId = powerGridId;

            ValidationResult validationResult = await validation.ValidateAsync(processPowerGrid);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            await execupayApiService.ProcessPowerGridAsync(User.GetConum(), User.GetFullName(), processPowerGrid.PayrollNumber, processPowerGrid.PowerGridId, true);
            return NoContent();
        }
    }
}
