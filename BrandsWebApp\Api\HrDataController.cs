﻿using Brands.DAL;
using BrandsWebApp.Authentication;
using BrandsWebApp.Services;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/HrData")]
    [ApiController]
    [Authorize(AuthenticationSchemes = ApiKeyAuthenticationOptions.DefaultScheme)]
    public class HrDataController : ControllerBase
    {
        private readonly EPDATAContext _ePDATAContext;
        private readonly ILogger<HrDataController> _logger;
        private readonly ISqlConnectionService _sqlConnectionService;

        public HrDataController(EPDATAContext ePDATAContext, ILogger<HrDataController> logger, ISqlConnectionService sqlConnectionService)
        {
            _ePDATAContext = ePDATAContext;
            _logger = logger;
            _sqlConnectionService = sqlConnectionService;
        }

        [HttpGet("{Empnum?}")]
        public async Task<IActionResult> GetHrDataAsync(decimal? empnum)
        {
            try
            {
                _logger.LogDebug("Entering GetHrDataAsync. Empnum: {Empnum}", empnum);
                var data = await _ePDATAContext.HRData
                    .Where(h => h.CoNum == HttpContext.User.GetConum() && (!empnum.HasValue || h.EmpNum == empnum))
                    .ToListAsync();
                return Ok(data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetHrDataAsync");
                throw;
            }
        }

        [HttpGet("DocumentTypes")]
        public async Task<IActionResult> GetDocumentTypes()
        {
            _logger.LogDebug("Entering GetDocumentTypes");
            try
            {
                var data = await _ePDATAContext.HRDocumentTypes
                    .Select(d => new { d.FieldName, d.DocumentTitle })
                    .ToListAsync();
                return Ok(data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetDocumentTypes");
                throw;
            }
        }

        [HttpGet("HrDocuments/{empnum}/{FieldName}")]
        public async Task<IActionResult> GetHrDocuments(decimal empnum, string fieldName)
        {
            _logger.LogDebug("Entering GetHrDocuments. Empnum: {Empnum} FieldName: {FieldName}", empnum, fieldName);
            try
            {
                var data = await _ePDATAContext.HRDocuments
                    .Where(d => d.CoNum == HttpContext.User.GetConum() && d.EmpNum == empnum && d.FieldName == fieldName)
                    .Select(d => new { d.DocumentID, d.EmpNum, d.FieldName })
                    .ToListAsync();
                return Ok(data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetHrDocuments");
                throw;
            }
        }

        [HttpGet("DownloadDocument/{DocumentID}")]
        public async Task<IActionResult> DownloadDocument(Guid documentId)
        {
            _logger.LogDebug("Entering DownloadDocument. DocumentId: {documentId}", documentId);
            try
            {
                var data = await _ePDATAContext.DocumentStorage
                    .SingleOrDefaultAsync(d => d.conum == HttpContext.User.GetConum() && d.document_id == documentId);

                var provider = new FileExtensionContentTypeProvider();
                string contentType;
                if (!provider.TryGetContentType(data.name, out contentType))
                {
                    contentType = "application/unknown";
                }
                return File(data.blob, contentType, System.IO.Path.GetFileName(data.name));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in DownloadDocument");
                throw;
            }
        }

        [HttpGet("LaborDistribution/{Empnum?}")]
        public async Task<IActionResult> GetLaborDistribution(decimal? empnum)
        {
            try
            {
                _logger.LogDebug("Entering GetLaborDistribution. Empnum: {Empnum}", empnum);
                var data = await _ePDATAContext.LaborDistributions_T
                    .Where(h => h.CoNum == HttpContext.User.GetConum() && (!empnum.HasValue || h.EmpNum == empnum))
                    .ToListAsync();
                return Ok(data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetLaborDistribution");
                throw;
            }
        }

        [HttpGet("Jobs")]
        public async Task<IActionResult> GetJobs()
        {
            _logger.LogDebug("Entering GetJobs.");
            try
            {
                await using (var con = _sqlConnectionService.GetSqlConnection())
                {
                    return Ok(await con.QueryAsync<Jobs>(@"
                        --DECLARE @conum AS decimal(6,0) = 3124
                        SELECT 'Job' Source, Job_id Id, job_descr Name, active IsActive from CO_JOBS_T Where conum = @conum
                        UNION ALL
                        SELECT 'Job2' Source, job_code, Job_Desc, Active_Job from JOB2_T Where conum = @conum
                        UNION ALL
                        SELECT 'Job3' Source, job_code, Job_Desc, Active_Job from JOB3_T Where conum = @conum
                        UNION ALL
                        SELECT 'Job4' Source, job_code, Job_Desc, Active_Job from JOB4_T Where conum = @conum
                        UNION ALL
                        SELECT 'Job5' Source, job_code, Job_Desc, Active_Job from JOB5_T Where conum = @conum
                        UNION ALL
                        SELECT 'Department' Source, DIVNUMD, DEPT_DESC, DPACTIVE FROM DEPARTMENT_T WHERE conum = @conum
                        UNION ALL
                        SELECT 'Division', DDIVNUM, DDIVNAME, 'YES' FROM DIVISION_T Where Conum = @conum", new { conum = HttpContext.User.GetConum() }));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetJobs");
                throw;
            }
        }

        public class Jobs
        {
            public string Source { get; set; }
            public int? Id { get; set; }
            public string Name { get; set; }
        }
    }
}