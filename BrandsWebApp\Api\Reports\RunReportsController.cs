﻿using Brands.DAL;
using Brands.DAL.DomainModels.App;
using Brands.DataModels;
using BrandsWebApp.Authentication;
using BrandsWebApp.Exceptions;
using BrandsWebApp.Hubs;
using BrandsWebApp.Models;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.RunReport;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Payroll;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayrollUtility.Constants;
using QueueBuilder;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/Reports/{conum}/RunReports")]
    [ApiController]
    public class RunReportsController : ControllerBase
    {
        private readonly ILogger<RunReportsController> logger;
        private readonly RunReportsService runReportsService;
        private readonly EPDATAContext dbContext;
        private readonly IAuthorizationService authorizationService;
        private readonly PayrollReportsService payrollReportsService;

        public RunReportsController(
            EPDATAContext dbContext,
            ILogger<RunReportsController> logger,
            RunReportsService runReportsService,
            IAuthorizationService authorizationService,
            PayrollReportsService payrollReportsService)
        {
            this.logger = logger;
            this.runReportsService = runReportsService;
            this.dbContext = dbContext;
            this.authorizationService = authorizationService;
            this.payrollReportsService = payrollReportsService;
        }

        [Authorize(Policy = AuthPolicies.RunReportsCompanyOrCpa)]
        [HttpGet]
        public async Task<IActionResult> GetCategorizedReportsAsync([FromRoute] decimal conum, [FromQuery] bool isFavorite)
        {
            var reports = await runReportsService.GetCategorizedReportsAsync(isFavorite, User.GetPaydeckUserId(), conum);

            return Ok(reports);
        }

        [Authorize(Policy = AuthPolicies.RunReportsCompanyOrCpa)]
        [HttpGet("{id}/Parameters")]
        public async Task<IActionResult> GetReportParametersAsync([FromRoute] decimal conum, [FromRoute] int id)
        {
            try
            {
                var response = await runReportsService.GetReportParametersAsync(id, conum);
                return Ok(response);
            }
            catch (DatabaseRecordNotFoundException e)
            {
                return NotFound(e.Message);
            }
        }

        [Authorize(Policy = AuthPolicies.RunReportsCompanyOrCpa)]
        [HttpPost("addToFavorites/{reportId}")]
        public async Task<ActionResult<string>> AddToFavorites(int reportId)
        {
            var report = dbContext.ReportEmailTemplates.Find(reportId);
            if (report == null)
            {
                return StatusCode(500, "No Report with given Id found");
            }
            var currentUserId = User.GetPaydeckUserId();
            if (!dbContext.ReportFavorites.Any(fav => fav.PaydeckUserId == currentUserId && fav.ReportId == reportId))
            {
                dbContext.ReportFavorites.Add(new ReportFavorites
                {
                    PaydeckUserId = User.GetPaydeckUserId(),
                    ReportId = reportId,
                    AddedOn = DateTime.Now
                });
                await dbContext.SaveChangesAsync().ConfigureAwait(false);
            }
            else
            {
                return Conflict("Already added to Favourites");
            }
            return Ok();
        }

        [Authorize(Policy = AuthPolicies.RunReportsCompanyOrCpa)]
        [HttpPost("removeFromFavorites/{reportId}")]
        public async Task<ActionResult<string>> RemoveFromFavorites(int reportId)
        {
            var favourite = await dbContext.ReportFavorites
                .SingleOrDefaultAsync(fav => fav.ReportId == reportId && fav.PaydeckUserId == User.GetPaydeckUserId()).ConfigureAwait(false);
            if (favourite == null)
            {
                return StatusCode(500, "No report with given Id found in favorites");
            }
            dbContext.ReportFavorites.Remove(favourite);
            await dbContext.SaveChangesAsync().ConfigureAwait(false);
            return Ok();
        }

        [Authorize(Policy = AuthPolicies.RunReportsCompanyOrCpa)]
        [HttpPost("Run/{id}")]
        public async Task<IActionResult> RunReportAsync([FromRoute] decimal conum, [FromRoute] int id, [FromBody] ReportParametersPost paramsBody)
        {
            try
            {
                var response = await runReportsService.RunReportAsync(id, paramsBody, conum);
                return Ok(response);
            }
            catch (DatabaseRecordNotFoundException e)
            {
                return NotFound(e.Message);
            }
            catch (ArgumentException e)
            {
                return BadRequest(e.Message);
            }
        }

        [Authorize(Policy = AuthPolicies.PaydeckPayrollOrRunReportsCompanyOrCpa)]
        [HttpPost("ProcessManualChecks")]
        public async Task<ActionResult<string>> ProcessManualChecks([FromRoute] decimal conum, [FromBody] CheckedManualChecks checkedManualChecks)
        {
            var checks = await dbContext.ManualCheckMasters.Where(mc => mc.CONUM == conum && mc.PAYROLL_NUM == -1 && mc.CHK_TYPE == "MANUAL" && mc.mpwStatus == ManualCheckStatus.New).ToArrayAsync();
            //option to only process selected checks was added later on, so if CheckedManualChecks is null, we will just process all checks. 
            //if user selected any specific checks to be printed (or all) - we will validate that the rowguid matches with what we have in the DB. 
            if (checkedManualChecks.CheckRowGuids != null)
            {
                var unmatchedChecks = checkedManualChecks.CheckRowGuids.Where(c => !checks.Select(c => c.rowguid).Contains(c));
                if (unmatchedChecks.Any())
                {
                    logger.LogError("Error in ProcessManualChecks. has unmatchedChecks {unmatchedChecks}", unmatchedChecks);
                    throw new BadRequestException("Checks not found. Please try again.");
                }
                checks = checks.Where(c => checkedManualChecks.CheckRowGuids.Contains(c.rowguid)).ToArray();
            }

            var reportQueueUpdate = await runReportsService.RunManualCheckReportAsync(conum, checks);

            var response = new
            {
                error = new { },
                message = "Your request has been submitted.\nYou will be notified when your report is ready to download. This may take a few minutes…",
                data = reportQueueUpdate
            };
            return Ok(response);
        }

        [Authorize(Policy = AuthPolicies.PayHistoryOrRunReportsCompanyOrCpa)]
        [HttpPost("ProcessPayStubsReport")]
        public async Task<ActionResult<string>> ProcessPayStubsReport([FromRoute] decimal conum, PayStubReportParameter[] parameters, [FromServices] IConfiguration configuration, [FromServices] QueueBuilder.QueueBuilder queueBuilder)
        {
            var report = dbContext.ReportEmailTemplates.Find(Models.Constants.REGULAR_CHECKS_REPORT_ID);
            if (report == null)
            {
                return StatusCode(500, "PayStub report not found.");
            }

            ReportQueue reportQueue = runReportsService.GetReportQueue(report, conum);
            dbContext.ReportsQueue.Add(reportQueue);
            await dbContext.SaveChangesAsync().ConfigureAwait(false);

            queueBuilder.WithConum(conum)
                .WithDescription("Web Process PayStubsReport")
                .WithRequesterInfo(HttpContext.User.GetEmail(), "PPX_WebUser", HttpContext.User.GetEmpConum(), HttpContext.User.GetEmpnum());

            foreach (var check in parameters)
            {
                var reportParameters = new List<Brands.DAL.DomainModels.App.ReportParameter>();
                reportParameters.Add(new Brands.DAL.DomainModels.App.ReportParameter(148, "CoNum", conum.ToString()));
                reportParameters.Add(new Brands.DAL.DomainModels.App.ReportParameter(149, "PrNum", check.Prnum.ToString()));
                reportParameters.Add(new Brands.DAL.DomainModels.App.ReportParameter(150, "EmpNum", check.Empnum.ToString()));
                reportParameters.Add(new Brands.DAL.DomainModels.App.ReportParameter(151, "ChkCounter", check.ChkCounter.ToString()));
                queueBuilder.EnqueueReport(new EnqueueReportOptions
                {
                    ReportEmailTemplate = report,
                    ReportParameters = reportParameters,
                });
            }
            queueBuilder.EnqueuePaydeckCombinedReport(reportQueue.Id);
            await queueBuilder.SaveAndPublishAsync();

            var response = new
            {
                error = new { },
                message = "Your request has been submitted.\nYou will be notified when your report is ready to download. This may take a few minutes…",
                data = ReportQueueUpdate.Copy(reportQueue)
            };
            return Ok(response);
        }

        [Authorize(Policy = AuthPolicies.PaydeckPayrollOrRunReportsCompanyOrCpa)]
        [HttpGet("Download/{reportQueueId}")]
        public async Task<ActionResult> Download([FromRoute] decimal conum, int reportQueueId, [FromServices] IOptionsSnapshot<ReportQueuePathCredentials> reportQCreds)
        {
            try
            {
                ReportQueue report = await runReportsService.UpdateReportDownloadInfoAsync(reportQueueId, conum);
                if (report.ReportId != Models.Constants.REGULAR_CHECKS_REPORT_ID)
                {
                    var authResult = await authorizationService.AuthorizeAsync(User, AuthPolicies.RunReportsCompanyOrCpa);
                    if (!authResult.Succeeded)
                    {
                        logger.LogWarning("User without needed permissions requested to download ReportId: {ReportId} Id: {Id}.", report.ReportId, report.Id);
                        return Unauthorized("You are not authorized to download the selected report.");
                    }
                }

                DownloadReportFileInfo downloadReportFileInfo = await runReportsService.GetReportFileInfoAsync(reportQueueId, reportQCreds);
                if (reportQCreds.Value.DownloadReportsViaUrl)
                {
                    return File(downloadReportFileInfo.Stream, downloadReportFileInfo.ContentType);
                }

                return File(downloadReportFileInfo.Contents, downloadReportFileInfo.ContentType, $"{report.ReportName} {report.ReportParametersDescription}");
            }
            catch (DatabaseRecordNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(ex.Message);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in Download. ReportId: {reportQueueId}", reportQueueId);
                throw;
            }
        }

        [Authorize(Policy = AuthPolicies.RunReportsCompanyOrCpa)]
        [HttpGet("History")]
        public async Task<IActionResult> HistoryAsync([FromRoute] decimal conum, [FromQuery] int? takeOnly)
        {
            var response = await runReportsService.HistoryAsync(takeOnly, conum);
            return Ok(response);
        }

        [HttpPost("/api/Statements/ReportUpdate")]
        [AllowAnonymous]
        public async Task<IActionResult> ReportUpdate(ReportQueueUpdate reportQueueUpdate, [FromQuery] bool withNotification, [FromServices] IHubContext<MainHub> mainHubContext, [FromServices] PayrollApprovalService payrollApprovalService)
        {
            logger.LogDebug("Entering ReportUpdate. {@ReportQueueUpdate}", reportQueueUpdate);
            if (!HttpContext.Request.Headers.TryGetValue(Models.Constants.SIGNAL_R_SECRET_KEY_HEADER_NAME, out var key) || !key.All(v => v == Models.Constants.SIGNAL_R_SECRET_KEY_HEADER_VALUE))
            {
                logger.LogWarning("Unauthorized in ReportUpdate");
                return Unauthorized();
            }

            var report = await dbContext.ReportsQueue.SingleAsync(r => r.Id == reportQueueUpdate.Id);
            reportQueueUpdate.Conum = (int)report.Conum;
            reportQueueUpdate.PrBatchInProcessId = report.PrBatchInProcessId;

            if (report.PrBatchInProcessId != null)
            {
                var usersWithPayrollPermission = await dbContext.BrandsAuthUserClaims
                    .Include(ue => ue.BrandsUserEmployee)
                    .Where(uc => uc.BrandsUserEmployee.Conum == reportQueueUpdate.Conum)
                    .Where(uc => uc.ClaimType == Permission.PermissionClaimName && (uc.ClaimValue == Permission.PaydeckPayroll || uc.ClaimValue == Permission.PaydeckPayroll))
                    .Select(uc => uc.UserId.ToString())
                    .ToListAsync();

                var prApprovalRptUpdate = (await payrollReportsService.GetPayrollReportsAsync(report.PrBatchInProcessId.Value, reportQueueUpdate.Conum, report.ReportId)).Single();
                var connections = MainHub.Connections.Values.Where(c => c.Conum == report.Conum.ToString() && usersWithPayrollPermission.Contains(c.UserId)).ToList();
                foreach (var connection in connections)
                {
                    await mainHubContext.Clients.Client(connection.ConnectionId).SendAsync("PrApprovalRptUpdate", prApprovalRptUpdate);
                }

                return NoContent();
            }

            var user = $"{report.RequestedByPaydeckUserId}";
            if (withNotification)
            {
                await mainHubContext.Clients.User(user).SendAsync("ReportNotification", reportQueueUpdate);
            }
            else
            {
                var connections = MainHub.Connections.Values.Where(c => c.Conum == report.Conum.ToString() && c.UserId == report.RequestedByPaydeckUserId.ToString()).ToList();
                foreach (var connection in connections)
                {
                    await mainHubContext.Clients.Client(connection.ConnectionId).SendAsync("ReportUpdate", reportQueueUpdate);
                }
            }

            return NoContent();
        }
    }
}
