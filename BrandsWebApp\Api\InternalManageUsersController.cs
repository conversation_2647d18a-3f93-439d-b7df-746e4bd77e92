﻿using Brands.DAL;
using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Auth;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Brands.DataModels;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/Internal/ManageUsers")]
    [ApiController]
    public class InternalManageUsersController : ControllerBase
    {
        private readonly ILogger<InternalManageUsersController> logger;
        private readonly ManageUserService manageUserService;
        private readonly EPDATAContext ePDATAContext;
        private readonly BrandsAuthService brandsAuthService;
        private readonly UserManager<BrandsAuthUser> userManager;
        private readonly UserEventService userEventService;
        private readonly PermissionDataService permissionDataService;
        private readonly SwipeClockApiService swipeClockApiService;

        public InternalManageUsersController(ILogger<InternalManageUsersController> logger, ManageUserService manageUserService, EPDATAContext ePDATAContext,
            BrandsAuthService brandsAuthService, UserManager<BrandsAuthUser> userManager, UserEventService userEventService, PermissionDataService permissionDs,
            SwipeClockApiService swipeClockApiService)
        {
            this.logger = logger;
            this.manageUserService = manageUserService;
            this.ePDATAContext = ePDATAContext;
            this.brandsAuthService = brandsAuthService;
            this.userManager = userManager;
            this.userEventService = userEventService;
            this.permissionDataService = permissionDs;
            this.swipeClockApiService = swipeClockApiService;
        }

        [HttpPost("CreateUser")]
        [AllowAnonymous]
        public async Task<IActionResult> CreateUserAsync([FromBody] CreateUserRequest request)
        {
            logger.LogDebug("Entering CreateUserAsync. {@Request}", request);
            if (!await manageUserService.AllowAccessAsync(request))
            {
                return Unauthorized();
            }

            Employee_T employee;
            employee = await ePDATAContext.Employees.SingleOrDefaultAsync(e => e.Conum == request.EmpConum && e.Empnum == request.Empnum);
            var user = await manageUserService.FindByEmailAsync(request.EmailAddress);
            var createUserResults = await manageUserService.ValidateCreateUser(request, employee, user);
            if (!createUserResults.isValid)
            {
                return BadRequest(createUserResults.message);
            }

            if (request.ResendInviteEmailOnly)
            {
                logger.LogDebug("Resending email.");
                await brandsAuthService.SendInviteEmail(user.Email, request, employee, user);
                return Ok();
            }

            var createUserReesults = await manageUserService.CreateOrLinkPaydeckUser(user, request, employee);
            if (!createUserReesults)
            {
                return BadRequest();
            }

            return Ok();
        }

        [HttpPost("delete_user_request")]
        public async Task<IActionResult> DeleteUser(CreateUserRequest createUserRequest)
        {
            logger.LogDebug("Entering delete_user_request. DeleteUserRequestInternal: {@DeleteUserRequestInternal}", createUserRequest.DeleteUserRequestInternal);
            if (!await manageUserService.AllowAccessAsync(createUserRequest))
            {
                return BadRequest();
            }

            var user = await userManager.FindByIdAsync(createUserRequest.DeleteUserRequestInternal?.UserId.ToString());
            if (user == null)
            {
                logger.LogWarning("User was not found. Id: {UserId}", createUserRequest.DeleteUserRequestInternal?.UserId);
            }

            logger.LogWarning("Deleting user. Email: {email} userId: {UserId} {@user}", user.Email, user.Id, user);
            userEventService.AddUserEvent(user, "DELETING_USER", true, message: $"Deleting user: {user.Email}")
               .WithCreatedBy(createUserRequest.CreatedByUserType, createUserRequest.CreatedByUserId);
            await userManager.UpdateAsync(user);
            var results = await userManager.DeleteAsync(user);
            if (!results.Succeeded)
            {
                logger.LogWarning("Error deleting user", results.Errors);
                return BadRequest("Error deleting user");
            }

            return Ok();
        }

        [HttpPost("change_company_link")]
        public async Task<IActionResult> ChangeCompanyLink(CreateUserRequest createUserRequest)
        {
            logger.LogDebug("Entering change_company_link. ChangeCompanyLinkInternal: {@RemoveCompanyLinkInternal}", createUserRequest.ChangeCompanyLinkInternal);
            if (createUserRequest.ChangeCompanyLinkInternal == null)
            {
                return BadRequest();
            }

            if (!await manageUserService.AllowAccessAsync(createUserRequest))
            {
                return BadRequest();
            }

            var request = new ManageUserService.UpdateUserCompanyLinkRequest
            {
                UserId = createUserRequest.ChangeCompanyLinkInternal?.UserId.ToString(),
                DeleteLink = createUserRequest.ChangeCompanyLinkInternal.DeleteOrInactivate == "DeleteIt",
                InactivateLink = createUserRequest.ChangeCompanyLinkInternal.DeleteOrInactivate == "InactivateIt",
                ActivateLink = createUserRequest.ChangeCompanyLinkInternal.DeleteOrInactivate == "ActivateIt",
                Conum = createUserRequest.ChangeCompanyLinkInternal.Conum,
                CreatedByUserId = createUserRequest.CreatedByUserId,
                CreatedByUserType = createUserRequest.CreatedByUserType
            };
            var results = await manageUserService.UpdateUserCompanyLink(request);
            if (!results.isValid)
            {
                return BadRequest(results.message);
            }
            return Ok();
        }

        [HttpPost("mark_user_phonenumber_confirmed")]
        public async Task<IActionResult> MarkUserPhonenumberConfirmed(CreateUserRequest createUserRequest)
        {
            logger.LogDebug("Entering mark_user_phonenumber_confirmed. MarkUserPhoneNumberConfirmedInternal: {@MarkUserPhoneNumberConfirmedInternal}", createUserRequest.MarkUserPhoneNumberConfirmedInternal);
            if (createUserRequest.MarkUserPhoneNumberConfirmedInternal == null)
            {
                return BadRequest();
            }

            if (!await manageUserService.AllowAccessAsync(createUserRequest))
            {
                return BadRequest();
            }

            var user = await userManager.FindByIdAsync(createUserRequest.MarkUserPhoneNumberConfirmedInternal.UserId.ToString());
            if (user == null)
            {
                logger.LogWarning("User was not found. Id: {UserId}", createUserRequest.MarkUserPhoneNumberConfirmedInternal?.UserId);
            }

            user.PhoneNumberConfirmed = true;
            userEventService.AddUserEvent(user, "MARK_PHONENUMBER_CONFIRMED", true)
                .WithCreatedBy(createUserRequest.CreatedByUserType, createUserRequest.CreatedByUserId);
            var results = await userManager.UpdateAsync(user);
            if (results.Succeeded)
            {
                return Ok();
            }
            else
            {
                logger.LogError("Error in mark_user_phonenumber_confirmed. {@Errors}", results.Errors);
                return BadRequest(results.Errors);
            }
        }

        [HttpPost("clear_lockout")]
        public async Task<IActionResult> ClearLockout(CreateUserRequest createUserRequest)
        {
            logger.LogDebug("Entering clear_lockout. clear_lockout: {@ClearLockoutInternal}", createUserRequest.ClearLockoutInternal);
            if (createUserRequest.ClearLockoutInternal == null)
            {
                return BadRequest();
            }

            if (!await manageUserService.AllowAccessAsync(createUserRequest))
            {
                return BadRequest();
            }

            var user = await userManager.FindByIdAsync(createUserRequest.ClearLockoutInternal.UserId.ToString());
            if (user == null)
            {
                logger.LogWarning("User was not found. Id: {UserId}", createUserRequest.ClearLockoutInternal?.UserId);
            }

            user.LockoutEnd = null;
            userEventService.AddUserEvent(user, "CLEAR_LOCKOUT", true).WithCreatedBy(createUserRequest.CreatedByUserType, createUserRequest.CreatedByUserId);
            var results = await userManager.UpdateAsync(user);
            if (results.Succeeded)
            {
                return Ok();
            }
            else
            {
                logger.LogError("Error in clear_lockout. {@Errors}", results.Errors);
                return BadRequest(results.Errors);
            }
        }

        [HttpPost("update_phone_number")]
        public async Task<IActionResult> UpdatePhoneNumber(CreateUserRequest createUserRequest, [FromServices] UserPhoneNumberService userPhoneNumberService)
        {
            logger.LogDebug("Entering clear_lockout. clear_lockout: {@update_phone_number}", createUserRequest.UpdatePhoneNumberInternal);
            if (createUserRequest.UpdatePhoneNumberInternal == null)
            {
                return BadRequest();
            }

            if (!await manageUserService.AllowAccessAsync(createUserRequest))
            {
                return BadRequest();
            }

            var valid = userPhoneNumberService.IsPhoneNumberValid(createUserRequest.UpdatePhoneNumberInternal.PhoneNumber);
            if (!valid.IsValid)
            {
                return BadRequest(valid.Message);
            }

            var user = await userManager.FindByIdAsync(createUserRequest.UpdatePhoneNumberInternal.UserId.ToString());
            if (user == null)
            {
                logger.LogWarning("User was not found. Id: {UserId}", createUserRequest.UpdatePhoneNumberInternal?.UserId);
            }

            userEventService.AddUserEvent(user, "UPDATE_PHONE_NUMBER", true, message: $"Updating PhoneNumber from: {user.PhoneNumber} to: {valid.Number}").WithCreatedBy(createUserRequest.CreatedByUserType, createUserRequest.CreatedByUserId);
            user.PhoneNumber = valid.Number;
            var results = await userManager.UpdateAsync(user);
            if (results.Succeeded)
            {
                return Ok();
            }
            else
            {
                logger.LogError("Error in update_phone_number. {@Errors}", results.Errors);
                return BadRequest(results.Errors);
            }
        }

        [HttpGet("user_permissions")]
        public async Task<IActionResult> GetUserPermissions(int userId, int conum)
        {
            logger.LogDebug("Entering GetUserPermissions");
            if (!await manageUserService.VerifyApiKeyHeaderAndInternalIpAsync())
            {
                return BadRequest();
            }
            var userClaims = await ePDATAContext.BrandsAuthUserClaims.Where(c => c.ClaimType == Permission.PermissionClaimName && c.UserId == userId && c.Conum == conum).ToListAsync();
            var claims = await permissionDataService.GetUserClaims(userClaims, conum, true);
            return Ok(claims.Select(c => c.Value));
        }

        [HttpGet("rolesWithAllClaims")]
        public async Task<IActionResult> GetRolesWithAllClaimsAsync()
        {
            logger.LogDebug("Entering GetRolesWithAllClaimsAsync");
            if (!await manageUserService.VerifyApiKeyHeaderAndInternalIpAsync())
            {
                return BadRequest();
            }
            var roles = await permissionDataService.GetRolesWithAllClaimsAsync(true);
            return Ok(roles);
        }

        [HttpGet("swipeClockUsers")]
        public async Task<IActionResult> GetSwipeClockUsers(int conum, [FromHeader(Name = "X-API-KEY-BRANDS-AUTH-MANAGE")] string apiKey)
        {
            try
            {
                logger.LogDebug("Entering GetSwipeClockUsers");
                if (!await manageUserService.VerifyApiKeyHeaderAndInternalIpAsync())
                {
                    return BadRequest();
                }
                var users = await swipeClockApiService.GetUsersAsync(conum);
                return Ok(users);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetSwipeClockUsers");
                throw;
            }
        }
    }
}
