﻿using BrandsWebApp.Authentication;
using BrandsWebApp.DataAccess;
using BrandsWebApp.Extensions;
using BrandsWebApp.Helpers;
using BrandsWebApp.Models.Auth;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/[controller]")]
    [ApiController]
    public class DashboardController : ControllerBase
    {
        private readonly PRC_DashboardCharts _DashboardCharts;
        private readonly DashboardDataService _dashboardDataService;
        private readonly ILogger _logger;
        public DashboardController(PRC_DashboardCharts dashboardCharts, DashboardDataService dashboardDataService, ILogger<DashboardController> logger)
        {
            _DashboardCharts = dashboardCharts;
            _dashboardDataService = dashboardDataService;
            _logger = logger;
        }

        [Authorize(Policy = AuthPolicies.DashboardCompanyOrCpa)]
        [HttpGet("{conum}/EmployeesByDepartment")]
        public async Task<ActionResult> EmployeesByDepartmentAsync([FromRoute] decimal conum)
        {
            return Ok(await _dashboardDataService.GetEmployeesByDepartment(conum).ConfigureAwait(false));
        }

        [Authorize(Policy = AuthPolicies.DashboardCompanyOrCpa)]
        [HttpGet("{conum}/Dashboard")]
        public async Task<IActionResult> GetDashboad([FromRoute] decimal conum)
        {
            return Ok(await _dashboardDataService.GetDashboard(conum, User.GetPaydeckUserId()).ConfigureAwait(false));
        }

        [Authorize(Policy = AuthPolicies.DashboardCompanyOrCpa)]
        [HttpGet("{conum}/MissedLastPayroll")]
        public async Task<IActionResult> MissedLastPayroll([FromRoute] decimal conum)
        {
            return Ok((await _dashboardDataService.GetMissedLastPayroll(conum).ConfigureAwait(false)).ToList());
        }

        [Authorize(Policy = AuthPolicies.DashboardCompanyOrCpa)]
        [HttpGet("{conum}/ManualChecks")]
        public async Task<IActionResult> GetManualChecks([FromRoute] decimal conum)
        {
            return Ok((await _dashboardDataService.GetManualChecks(conum).ConfigureAwait(false)).ToList());
        }

        [Authorize(Policy = AuthPolicies.DashboardCompanyOrCpa)]
        [HttpGet("{conum}/BirthdayOrAnniversary")]
        public async Task<IActionResult> BirthdayOrAnniversary([FromRoute] decimal conum)
        {
            return Ok((await _dashboardDataService.GetBirthdayOrAnniversary(conum).ConfigureAwait(false)).ToList());
        }

        [Authorize(Policy = nameof(Permission.Dashboard))]
        [HttpGet]
        public async Task<ActionResult> Get()
        {
            var data = await _DashboardCharts.GetLastPayrollSummaryTip(HttpContext.User.GetConum());
            var json = new KeyValuePair<string, string>[]
            {
                new KeyValuePair<string, string>("Active Employees", data.CurrentActiveEmployees.ToString()),
                new KeyValuePair<string, string>("Next Payroll Date", data.NextPayrollDate?.ToShortDateString()),
                new KeyValuePair<string, string>("Total Cash Last Payroll", data.TotalCashLastPayroll.HasValue ? data.TotalCashLastPayroll.Value.ToString("n2") : 0.ToString("n2")),
                new KeyValuePair<string, string>("Total Hours Last Payroll", data.TotalHoursLastPayroll.ToString()),
            };
            var response = new
            {
                Error = new { },
                Code = 200,
                Result = new
                {
                    Data = json,
                },
            };
            return Ok(response);
        }

        [Authorize(Policy = AuthPolicies.DashboardCompanyOrCpa)]
        [HttpGet("{conum}/Payroll")]
        public async Task<ActionResult> Payroll([FromRoute] decimal conum, int type, int startTime, int endTime, int columns)
        {
            if (startTime - endTime < 0)
                return StatusCode(500, "not valid time range");
            if (startTime == 0)
                startTime = 30;
            var fromDate = DateTime.Today.AddDays(-startTime);
            var toDate = DateTime.Today.AddDays(-endTime);
            bool isMoreThan180Days = startTime - endTime > 180;

            // if no chartType is specified in the type param, or chartType is 0, then by default is 1
            var chartType = (DashboardHelper.ChartTypes)(type == 0 ? 1 : type);
            object Data = null;
            switch (chartType)
            {
                case DashboardHelper.ChartTypes.TotalPayrollCashByDate:
                    Data = (await _DashboardCharts.GetTotalPayrollHoursAndAmounts(conum, fromDate, toDate, isMoreThan180Days)).Select(p => new
                    {
                        key = isMoreThan180Days ? DashboardHelper.ParseMonth(p.CHECK_DATE) : $"{p.CHECK_DATE.ToString("d")}",
                        value = p.TotalCashAmount,
                    });
                    break;
                case DashboardHelper.ChartTypes.Top15PayAmountsByEmployees:
                    Data = (await _DashboardCharts.GetTop15PayEmployees(conum, fromDate, toDate)).Select(t => new
                    {
                        key = t.Name,
                        value = t.TotalPaid
                    });
                    break;
                case DashboardHelper.ChartTypes.Top15HourAmountsByEmployees:
                    Data = (await _DashboardCharts.GetTop15HoursEmployees(conum, fromDate, toDate)).Select(t => new
                    {
                        key = t.Name,
                        value = TimeSpan.FromHours((double)t.TotalHours).ToString()
                    });
                    break;
                default:
                    break;
            }
            var response = new
            {
                Error = new { },
                Code = 200,
                Result = new
                {
                    ChartId = chartType.GetHashCode(),
                    ChartName = EnumHelper<DashboardHelper.ChartTypes>.GetDisplayValue(chartType),
                    TimeRange = startTime - endTime,
                    data = Data,
                }
            };
            return Ok(response);
        }

        [Authorize(Policy = AuthPolicies.DashboardCompanyOrCpa)]
        [HttpGet("{conum}/EmployeeCounts/{timeRange}")]
        public async Task<IActionResult> GetEmployeeCounts([FromRoute] decimal conum, string timeRange)
        {
            try
            {
                var timeRanges = timeRange.GetTimeFrame();
                var fromDate = timeRanges[0];
                var toDate = timeRanges[1];
                return Ok(await _dashboardDataService.GetEmployeeCountsAsync(conum, fromDate, toDate));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetEmployeeCounts");
                throw;
            }
        }

        [Authorize(Policy = AuthPolicies.DashboardCompanyOrCpa)]
        [HttpGet("{conum}/PayTypesCount/{timeRange}")]
        public async Task<IActionResult> GetPayTypesCount([FromRoute] decimal conum, string timeRange)
        {
            try
            {
                var timeRanges = timeRange.GetTimeFrame();
                var fromDate = timeRanges[0];
                var toDate = timeRanges[1];
                return Ok(await _dashboardDataService.GetPayTypesCount(conum, fromDate, toDate));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetPayTypesCount");
                throw;
            }
        }

        [Authorize(Policy = nameof(Permission.Dashboard))]
        [HttpGet("[action]")]
        public async Task<ActionResult> Employee(int employee = -1, int department = -1, string timerange = "thisweek")
        {
            // get starttime and endtime
            var timeRange = timerange.GetTimeFrame();
            var fromDate = timeRange[0];
            var toDate = timeRange[1];

            // Employee HOURS AND EARNINGS:
            var eeHoursAndEarnings = await _DashboardCharts.GetEmployeeHoursAndEarnings(HttpContext.User.GetConum(), fromDate, toDate);
            if (employee > -1)
            {
                eeHoursAndEarnings = eeHoursAndEarnings.Where(e => e.Empnum == employee);
            }
            var regularHours = eeHoursAndEarnings.Sum(e => e.REG_HRS);
            var overtimeHours = eeHoursAndEarnings.Sum(e => e.OT_HRS);
            // var sickHours - data not available
            var regularEarnings = eeHoursAndEarnings.Sum(e => e.REG_PAY);
            var overtimeEarnings = eeHoursAndEarnings.Sum(e => e.OT_PAY);
            // var sickEarnings
            // Employee DEDUCTIONS:
            var eeDeductions = await _DashboardCharts.GetEmployeeDeductions(HttpContext.User.GetConum(), fromDate, toDate);
            if (employee > -1)
            {
                eeDeductions = eeDeductions.Where(e => e.Empnum == employee);
            }
            var federalIncomeTax = eeDeductions.Sum(e => e.FED_WH);
            var stateTax = eeDeductions.Sum(e => e.STATE_AMOUNT);
            var localTax = eeDeductions.Sum(e => e.LCL_AMOUNT);
            var socialSecurityTax = eeDeductions.Sum(e => e.OASDI);
            var medicareTax = eeDeductions.Sum(e => e.MEDICARE);

            var charts = new object[]
            {
                new
                {
                    name = "Employee Hours",
                    data = new object[]
                    {
                        new { key = "Regular Hours", value = regularHours , text = FormateHours(regularHours) },
                        new { key = "Overtime Hours", value = overtimeHours , text = FormateHours(overtimeHours) },
                        //new { key = "Paid sick Hours", value = (decimal)0 , text = TimeSpan.FromHours((double)sickHours).ToString() },
                    }
                },
                new
                {
                    name = "Employee Earnings",
                    data = new object[]
                    {
                        new { key = "Regular Earnings", value = regularEarnings },
                        new { key = "Overtime Earnings", value = overtimeEarnings },
                        //new { key = "Sick Earnings", value = sickEarnings },
                    }
                },
                new
                {
                    name = "Tax Deductions",
                    data = new object[]
                    {
                        new { key = "Federal Income Tax", value = federalIncomeTax },
                        new { key = "State Taxes", value = stateTax },
                        new { key = "Local Taxes", value = localTax },
                        new { key = "Social Security Tax", value = socialSecurityTax },
                        new { key = "Medicare Tax Withholding", value = medicareTax },
                    }
                }
            };


            var ees = await _DashboardCharts.GetEmployees(HttpContext.User.GetConum(), fromDate, toDate);
            var employeesList = ees.Select(e => new
            {
                Text = e.FirstName + " " + (String.IsNullOrEmpty(e.MiddleName) ? "" : e.MiddleName + " ") + e.LastName,
                Value = (int)e.Empnum
            }).ToList();
            employeesList.ToList().Insert(0, new { Text = "All", Value = -1 });

            var response = new
            {
                Error = new { },
                Code = 200,
                Result = new
                {
                    employees = employeesList,
                    charts,
                },
            };
            return Ok(response);
        }

        private static string FormateHours(decimal hours)
        {
            var ts = TimeSpan.FromHours((double)hours);
            return $"{((int)(ts.TotalHours)):d2}:{((int)ts.Subtract(new TimeSpan((int)ts.TotalHours, 0, 0)).TotalMinutes):d2}";
        }
    }
}
