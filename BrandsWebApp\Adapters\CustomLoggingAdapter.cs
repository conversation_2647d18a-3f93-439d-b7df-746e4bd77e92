﻿using BrandsWebApp.Helpers;
using BrandsWebApp.Middleware;
using JSNLog;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Net.Http;

namespace BrandsWebApp.Adapters
{
    public class CustomLoggingAdapter : ILoggingAdapter
    {
        private ILoggerFactory loggerFactory;

        public CustomLoggingAdapter(ILoggerFactory loggerFactory)
        {
            this.loggerFactory = loggerFactory;
        }

        public void Log(FinalLogData finalLogData)
        {
            ILogger logger = loggerFactory.CreateLogger(finalLogData.FinalLogger);
            Serilog.Context.LogContext.PushProperty("IsFELog", true);
            var url = finalLogData.LogRequest?.Headers.TryGetValue("browserhref", out string s) == true ? s : "unknown";
            Serilog.Context.LogContext.PushProperty("BrowserHref", url);
            var reactAppVersion = finalLogData.LogRequest?.Headers.TryGetValue("x-react-app-version", out string version) == true ? version : "unknown";
            Serilog.Context.LogContext.PushProperty("ReactVersion", reactAppVersion);

            Object message = LogHelper.GetContentAsObjectByContentTypeJson(finalLogData.FinalMessage, true, LogHelper.blacklist);

            switch (finalLogData.FinalLevel)
            {
                case Level.TRACE: logger.LogTrace("{message} {@JsonMessage}", finalLogData.LogRequest.Message, message); break;
                case Level.DEBUG: logger.LogDebug("{message} {@JsonMessage}", finalLogData.LogRequest.Message, message); break;
                case Level.INFO: logger.LogInformation("{message} {@JsonMessage}", finalLogData.LogRequest.Message, message); break;
                case Level.WARN: logger.LogWarning("{message} {@JsonMessage}", finalLogData.LogRequest.Message, message); break;
                case Level.ERROR: logger.LogError("{message} {@JsonMessage}", finalLogData.LogRequest.Message, message); break;
                case Level.FATAL: logger.LogCritical("{message} {@JsonMessage}", finalLogData.LogRequest.Message, message); break;
            }
        }
    }
}
