﻿using Brands.DAL;
using Brands.DataModels;
using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Employee;
using DevExpress.XtraRichEdit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Employee
{
    [ApiController]
    [Route("api/Employees/Notes")]
    [Authorize(Policy = nameof(Permission.ManageEmployees))]
    public class EmployeeNotesController : ControllerBase
    {
        private readonly EPDATAContext ePDATAContext;
        private readonly ILogger<EmployeeController> logger;

        public EmployeeNotesController(
            EPDATAContext ePDATAContext,
            ILogger<EmployeeController> logger)
        {
            this.ePDATAContext = ePDATAContext;
            this.logger = logger;
        }

        [HttpGet("{empnum:int}")]
        public async Task<IActionResult> GetNotes(int empnum)
        {
            try
            {
                logger.LogDebug("Entering GetNotes. {Empnum} ", empnum);
                var notes = await ePDATAContext.EmployeeNotes
                    .Where(note => (note.Empnum == empnum) && (note.Conum == User.GetConum())
                        && (note.ClosedDate == null || note.ClosedDate < DateTime.Now))
                    .Where(n => n.IsShow2Client && !n.IsDeleted && !n.IsTemplate)
                    .ToListAsync().ConfigureAwait(false);
                foreach (var item in notes)
                {
                    item.Description = ConvertRtfToHtml(item.Description);
                }

                return Ok(notes);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetNotes");
                throw;
            }
        }

        [HttpPost("{empnum:int}")]
        public async Task<IActionResult> SaveNotes(EmployeeNotes notes, [FromRoute] int empnum)
        {
            try
            {
                logger.LogDebug("Entering SaveNotes.");
                var crmEntry = new Crm_Base_T
                {
                    CrmId = Guid.NewGuid(),
                    Conum = User.GetConum(),
                    CreatedBy = User.GetUserId(),
                    CreatedOn = DateTime.Now,
                    ModifiedBy = User.GetUserId(),
                    ModifiedOn = DateTime.Now,
                    RowGuid = Guid.NewGuid(),
                    Category = "Employee",
                    Empnum = empnum,
                    IsShow2Client = true,
                    IsShow2Employee = false,
                    Options = 0,
                    OptionsUser = 0,
                    IsShow2Manager = false,
                    IsClient = false,
                    Workflow = "None",
                    WorkflowType = "Default"
                };

                notes.Description = ConvertHtmlToRtf(notes.Description);
                notes.Copy(crmEntry);
                ePDATAContext.EmployeeNotes.Add(crmEntry);
                await ePDATAContext.SaveChangesAsync();
                notes.Description = ConvertRtfToHtml(notes.Description);
                return Ok(notes);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in SaveNotes");
                throw;
            }
        }

        [HttpPut("{crmid:guid}")]
        public async Task<IActionResult> UpdateNotes([FromRoute] Guid crmid, EmployeeNotes notes)
        {
            try
            {
                string result = string.Empty;
                logger.LogDebug("Entering UpdateNotes.");
                var crmEntry = await ePDATAContext.EmployeeNotes.Where(n => n.CrmId == crmid && n.Conum == User.GetConum())
                    .SingleOrDefaultAsync();
                if (crmEntry is not null)
                {
                    notes.Description = ConvertHtmlToRtf(notes.Description);
                    notes.Copy(crmEntry);
                    ePDATAContext.EmployeeNotes.Update(crmEntry);
                    await ePDATAContext.SaveChangesAsync().ConfigureAwait(false);
                    notes.Description = ConvertRtfToHtml(notes.Description);
                    return Ok(notes);
                }
                else
                {
                    return NotFound();
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdateNotes");
                throw;
            }
        }

        [HttpDelete("{empnum:int}/{crmid:guid}")]
        public async Task<IActionResult> DeleteNote([FromRoute] int empnum, [FromRoute] Guid crmid)
        {
            try
            {
                string result = string.Empty;
                logger.LogDebug("Entering UpdateNotes.");
                var crmEntry = await ePDATAContext.EmployeeNotes.Where(n => n.CrmId == crmid && n.Empnum == empnum && n.Conum == User.GetConum())
                    .SingleOrDefaultAsync();
                if (crmEntry is not null)
                {
                    crmEntry.IsDeleted = true;
                    await ePDATAContext.SaveChangesAsync();
                    return Ok("Deleted Successfully");
                }
                else
                {
                    return NotFound();
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdateNotes");
                throw;
            }
        }

        private string ConvertRtfToHtml(string rtf)
        {
            using RichEditDocumentServer documentServer = new RichEditDocumentServer();
            documentServer.RtfText = rtf;
            return documentServer.HtmlText;
        }

        private string ConvertHtmlToRtf(string html)
        {
            using RichEditDocumentServer documentServer = new RichEditDocumentServer();
            documentServer.HtmlText = html;
            return documentServer.RtfText;
        }
    }
}
