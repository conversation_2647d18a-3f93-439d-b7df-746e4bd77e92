﻿using Brands.DAL;
using Brands.DataModels;
using Brands.DataModels.Enums;
using BrandsWebApp.Authentication;
using BrandsWebApp.Constants;
using BrandsWebApp.DataAccess.OnboardingData;
using BrandsWebApp.Models.Account;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.TwoFactorAuth;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Auth;
using BrandsWebApp.Services.EmployeeDeck;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/auth")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "v2")]
    public class AuthController : BaseController
    {
        private readonly BrandsAuthService brandsAuthService;
        private readonly SignInManager<BrandsAuthUser> signInManager;
        readonly UserIdentityService userIdentityService;
        readonly UserPhoneNumberService userPhoneNumberService;
        private readonly UserEventService userEventService;
        private readonly BrandsLoginService brandsLoginService;
        private readonly OnboardingDataService onboardingDataService;
        private readonly IUserEmailService userEmailService;
        private readonly EssAuthorizationService essAuthorizationService;
        private readonly EssOnboardingService essOnboardingService;

        public AuthController(BrandsAuthService brandsAuthService, SignInManager<BrandsAuthUser> signInManager,
            UserIdentityService userIdentityService, UserPhoneNumberService userPhoneNumberService, UserEventService userEventService,
            BrandsLoginService brandsLoginService, OnboardingDataService onboardingDataService,
            IServiceProvider serviceProvider, IUserEmailService userEmailService, EssAuthorizationService essAuthorizationService, EssOnboardingService essOnboardingService)
            : base(serviceProvider)
        {
            this.userPhoneNumberService = userPhoneNumberService;
            this.userEventService = userEventService;
            this.brandsLoginService = brandsLoginService;
            this.userIdentityService = userIdentityService;
            this.brandsAuthService = brandsAuthService;
            this.onboardingDataService = onboardingDataService;
            this.signInManager = signInManager;
            this.userEmailService = userEmailService;
            this.essAuthorizationService = essAuthorizationService;
            this.essOnboardingService = essOnboardingService;
        }

        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<ActionResult<UserAuthorizationResults>> Login([FromBody] LoginRequest loginRequest, [FromServices] IOptionsSnapshot<Models.LoginOptions> options)
        {
            logger.LogDebug("Entering Login. EmailAddress: {EmailAddress}", loginRequest.EmailAddress);
            if (options.Value != null && options.Value.PaydeckUnavailableMessage.IsNotNullOrWhiteSpace())
            {
                logger.LogWarning("Showing login unavailable message: {message}", options.Value.PaydeckUnavailableMessage);
                await brandsLoginService.AddLoginRequestRecordAsync(loginRequest.EmailAddress, "Paydeck unavailable", false);
                return BadRequest(options.Value.PaydeckUnavailableMessage);
            }

            var user = await userManager.FindByEmailAsync(loginRequest.EmailAddress);
            if (user == null)
            {
                logger.LogWarning("user was not found {EmailAddress}", loginRequest.EmailAddress);
                await brandsLoginService.AddLoginRequestRecordAsync(loginRequest.EmailAddress, "User not found", false);
                return Unauthorized("Invalid username or password");
            }

            if (!user.EmailConfirmed && (user.BrandsUserEmployees.Any() || user.BrandsAuthUserCpaFirms.Any()))
            {
                await userEventService.SaveUserEvent(user, "LOGIN", false, message: $"Error: Cannot sign in without a confirmed email.");
                await brandsLoginService.AddLoginRequestRecordAsync(loginRequest.EmailAddress, "Email not confirmed", false, user.Id);
                return Unauthorized("Email Not Confirmed");
            }

            var results = await signInManager.CheckPasswordSignInAsync(user, loginRequest.Password, true);
            if (!results.Succeeded)
            {
                logger.LogWarning("Failed signing in IsLockedOut: {IsLockedOut}, {EmailAddress}", results.IsLockedOut, loginRequest.EmailAddress);
                await userEventService.SaveUserEvent(user, "LOGIN", false, message: $"Error: {(results.IsLockedOut ? "Account Locked" : "Invalid password")}");
                await brandsLoginService.AddLoginRequestRecordAsync(loginRequest.EmailAddress, "Incorrect login details provided", false, user.Id);
                return Unauthorized(results.IsLockedOut ? "Account Locked" : "Invalid username or password");
            }

            Profile profile = null;
            if (loginRequest.EmployeeOnboardingCode != null)
            {
                profile = await essOnboardingService.AcceptOnboardingInviteAsync(loginRequest.EmployeeOnboardingCode, user.Id);
            }

            if (user.ShouldResetPassword)
            {
                var token = await brandsAuthService.SendPasswordResendEmailAsync(user);
                await userEventService.SaveUserEvent(user, "RESET_PASS_EMAIL_SENT", true);
                return Ok(userIdentityService.GetResetPasswordAuthorization(user, token));
            }

            var minMfaLevel = await brandsLoginService.GetMinimumMfaLevel(user, loginRequest.EssOnly);
            if (minMfaLevel >= MfaLevel.Phone && !user.PhoneNumberConfirmed)
            {
                logger.LogWarning("Redirecting to TwoFactorAuthSetup to meet minMfaLevel {EmailAddress}", loginRequest.EmailAddress);
                await userEventService.SaveUserEvent(user, "LOGIN", true, message: "Redirecting to TwoFactorAuthSetup to meet minMfaLevel");
                await brandsLoginService.AddLoginRequestRecordAsync(loginRequest.EmailAddress, "Phone 2FA setup required", true, user.Id);
                return Ok(userIdentityService.GetRedirectToTwoFactorAuth(user, minMfaLevel));
            }

            bool isBrowserRemembered = loginRequest.RememberBrowserGuid.HasValue && await brandsLoginService.IsBrowserRememberedAsync(user.Id, loginRequest.RememberBrowserGuid.Value);
            if (!isBrowserRemembered && minMfaLevel > MfaLevel.None)
            {
                logger.LogWarning("Two-Factor Authentication required. {EmailAddress}", loginRequest.EmailAddress);
                await brandsLoginService.AddLoginRequestRecordAsync(loginRequest.EmailAddress, "2FA login required", true, user.Id);
                return Ok(userIdentityService.GetRedirectToTwoFactorAuth(user, minMfaLevel));
            }

            profile = profile ?? await CreateOnboardingCompany(loginRequest.OnboardingGuid, user);
            UserAuthorizationResults userAuthResults = await brandsLoginService.GetUserAuthorizationResultsAsync(user, profile, essProfilesOnly: loginRequest.EssOnly);

            if (userAuthResults.RedirectTo == RedirectTo.Dashboard)
            {
                user.LastLoginConum = userAuthResults.Conum;
            }

            await userEventService.SaveUserEvent(user, "LOGIN", true, userAuthResults.Profile, GetLoginMessage(userAuthResults), jwtGuid: userAuthResults.JwtGuid);
            await brandsLoginService.AddLoginRequestRecordAsync(loginRequest.EmailAddress, "Login successful", true, user.Id);

            return Ok(userAuthResults);
        }

        [HttpPost("TwoFactorLogin")]
        [AllowAnonymous]
        public async Task<ActionResult<UserAuthorizationResults>> TwoFactorLoginWithOTP(TwoFactorLoginRequest twoFactorLoginRequest)
        {
            logger.LogDebug("Entering TwoFactorLogin. {@twoFactorLoginRequest}", twoFactorLoginRequest);

            var identityResults = await userIdentityService.ValidateUserIdentity(RedirectTo.TwoFactorAuth);
            if (!identityResults.IsValid)
            {
                logger.LogDebug("User is not validated");
                await brandsLoginService.AddLoginRequestRecordAsync(User.GetEmail(), "2FA unable to validate user", false, User.GetPaydeckUserId());
                return Unauthorized();
            }

            var user = await userManager.FindByIdAsync(identityResults.User.Id.ToString());
            var tokenProvider = user.TwoFactorType == TwoFactorAuthType.Email ? TokenOptions.DefaultEmailProvider : TokenOptions.DefaultPhoneProvider;
            var verificationResult = await userManager.VerifyTwoFactorTokenAsync(identityResults.User, tokenProvider, twoFactorLoginRequest.Code);
            if (!verificationResult)
            {
                string errorMessage = "Incorrect OTP code provided.";
                await userEventService.SaveUserEvent(user, "2FA_LOGIN", false, message: $"Error: Incorrect OTP code provided.");
                logger.LogWarning(errorMessage);
                await brandsLoginService.AddLoginRequestRecordAsync(user.UserName, "Incorrect 2FA code provided", true, user.Id);
                return BadRequest(errorMessage);
            }

            Profile profile = await CreateOnboardingCompany(twoFactorLoginRequest.OnboardingGuid, user);

            if (user.TwoFactorType != TwoFactorAuthType.Email)
            {
                user.PhoneNumberConfirmed = true;
            }
            await userManager.UpdateAsync(user);

            bool essProfilesOnly = identityResults.AuthenticateResult.Principal.IsEssProfilesOnly();
            UserAuthorizationResults userAuthResults = await brandsLoginService.GetUserAuthorizationResultsAsync(user, profile, rememberBrowser: twoFactorLoginRequest.RememberBrowser, essProfilesOnly: essProfilesOnly);

            if (userAuthResults.RedirectTo == RedirectTo.Dashboard)
            {
                user.LastLoginConum = userAuthResults.Conum;
            }
            await userEventService.SaveUserEvent(user, "2FA_LOGIN", true, userAuthResults.Profile, GetLoginMessage(userAuthResults), jwtGuid: userAuthResults.JwtGuid);
            await brandsLoginService.AddLoginRequestRecordAsync(user.UserName, "2FA login successful", true, user.Id);
            return Ok(userAuthResults);
        }

        [HttpPost("SwitchProfile")]
        public async Task<ActionResult<UserAuthorizationResults>> SwitchProfileAsync([FromBody] SwitchProfileRequest profile)
        {
            logger.LogDebug("Entering SwitchProfile. ProfileType: {ProfileType} ProfileId: {ProfileId}", profile.ProfileType, profile.Id);

            BrandsAuthUser user = null;
            bool essProfilesOnly;
            var result = await HttpContext.AuthenticateAsync();
            if (result.Succeeded)
            {
                user = await userManager.FindByIdAsync(result.Principal.GetUserId());
                essProfilesOnly = result.Principal.IsEssProfilesOnly();
            }
            else
            {
                var identityResults = await userIdentityService.ValidateUserIdentity(RedirectTo.IPRestricted, RedirectTo.SelectCompany, RedirectTo.CpaPortal);
                if (!identityResults.IsValid)
                {
                    logger.LogWarning("user was not validated");
                    return Unauthorized();
                }
                user = identityResults.User;
                essProfilesOnly = identityResults.AuthenticateResult.Principal.IsEssProfilesOnly();
            }

            if (user == null)
            {
                return BadRequest();
            }

            if (profile.EmployeeOnboardingCode != null)
            {
                await essOnboardingService.AcceptOnboardingInviteAsync(profile.EmployeeOnboardingCode, user.Id);
            }
            ImpersonateInfo impersonateInfo = await GetImpersonateInfoFromTokenAsync(user);
            UserAuthorizationResults userAuthResults = await brandsLoginService.GetUserAuthorizationResultsAsync(user, profile, impersonateInfo: impersonateInfo, essProfilesOnly: essProfilesOnly);
            if (userAuthResults.RedirectTo == RedirectTo.Dashboard || userAuthResults.RedirectTo == RedirectTo.Onboarding)
            {
                user.LastLoginConum = profile.Id;
            }
            await userEventService.SaveUserEvent(user, "SWITCH_COMPANY", true, userAuthResults.Profile, GetLoginMessage(userAuthResults), userAuthResults.JwtGuid);

            return Ok(userAuthResults);
        }

        [HttpGet("Profile")]
        public async Task<ActionResult<UserAuthorizationResults>> GetProfile()
        {
            try
            {
                logger.LogDebug("Entering GetProfile.");
                BrandsAuthUser user = null;
                Profile profile = null;
                bool essProfilesOnly;
                var result = await HttpContext.AuthenticateAsync();

                if (result.Succeeded)
                {
                    user = await userManager.FindByIdAsync(result.Principal.GetUserId());
                    if (User.GetProfileType().HasValue && User.GetProfileId().HasValue)
                    {
                        profile = new Profile(User.GetProfileType().Value, User.GetProfileId().Value);
                    }
                    essProfilesOnly = User.IsEssProfilesOnly();
                }
                else
                {
                    var identityResults = await userIdentityService.ValidateUserIdentity(
                        RedirectTo.IPRestricted, RedirectTo.SelectCompany, RedirectTo.CpaPortal,
                        RedirectTo.AddCpaDetails, RedirectTo.AddCpaUserName);
                    if (!identityResults.IsValid)
                    {
                        logger.LogWarning("user was not validated");
                        return Unauthorized();
                    }
                    user = identityResults.User;
                    var conum = identityResults.AuthenticateResult.Principal.GetIpRestirictedConum();
                    if (conum > 0)
                    {
                        profile = new Profile(ProfileType.Company, conum);
                    }
                    else if (identityResults.AuthenticateResult.Principal.GetProfileType().HasValue && identityResults.AuthenticateResult.Principal.GetProfileId().HasValue)
                    {
                        profile = new Profile(identityResults.AuthenticateResult.Principal.GetProfileType().Value, identityResults.AuthenticateResult.Principal.GetProfileId().Value);
                    }
                    essProfilesOnly = identityResults.AuthenticateResult.Principal.IsEssProfilesOnly();
                }

                if (user == null)
                {
                    return Unauthorized();
                }

                logger.LogDebug("In GetProfile {UserId} {@Profile}", user.Id, profile);

                ImpersonateInfo impersonateInfo = await GetImpersonateInfoFromTokenAsync(user);
                UserAuthorizationResults userAuthResults = await brandsLoginService.GetUserAuthorizationResultsAsync(user, profile, impersonateInfo, essProfilesOnly: essProfilesOnly);

                await userEventService.SaveUserEvent(user, "GET_PROFILE", true, userAuthResults.Profile, GetLoginMessage(userAuthResults), userAuthResults.JwtGuid);
                return Ok(userAuthResults);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetProfile");
                throw;
            }
        }

        [HttpPost("ConfirmAccount")]
        [HttpPost("SetUpPassword")]
        [AllowAnonymous]
        public async Task<ActionResult<UserAuthorizationResults>> SetUpPassword([FromBody] SetUpPassword setUpPassword)
        {
            logger.LogDebug("Entering SetUpPassword. {@VerifyUserRequest}", setUpPassword);
            var user = await userManager.FindByEmailAsync(setUpPassword.Email);
            if (user == null)
            {
                logger.LogWarning("User was not found. {EmailAddress}", setUpPassword.Email);
                return BadRequest();
            }
            else if (user.EmailConfirmed && user.PasswordHash.IsNotNullOrWhiteSpace())
            {
                await userEventService.SaveUserEvent(user, "CONFIRM_ACCOUNT", false, message: "Error: password already set");
                logger.LogWarning("User password was already set. {EmailAddress}", setUpPassword.Email);
                if (await userManager.CheckPasswordAsync(user, setUpPassword.Password))
                {
                    return BadRequest("Your password has already been set. Please go to the login page to log in.");
                }
                return BadRequest();
            }

            var result = await userManager.ConfirmEmailAsync(user, setUpPassword.Token);
            if (!result.Succeeded)
            {
                await userEventService.SaveUserEvent(user, "CONFIRM_ACCOUNT", false, message: "Error: invalid token");
                logger.LogWarning("ConfirmEmailAsync Failed. {@Errors}", result.Errors);
                return BadRequest(result.Errors);
            }
            else
            {
                await userEventService.SaveUserEvent(user, "CONFIRM_ACCOUNT", true);
            }

            logger.LogDebug("ConfirmEmailAsync Succeeded");

            bool setPassword = false;
            if (user.PasswordHash.IsNullOrWhiteSpace())
            {
                var passwordResults = await userManager.AddPasswordAsync(user, setUpPassword.Password);
                if (!passwordResults.Succeeded)
                {
                    await userEventService.SaveUserEvent(user, "ADD_PASSWORD", false, message: $"Error: {passwordResults.Errors.GetErrorString()}");
                    logger.LogWarning("AddPasswordAsync failed. {@Errors}", passwordResults.Errors);
                    return BadRequest(passwordResults.Errors);
                }
                else
                {
                    logger.LogDebug("AddPasswordAsync Succeeded");
                    await userEventService.SaveUserEvent(user, "ADD_PASSWORD", true);
                    setPassword = true;
                }
            }

            await CreateOnboardingCompany(setUpPassword.OnboardingGuid, user);
            await essAuthorizationService.SetupFirstTimeEssUserAsync(user);

            var minMfaLevel = await brandsLoginService.GetMinimumMfaLevel(user, false);
            if (minMfaLevel >= MfaLevel.Phone && !user.PhoneNumberConfirmed)
            {
                logger.LogWarning("Redirecting to TwoFactorAuthSetup to meet minMfaLevel {EmailAddress}", user.Email);
                await userEventService.SaveUserEvent(user, "LOGIN", true, message: "Redirecting to TwoFactorAuthSetup to meet minMfaLevel");
                await brandsLoginService.AddLoginRequestRecordAsync(user.Email, "Phone 2FA setup required", true, user.Id);
                return Ok(userIdentityService.GetRedirectToTwoFactorAuth(user, minMfaLevel));
            }

            UserAuthorizationResults userAuthResults;
            if (setPassword)
            {
                userAuthResults = await brandsLoginService.GetUserAuthorizationResultsAsync(user, null);
                await userEventService.SaveUserEvent(user, "LOGIN_AFTER_CONFIRM_ACCOUNT", true, userAuthResults.Profile, GetLoginMessage(userAuthResults), jwtGuid: userAuthResults.JwtGuid);
                await brandsLoginService.AddLoginRequestRecordAsync(userAuthResults.UserName, "Login successful", true, user.Id);
            }
            else
            {
                userAuthResults = userIdentityService.GetRedirectToLoginAuthorization();
            }

            return Ok(userAuthResults);
        }

        [HttpPost("ResendInviteEmail")]
        [AllowAnonymous]
        public async Task<ActionResult<UserAuthorizationResults>> ResendInviteEmail([FromBody] ResendInviteEmailRequest request)
        {
            logger.LogDebug("Entering ResendInviteEmail {@ResendInviteEmail}", request);
            try
            {
                var user = await userManager.FindByEmailAsync(request.Email);
                if (user == null)
                {
                    logger.LogWarning("User was not found, {Email}", request.Email);
                    return Ok();
                }
                else if (user.EmailConfirmed)
                {
                    logger.LogWarning("email has already been confirmed");
                    await userEventService.SaveUserEvent(user, "INVITE_EMAIL_RESENT", false, message: $"Error: Email has already been confirmed");
                    return BadRequest("Your email has already been confirmed. Please go to the login page to log in.");
                }

                await brandsAuthService.ResendInviteEmail(user);
            }
            catch (Exception err)
            {
                logger.LogError(err, "Error in ResendInviteEmail email: {email}", request.Email);
            }

            return Ok();
        }

        [HttpPost("ForgotPassword")]
        [AllowAnonymous]
        public async Task<ActionResult<UserAuthorizationResults>> ForgotPassword(ForgotPasswordRequest req)
        {
            logger.LogDebug("Entering ForgotPassword. Email: {Email}", req.Email);
            var email = req.Email;
            try
            {
                var user = await userManager.FindByEmailAsync(email);
                if (user == null)
                {
                    logger.LogWarning("User was not found. {email}", email);
                    return Ok();
                }

                if (!user.EmailConfirmed)
                {
                    logger.LogDebug("User not confirmed email yet.", req.Email);
                    var userAuthorizationResults = new UserAuthorizationResults { Version = Program.Version, UserType = "Brands", RedirectTo = RedirectTo.EmailNotConfirmed };
                    return Ok(userAuthorizationResults);
                }

                await brandsAuthService.SendPasswordResendEmailAsync(user);
                await userEventService.SaveUserEvent(user, "RESET_PASS_EMAIL_SENT", true);
            }
            catch (Exception err)
            {
                logger.LogError(err, "Error in ForgotPassword email: {email}", email);
            }

            return Ok();
        }

        [HttpPost("ResetPassword")]
        public async Task<ActionResult<UserAuthorizationResults>> ResetPassword(ResetPasswordRequest req)
        {
            logger.LogDebug("Entering ResetPassword.");

            BrandsAuthUser user;
            string resetPasswordToken;

            var identityResults = await userIdentityService.ValidateUserIdentity(RedirectTo.ResetPassword);
            if (identityResults.IsValid)
            {
                user = identityResults.User;
                resetPasswordToken = userIdentityService.GetResetPasswordToken(identityResults);
            }
            else
            {
                logger.LogDebug("User is not validated, will try to validate by reset password token");

                if (req.EmailAddress.IsNullOrWhiteSpace() || req.Token.IsNullOrWhiteSpace())
                {
                    logger.LogWarning("Email address or reset password token is missing.");
                    return BadRequest("Both email address and reset password token are required.");
                }

                user = await userManager.FindByEmailAsync(req.EmailAddress);
                if (user == null)
                {
                    logger.LogWarning("User was not found. Email: {Email}", req.EmailAddress);
                    return Unauthorized();
                }

                var verify = await userManager.VerifyUserTokenAsync(user, userManager.Options.Tokens.PasswordResetTokenProvider, "ResetPassword", req.Token);
                if (!verify)
                {
                    logger.LogWarning("Failed to verify token. Email: {Email}", req.EmailAddress);
                    return Unauthorized();
                }

                resetPasswordToken = req.Token;
            }

            var validationErrors = await ValidateNewPassword(user, req.NewPassword);
            if (validationErrors != null)
            {
                await userEventService.SaveUserEvent(user, "RESET_PASSWORD", false, message: $"Error: {validationErrors.GetErrorString()}");
                return BadRequest(validationErrors);
            }

            var resetPasswordResults = await userManager.ResetPasswordAsync(user, resetPasswordToken, req.NewPassword);
            if (!resetPasswordResults.Succeeded)
            {
                await userEventService.SaveUserEvent(user, "RESET_PASSWORD", false, message: $"Error: {resetPasswordResults.Errors.GetErrorString()}");
                return BadRequest(resetPasswordResults.Errors);
            }

            user.ShouldResetPassword = false;
            await userEventService.SaveUserEvent(user, "RESET_PASSWORD", true);
            return Ok(userIdentityService.GetRedirectToLoginAuthorization());
        }

        [HttpPost("ChangePassword")]
        [Authorize]
        public async Task<ActionResult<UserAuthorizationResults>> ChangePassword([FromBody] ChangePasswordRequest body)
        {
            logger.LogDebug("Entering ResetPassword.");
            if (body.NewPassword != body.ConfirmPassword)
            {
                return this.Error("PasswordMismatch", "Passwords must match.");
            }

            var validators = userManager.PasswordValidators;
            var user = await userManager.FindByNameAsync(User.GetEmail());

            #region Validation

            // TODO: Figure out how much error info the frontend actually needs
            // since the frontend should already be validating passwords.
            List<IdentityError> validationErrors = null;
            foreach (var validator in validators)
            {
                var validation = await validator.ValidateAsync(
                    userManager,
                    user,
                    body.NewPassword
                );
                if (!validation.Succeeded)
                {
                    if (validationErrors == null)
                    {
                        validationErrors = new List<IdentityError>();
                    }
                }
            }

            if (validationErrors != null && validationErrors.Count > 0)
            {
                return ApiControllerExtensions.Error(this, "InvalidPassword", "Invalid Password.");
            }

            #endregion

            var result = await userManager.ChangePasswordAsync(
                user,
                body.CurrentPassword,
                body.NewPassword
            );
            if (!result.Succeeded)
            {
                return this.Error("IncorrectPassword", "Incorrect password.");
            }

            await userEventService.SaveUserEvent(user, "CHANGE_PASSWORD", true, message: "");
            return Ok();
        }

        [Authorize]
        [HttpPost("UpdateUserProfile")]
        public async Task<ActionResult<UserAuthorizationResults>> UpdateUserProfile([FromBody] UpdateProfileRequest updateProfileRequest)
        {
            logger.LogDebug("Entering UpdateUserProfile. updateProfileRequest: {@updateProfileRequest}", updateProfileRequest);
            BrandsAuthUser user = await userManager.FindByIdAsync(HttpContext.User.GetUserId());

            if (updateProfileRequest.DismissedBannerId.HasValue)
            {
                logger.LogDebug("Updating user DismissedBannerId from: {oldValue} to {newValue}", user.DismissedBannerId, updateProfileRequest.DismissedBannerId);
                user.DismissedBannerId = updateProfileRequest.DismissedBannerId + 0.5m;
                userEventService.AddUserEvent(user, "DISMISSED_BANNER", true, null, $"Id: {updateProfileRequest.DismissedBannerId}");
            }

            var results = await userManager.UpdateAsync(user);
            if (results.Succeeded)
            {
                return Ok();
            }
            else
            {
                logger.LogError("Error in UpdateUserProfile. updateProfileRequest: {@updateProfileRequest} Errors: {@Errors}", updateProfileRequest, results.Errors);
                return BadRequest(results.Errors);
            }
        }


#if !Release
        [HttpPost("ImpersonateUser/{userId}")]
        public async Task<ActionResult<UserAuthorizationResults>> ImpersonateUser(int userId, int conum)
        {
            if (!Debugger.IsAttached)
            {
                return BadRequest();
            }

            var user = await userManager.FindByIdAsync(userId.ToString());
            var userAuthResults = await brandsLoginService.GetUserAuthorizationResultsAsync(user, new Profile(ProfileType.Company, conum));
            userAuthResults.Token = $"Bearer {userAuthResults.Token}";

            return Ok(userAuthResults);
        }
#endif

        [HttpPost("Logout")]
        public async Task<ActionResult<UserAuthorizationResults>> LogoutImpersonate([FromBody] LogoutRequest body, [FromServices] EPDATAContext ePDATAContext)
        {
            logger.LogDebug("Entering LogOut (impersonate request)");

            var impersonateRequest = await ePDATAContext.PaydeckImpersonate.FirstOrDefaultAsync(x => x.Guid == body.Guid);
            if (impersonateRequest == null)
            {
                logger.LogWarning("ImpersonateRequest with GUID {guid} was not found.", body.Guid);
                return NotFound();
            }

            if (impersonateRequest.UsedOn != null)
            {
                logger.LogWarning("ImpersonateRequest with GUID {guid} already used.", body.Guid);
                return BadRequest();
            }

            var user = await userManager.FindByIdAsync(impersonateRequest.PaydeckUserId.ToString());
            if (user == null)
            {
                logger.LogWarning("ImpersonateRequest PaydeckUser with ID {PaydeckUserId} was not found.", impersonateRequest.PaydeckUserId);
                return NotFound();
            }

            BrandsAuthUser userToCopyPermissionsFrom = null;
            BrandsUserEmployee userEmployeeToCopyPermissionsFrom = null;
            BrandsAuthUser userToImpersonate = null;
            if (impersonateRequest.ImpersonatePaydeckUserId == null)
            {
                if (!impersonateRequest.CopyPermissionsFromPaydeckUserId.HasValue)
                {
                    logger.LogWarning("ImpersonateRequest CopyPermissionsFromPaydeckUserId must have a value.");
                    return BadRequest();
                }

                if (!impersonateRequest.CopyPermissionsFromConum.HasValue)
                {
                    logger.LogWarning("ImpersonateRequest CopyPermissionsFromConum must have a value.");
                    return BadRequest();
                }

                userToCopyPermissionsFrom = await userManager.FindByIdAsync(impersonateRequest.CopyPermissionsFromPaydeckUserId.ToString());
                if (userToCopyPermissionsFrom == null)
                {
                    logger.LogWarning("ImpersonateRequest userToCopyPermissionsFrom with ID {CopyPermissionsFromPaydeckUserId} was not found.", impersonateRequest.CopyPermissionsFromPaydeckUserId);
                    return NotFound();
                }
                userEmployeeToCopyPermissionsFrom = userToCopyPermissionsFrom.BrandsUserEmployees
                    .SingleOrDefault(ue => ue.Conum == impersonateRequest.CopyPermissionsFromConum);
                if (userEmployeeToCopyPermissionsFrom == null)
                {
                    logger.LogWarning("ImpersonateRequest userEmployeeToCopyPermissionsFrom with ID {CopyPermissionsFromConum} was not found.", impersonateRequest.CopyPermissionsFromPaydeckUserId);
                    return NotFound();
                }

                logger.LogDebug("User {userId} is impersonating company {conum} using copied permissions from user {permissionsUserId}", user.Id, impersonateRequest.CopyPermissionsFromConum, impersonateRequest.CopyPermissionsFromPaydeckUserId);
            }
            else
            {
                userToImpersonate = await userManager.FindByIdAsync(impersonateRequest.ImpersonatePaydeckUserId.ToString());
                if (userToImpersonate == null)
                {
                    logger.LogWarning("ImpersonateRequest userToImpersonate with ID {ImpersonatePaydeckUserId} was not found.", impersonateRequest.ImpersonatePaydeckUserId);
                    return NotFound();
                }

                logger.LogDebug("User {userId} is impersonating user {impersonatedUserId}", user.Id, impersonateRequest.ImpersonatePaydeckUserId);
            }

            ImpersonateInfo impersonateInfo = new ImpersonateInfo
            {
                ImpersonatedByUserId = user.Id,
                ImpersonateCompanyNumber = impersonateRequest.Conum,
                CopyPermissionsFromUser = userToCopyPermissionsFrom,
                CopyPermissionsFromBrandsUserEmployee = userEmployeeToCopyPermissionsFrom,
                ImpersonateUser = userToImpersonate
            };

            UserAuthorizationResults userAuthResults = await brandsLoginService.GetUserAuthorizationResultsAsync(user, null, impersonateInfo: impersonateInfo);

            if (userToImpersonate != null)
            {
                await userEventService.SaveUserEvent(
                    userToImpersonate,
                    UserEvents.UserBeingImpersonated, true,
                    userAuthResults.Profile,
                    $"Impersonated by Front Desk user [{impersonateRequest.FdUserName}]",
                    userAuthResults.JwtGuid,
                    impersonateInfo.ImpersonatedByUserId);

                await userEventService.SaveUserEvent(
                    userToImpersonate,
                    UserEvents.Login, true,
                    userAuthResults.Profile,
                    GetLoginMessage(userAuthResults),
                    jwtGuid: userAuthResults.JwtGuid,
                    impersonatedByUserId: impersonateInfo.ImpersonatedByUserId);
            }
            else
            {
                await userEventService.SaveUserEvent(
                    user,
                    UserEvents.Login, true,
                    userAuthResults.Profile,
                    GetLoginMessage(userAuthResults),
                    jwtGuid: userAuthResults.JwtGuid);
            }

            impersonateRequest.UsedOn = DateTime.Now;
            await ePDATAContext.SaveChangesAsync();

            return Ok(userAuthResults);
        }

        async Task<List<IdentityError>> ValidateNewPassword(BrandsAuthUser user, string password)
        {
            var validators = userManager.PasswordValidators;
            // TODO: Figure out how much error info the frontend actually needs
            // since the frontend should already be validating passwords.
            List<IdentityError> validationErrors = null;
            foreach (var validator in validators)
            {
                var validation = await validator.ValidateAsync(
                    userManager,
                    user,
                    password
                );
                if (!validation.Succeeded)
                {
                    if (validationErrors == null)
                    {
                        validationErrors = new List<IdentityError>();
                    }

                    validationErrors.AddRange(validation.Errors);
                }
            }

            return validationErrors;
        }

        private async Task<Profile> CreateOnboardingCompany(string onboardingGuidString, BrandsAuthUser user)
        {
            Profile profile = null;
            try
            {
                if (!string.IsNullOrWhiteSpace(onboardingGuidString))
                {
                    logger.LogDebug("Payload has OnboardingGuid set. {OnboardingGuid}", onboardingGuidString);
                    if (Guid.TryParse(onboardingGuidString, out Guid onboardingGuid))
                    {
                        var onboarding = await onboardingDataService.GetOnboardingData(onboardingGuid);
                        if (onboarding == null)
                        {
                            return null;
                        }
                        if (onboarding.Email.ToLower() != user.Email.ToLower())
                        {
                            logger.LogWarning("Users email does not match onboarding email. UserEmail: {UserEmail} OnboardingEmail: {OnboardingEmail}", user.Email, onboarding.Email);
                            return null;
                        }
                        var conum = await onboardingDataService.HandleFirstTimeLoginOfOnboardingCompany(onboardingGuid);
                        profile = conum != null ? new Profile(ProfileType.Company, conum.Value) : profile;
                        await userEventService.SaveUserEvent(user, "ONBOARDING_COMPANY", true, message: $"Successfully linked onboarding company. OnboardingGuid: {onboardingGuid}, Conum: {conum}");
                        logger.LogDebug("Successfully linked onboarding company. OnboardingGuid: {onboardingGuid}, Conum: {conum}", onboardingGuid, conum);
                    }
                    else
                    {
                        await userEventService.SaveUserEvent(user, "ONBOARDING_COMPANY", false, message: $"Invalid OnboardingGuid: {onboardingGuidString}");
                        logger.LogWarning("Could not parse OnboardingGuid to GUID. {OnboardingGuid}", onboardingGuidString);
                    }
                }
            }
            catch (Exception ex)
            {
                await userEventService.SaveUserEvent(user, "ONBOARDING_COMPANY", false, message: $"Error creating onborading comany OnboardingGuid: {onboardingGuidString}");
                logger.LogError(ex, "Error creating company for onboardingGuid. {onboardingGuid}", onboardingGuidString);
                throw;
            }

            return profile;
        }

        private static string GetLoginMessage(UserAuthorizationResults userAuthorizationResults)
        {
            if (userAuthorizationResults.RedirectTo == RedirectTo.IPRestricted)
            {
                return $"RedirectTo: {userAuthorizationResults.RedirectTo}. Tried logging in to #{userAuthorizationResults.IpRestirictedConum} - {userAuthorizationResults.IpRestirictedCompanyName}";
            }
            if (userAuthorizationResults.RedirectTo > 0)
            {
                return $"RedirectTo: {userAuthorizationResults.RedirectTo}";
            }

            return string.Empty;
        }
    }
}