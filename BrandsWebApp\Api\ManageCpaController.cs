﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Cpa;
using BrandsWebApp.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/ManageCpa")]
    [ApiController]
    [Authorize(Policy = nameof(BrandsWebClaimTypes.CpaAccessEnabled))]
    [Authorize(Policy = nameof(Permission.ManageContactsAndUsers))]
    public class ManageCpaController : ControllerBase
    {
        private readonly CpaService cpaService;
        private readonly CompanyService companyService;
        private readonly ILogger<ManageCpaController> logger;

        public ManageCpaController(
            CpaService cpaService,
            CompanyService companyService,
            ILogger<ManageCpaController> logger)
        {
            this.cpaService = cpaService;
            this.companyService = companyService;
            this.logger = logger;
        }

        [HttpGet("CpaCompanies")]
        public async Task<IActionResult> GetCpaCompanies([FromQuery] string name)
        {
            var cpaCompanies = await cpaService.GetCpaCompaniesAsync(name);

            return Ok(cpaCompanies);
        }

        [HttpGet("UserCompanies")]
        public async Task<IActionResult> GetUserCompanies()
        {
            var companies = await companyService.GetUserCompaniesWithPermissionAsync(User.GetPaydeckUserId(), Permission.ManageContactsAndUsers);

            return Ok(companies);
        }

        [HttpPost("Invite")]
        public async Task<IActionResult> InviteCpaFromPaydeck(InviteCpaDetails inviteCpa)
        {
            await cpaService.InviteCpaFirmFromPaydeckAsync(inviteCpa);

            return NoContent();
        }

        [HttpPost]
        [AllowAnonymous]
        public async Task<IActionResult> AddCpaCompany(AddCpaCompany cpaCompany)
        {
            await cpaService.AddCpaFirmAsync(cpaCompany);

            return NoContent();
        }

        [HttpDelete("{firmId}")]
        public async Task<IActionResult> RemoveCpaFromPaydeck([FromRoute] int firmId)
        {
            await cpaService.RemoveCpaFirmFromPaydeckAsync(firmId, User.GetConum());

            return NoContent();
        }

        [HttpPost("{firmId}/ResendInvite")]
        public async Task<IActionResult> ResendCpaInviteFromPaydeck([FromRoute] int firmId)
        {
            await cpaService.ResendCpaInviteFromPaydeckAsync(firmId, User.GetConum());

            return NoContent();
        }
    }
}
