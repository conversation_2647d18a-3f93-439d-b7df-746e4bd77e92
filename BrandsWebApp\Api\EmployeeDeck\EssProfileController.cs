﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.Ess.Profile;
using BrandsWebApp.Services.EmployeeDeck;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.EmployeeDeck
{
    [Authorize(Policy = AuthPolicies.IsEssUser)]
    [ApiController]
    [Route("api/employee-deck/my/profile")]
    public class EssProfileController : ControllerBase
    {
        private readonly EssProfileService profileService;

        public EssProfileController(EssProfileService profileService)
        {
            this.profileService = profileService;
        }

        [HttpGet]
        public async Task<ActionResult<EssEmployeeProfile>> GetProfile()
        {
            var data = await profileService.GetEssEmployeeProfileAsync(User.GetEssProfileId().Value);
            return Ok(data);
        }

        [HttpGet("count")]
        public async Task<ActionResult> GetProfileCount()
        {
            var profileCount = await profileService.GetEssUserProfilesCountAsync(User.GetPaydeckUserId());
            return Ok(new
            {
                ProfileCount = profileCount,
            });
        }

        [HttpGet("list")]
        public async Task<ActionResult> GetProfilesList()
        {
            var data = await profileService.GetEssUserProfilesListAsync(User.GetPaydeckUserId());
            return Ok(new
            {
                profiles = data
            });
        }

        [HttpPut("address")]
        public async Task<ActionResult> UpdateAddress([FromBody] EmployeeAddress address)
        {
            await profileService.UpdateEmployeeAddressAsync(User.GetEssProfileId().Value, address);
            return NoContent();
        }

        [HttpPut("contact")]
        public async Task<ActionResult> UpdateAddress([FromBody] EssEmployeeProfileContact contact)
        {
            await profileService.UpdateEmployeeContactAsync(User.GetEssProfileId().Value, contact);
            return NoContent();
        }
    }
}
