﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Employee;
using BrandsWebApp.Services;
using BrandsWebApp.Services.EmployeeDeck;
using BrandsWebApp.Services.Validation.EmployeeValidation;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Employee
{
    [ApiController]
    [Route("api/Employees/New")]
    [Authorize(Policy = nameof(Permission.ManageEmployees))]
    public class NewEmployeeController : ControllerBase
    {
        private readonly ILogger<EmployeeController> logger;
        private readonly ISqlConnectionService sqlConnectionService;
        private readonly EssOnboardingService essOnboardingService;

        public NewEmployeeController(
            ILogger<EmployeeController> logger,
            ISqlConnectionService sqlConnectionService,
            EssOnboardingService essOnboardingService)
        {
            this.logger = logger;
            this.sqlConnectionService = sqlConnectionService;
            this.essOnboardingService = essOnboardingService;
        }

        [HttpPost]
        public async Task<IActionResult> NewEmployee(NewEmployee newEmployee, [FromServices] NewEmployeeValidation validations)
        {
            logger.LogDebug("Entering NewEmployee");
            try
            {
                var empType = new Dictionary<string, string> { { "Regular", "REGULAR" }, { "Contract", "CONTRACT" }, { "Household", "HOUSE" }, { "Agriculture", "AGG" } };
                if (newEmployee.EmpType.IsNotNullOrWhiteSpace() && empType.TryGetValue(newEmployee.EmpType, out string value))
                {
                    newEmployee.EmpType = value;
                }
                newEmployee.SetConum((int)User.GetConum());
                var validationResult = await validations.ValidateAsync(newEmployee);
                if (!validationResult.IsValid)
                {
                    return BadRequest(validationResult);
                }

                object paramsObject = new
                {
                    Conum = User.GetConum(),
                    newEmployee.Empnum,
                    newEmployee.FirstName,
                    newEmployee.MiddleName,
                    newEmployee.LastName,
                    newEmployee.Gender,
                    newEmployee.Ssn,
                    newEmployee.DateOfBirth,
                    newEmployee.Address,
                    newEmployee.City,
                    newEmployee.State,
                    newEmployee.Zip,
                    ContactHomeEmail = newEmployee.HomeEmail,
                    UserEmail = newEmployee.WorkEmail,
                    ContactHomePhone = newEmployee.WorkPhone,
                    ContactCellphone = newEmployee.CellPhone,
                    PhoneNumber = newEmployee.HomePhone,
                    newEmployee.Division,
                    newEmployee.Department,
                    newEmployee.W4Version,
                    newEmployee.FederalStatus,
                    newEmployee.FederalDependents,
                    newEmployee.WorkState,
                    newEmployee.ResidenceState,
                    newEmployee.UnemploymentState,
                    UnemploymentExempt = newEmployee.UnemploymentExempt ? 1 : 0,
                    newEmployee.SalaryOrRate1,
                    newEmployee.SalaryOrRate1Amount,
                    newEmployee.SalaryOrRate1EffectiveDate,
                    EmpType = newEmployee.EmpType.ToUpper(),
                    newEmployee.Tin,
                    PayFreq = newEmployee.PayFrequency,
                    newEmployee.DefaultHours
                };

                await using var con = sqlConnectionService.GetSqlConnection();
                var results = await con.QueryAsync<NewEmployeeResults>("custom.PaydeckNewEmp", paramsObject, commandType: System.Data.CommandType.StoredProcedure);
                return Ok(new
                {
                    results
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in NewEmployee");
                throw;
            }
        }

        [HttpPost("ValidateEmpnum")]
        public async Task<IActionResult> ValidateEmployeeNumber(int empnum, [FromServices] EmpValidationDataService empValidationDataService)
        {
            logger.LogDebug("Entering ValidateEmployeeNumber {empnum}", empnum);
            try
            {
                bool isNumberValid = empnum > 0 && await empValidationDataService.IsValidEmployeeNumberAsync(User.GetConum(), empnum);
                return Ok(isNumberValid);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in ValidateEmployeeNumber");
                throw;
            }
        }
    }
}
