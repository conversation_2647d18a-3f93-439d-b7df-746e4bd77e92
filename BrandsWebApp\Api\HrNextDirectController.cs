﻿using Brands.DAL;
using BrandsWebApp.Authentication;
using BrandsWebApp.Models;
using Flurl;
using Flurl.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [ApiController]
    [Route("api/HrNextDirect")]
    [Authorize(Policy = nameof(Permission.HrNextDirect))]
    public class HrNextDirectController : ControllerBase
    {
        private readonly ILogger<HrNextDirectController> logger;
        private readonly EPDATAContext ePDATAContext;
        private HrNextDirectOptions options;

        public HrNextDirectController(IOptions<HrNextDirectOptions> options, ILogger<HrNextDirectController> logger, EPDATAContext ePDATAContext)
        {
            this.options = options.Value;
            this.logger = logger;
            this.ePDATAContext = ePDATAContext;
        }

        [HttpGet]
        public async Task<IActionResult> GetSso()
        {
            logger.LogDebug("Entering GetSso");
            var coOptions = await ePDATAContext.CoOptionPayrolls.SingleOrDefaultAsync(c => c.Conum == User.GetConum());
            string roleName = string.Empty;
            if (coOptions != null && coOptions.HrNextDirect_RoleName.IsNotNullOrWhiteSpace())
            {
                roleName = string.Format(coOptions.HrNextDirect_RoleName, User.GetConum());
            }
            else
            {
                roleName = string.Format(await ePDATAContext.GetUdfValueAsync("HrNextDirect_RoleName"), User.GetConum());
            }
            logger.LogDebug("RoleName: {RoleNmae}", roleName);
            LoginResponse authLoginResponse;
            try
            {
                authLoginResponse = await options.BaseUrl.AppendPathSegment("/api/v1/auth/login").PostJsonAsync(new
                {
                    username = options.Username,
                    password = options.Password,
                    apiClientKey = options.ApiClientKey
                }).ReceiveJson<LoginResponse>();
            }
            catch (FlurlHttpException ex)
            {
                DumpError(ex);
                throw;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in /auth/login");
                throw;
            }

            ////get user id
            ///we used this to work around an issue that only inactive users were able to login. 
            //try
            //{
            //    var user = await options.BaseUrl
            //        .AppendPathSegment($"/api/v1/users/{User.GetEmail()}")
            //        .WithOAuthBearerToken(authLoginResponse.token.accessToken)
            //        .GetJsonAsync<LoginResponse>();

            //    var inactiveUser = await options.BaseUrl.AppendPathSegments("/api/v1/users", user.Id)
            //        .WithOAuthBearerToken(authLoginResponse.token.accessToken)
            //        .PatchJsonAsync(new { isActive = false})
            //        .ReceiveJson<LoginResponse>();
            //}
            //catch (FlurlHttpException ex)
            //{
            //    DumpError(ex);
            //}
            //catch (Exception ex)
            //{
            //    logger.LogError(ex, "Error in /auth/login");
            //}

            try
            {
                var post = new AuthSsoPost.Root
                {
                    username = User.GetEmail(),
                    associatedUserData = new AuthSsoPost.AssociatedUserData
                    {
                        firstName = User.GetFirstName(),
                        lastName = User.GetLastName(),
                        isActive = true,
                        isPayrollRightsGranted = false,
                        isTimeRightsGranted = false,
                        timezoneInfoID = "Eastern",
                        userCompanyEmployeeLinks = new List<AuthSsoPost.UserCompanyEmployeeLink> { new AuthSsoPost.UserCompanyEmployeeLink(User.GetConum().ToString()) },
                        userRoleLinks = new List<AuthSsoPost.UserRoleLink> { new AuthSsoPost.UserRoleLink($"{User.GetConum()}", roleName) }
                    }
                };

                var authSsoResponse = await options.BaseUrl.AppendPathSegment("/api/v1/AuthSSO")
                    .WithOAuthBearerToken(authLoginResponse.token.accessToken)
                    .PostJsonAsync(post).ReceiveJson<LoginResponse>();

                return Ok(new
                {
                    url = $"{options.BaseUrl}/LoginSSO.aspx?accessToken={authSsoResponse.token.accessToken}"
                });
            }
            catch (FlurlHttpException ex)
            {
                DumpError(ex);
                return BadRequest(await GetErrorResponse(ex));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in /api/v1/AuthSSO");
                throw;
            }
        }

        private async void DumpError(FlurlHttpException ex)
        {
            var errorMsg = $"ERORR{Environment.NewLine}Request:{Environment.NewLine}{FormatJson(ex.Call.RequestBody)}{Environment.NewLine}Response:{Environment.NewLine}{FormatJson(await ex.GetResponseStringAsync())}";
            logger.LogError(errorMsg);
        }

        private async Task<string> GetErrorResponse(FlurlHttpException flurlHttpException)
        {
            try
            {
                var json = await flurlHttpException.GetResponseStringAsync();
                var error = JsonConvert.DeserializeObject<ExceptionRootResponse>(json);
                if (error != null && error.message.IsNotNullOrWhiteSpace())
                {
                    return $"Error. {error.message}. {error.errors?.Join(',')}";
                }
                else
                {
                    return "Something went wrong.";
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetErrorResponse");
                return "Something went wrong.";
            }
        }

        private string FormatJson(string json)
        {
            try
            {
                var parsedJson = JsonConvert.DeserializeObject(json);
                return JsonConvert.SerializeObject(parsedJson, Formatting.Indented);
            }
            catch (Exception)
            {
                return json;
            }
        }

        public class Token
        {
            public string accessToken { get; set; }
            public DateTime accessTokenIssuedOn { get; set; }
            public DateTime accessTokenValidUntil { get; set; }
        }

        public class LoginResponse
        {
            public int? Id { get; set; }
            public string username { get; set; }
            public string timezoneID { get; set; }
            public string firstName { get; set; }
            public string lastName { get; set; }
            public DateTime createDate { get; set; }
            public bool active { get; set; }
            public string apiClientKey { get; set; }
            public bool apiIsAuthorized { get; set; }
            public Token token { get; set; }
        }

        public class AuthSsoPost
        {
            // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse); 
            public class UserCompanyEmployeeLink
            {
                public UserCompanyEmployeeLink(string companyIntegrationCode)
                {
                    this.CompanyIntegrationCode = companyIntegrationCode;
                }

                public int? companyID { get; set; }
                public int? vendorCompanyID { get; set; }
                public string CompanyIntegrationCode { get; set; }
                public int? employeeID { get; set; }
                public int? vendorEmployeeID { get; set; }
                public string employeeCode { get; set; }
            }

            public class UserRoleLink
            {
                public UserRoleLink(string companyIntegrationCode, string roleName)
                {
                    this.CompanyIntegrationCode = companyIntegrationCode;
                    this.RoleName = roleName;
                }

                public string RoleName { get; set; }
                public int? companyID { get; set; }
                public int? vendorCompanyID { get; set; }
                public string CompanyIntegrationCode { get; set; }
            }

            public class AssociatedUserData
            {
                public string firstName { get; set; }
                public string lastName { get; set; }
                public bool isActive { get; set; }
                public string timezoneInfoID { get; set; }
                public bool isPayrollRightsGranted { get; set; }
                public bool isTimeRightsGranted { get; set; }
                public List<UserCompanyEmployeeLink> userCompanyEmployeeLinks { get; set; }
                public List<UserRoleLink> userRoleLinks { get; set; }
            }

            public class Root
            {
                public string username { get; set; }
                public AssociatedUserData associatedUserData { get; set; }
            }
        }

        public class ExceptionRootResponse
        {
            public string status { get; set; }
            public string message { get; set; }
            public List<string> errors { get; set; }
        }
    }
}