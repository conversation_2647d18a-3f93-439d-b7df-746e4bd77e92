﻿using Brands.DAL;
using Brands.DAL.QueryTypes.PayHistory;
using BrandsWebApp.Authentication;
using BrandsWebApp.DataAccess;
using BrandsWebApp.Extensions;
using BrandsWebApp.Models.PayHistory;
using BrandsWebApp.Services;
using CsvHelper;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/EmployeePayHistory")]
    [ApiController]
    [Authorize(Policy = nameof(Permission.PayHistory))]
    public class EmployeePayHistoryController : ControllerBase
    {
        private readonly EPDATAContext dataContext;
        private readonly ILogger<EmployeePayHistoryController> logger;
        private readonly ISqlConnectionService sqlConnectionService;
        private readonly EmployeeService employeeService;

        public EmployeePayHistoryController(EPDATAContext dataContext, ILogger<EmployeePayHistoryController> logger, ISqlConnectionService sqlConnectionService, EmployeeService employeeService)
        {
            this.dataContext = dataContext;
            this.logger = logger;
            this.sqlConnectionService = sqlConnectionService;
            this.employeeService = employeeService;
        }

        [HttpGet("Employees")]
        public async Task<ActionResult> GetEmployeesAsync(bool? isActiveOnly)
        {
            var employees = await dataContext.Employees
                .Where(e => e.Conum == HttpContext.User.GetConum() && (!isActiveOnly.HasValue || !isActiveOnly.Value || !e.TermDate.HasValue))
                .Select(e => new
                {
                    e.Empnum,
                    FullName = $"{e.FirstName} {e.LastName}",
                    IsActive = !e.TermDate.HasValue
                })
                .ToListAsync();
            return Ok(employees);
        }

        [HttpGet("EmployeeList")]
        public async Task<IActionResult> GetEmployeeListAsync([FromQuery] PayHistoryFilterModel filterModel, [FromServices]EmployeePayHistoryDataService payHistoryDataService)
        {
            var result = await payHistoryDataService.GetEmployeeListAsync(filterModel);

            return Ok(result);
        }

        [HttpGet("PayHistory")]
        public async Task<ActionResult> GetPayHistory(decimal empnum, DateTime? fromDate, DateTime? toDate, bool includeActivePayrolls)
        {
            logger.LogDebug("Entering PayHistory. Empnum: {Empnum} FromDate: {FromDate} ToDate: {ToDate} IncludeActivePayrolls: {IncludeActivePayrolls}", empnum, fromDate, toDate, includeActivePayrolls);
            if (!fromDate.IsValidSqlDate())
            {
                return BadRequest($"Invalid from date. {fromDate:g}");
            }

            if(!toDate.IsValidSqlDate())
            {
                return BadRequest($"Invalid to date. {toDate:g}");
            }

            var conum = HttpContext.User.GetConum();
            var employee = dataContext.Employees
                .Include(e => e.Department_T)
                .Include(e => e.Division)
                .ThenInclude(d => d.Department_Ts)
                .Single(e => e.Empnum == empnum && e.Conum == conum);

            IEnumerable<CheckMaster> checkMasters = await employeeService.GetEmployeePayHistoryAsync(conum, empnum, fromDate, toDate, includeActivePayrolls);

            var results = new
            {
                employee.Empnum,
                FullName = $"{employee.FirstName} {employee.LastName}",
                IsActive = !employee.TermDate.HasValue,
                BasicInfo = new
                {
                    employee.StartDate,
                    Division = employee.Division?.DDIVNAME,
                    Department = employee.Department_T?.DEPT_DESC
                },
                PaymentHistory = checkMasters.Select(cm => new
                {
                    cm.CheckDate,
                    cm.CheckNumber,
                    cm.CheckType,
                    cm.Hours,
                    cm.Gross,
                    Deductions = cm.Deds,
                    cm.Taxes,
                    cm.Net,
                    cm.CheckAmount,
                    cm.Prnum,
                    cm.CheckCounter
                }),
            };
            return Ok(results);
        }

        [HttpPost("PayDetails")]
        public async Task<ActionResult> GetPayDetails([FromQuery]int empNum, [FromBody] List<SelectedPayHistories> selectedPayHistories)
        {
            logger.LogDebug("Entering GetPayDetails. Empnum: {Empnum} {@payHistories}", empNum, selectedPayHistories);
            var prnums = String.Join(",", selectedPayHistories.Select(p => p.Prnum));
            var prnumsAndChkCounters = String.Join(",", selectedPayHistories.Select(p => $"{p.Prnum}-{p.CheckCounter}"));
            logger.LogDebug("prnums: {prnums} prnumsAndChkCounters: {prnumsAndChkCounters}", prnums, prnumsAndChkCounters);
            var tblParams = new DataTable();
            tblParams.Columns.Add("Col1", typeof(decimal));
            tblParams.Columns.Add("Col2", typeof(decimal));

            foreach (var item in selectedPayHistories)
            {
                tblParams.Rows.Add(item.Prnum, item.CheckCounter);
            }

            await using (var con = sqlConnectionService.GetSqlConnection())
            {
                var conum = HttpContext.User.GetConum();

                var paysDynamicParameters = new DynamicParameters();
                paysDynamicParameters.Add("@Conum", conum);
                paysDynamicParameters.Add("@Empnum", empNum);
                paysDynamicParameters.Add("@PrChkNums", tblParams, DbType.Object, ParameterDirection.Input);
                var payDetails = await con.QueryAsync<PayDetail>("[custom].[ep_ReportPayrollPays1]", paysDynamicParameters, commandType: CommandType.StoredProcedure);
                var payDetailsSummary = payDetails
                    .GroupBy(d => new { d.Type, d.Earnings, d.Department, d.Rate, /*d.PayCode*/ })
                    .Select(grp => new
                    {
                        grp.Key.Type,
                        grp.Key.Earnings,
                        grp.Key.Department,
                        grp.Key.Rate,
                        Hours = grp.Sum(d => d.Hours),
                        Amount = grp.Sum(d => d.Amount),
                    });

                var taxesResult = await con.QueryAsync<PayrollTaxes>("[custom].[ep_ReportPayrollTaxes] @Conum, @PrNums, @Empnum", new { conum, prnums, empNum });
                var taxes = taxesResult.Where(t => prnumsAndChkCounters.Contains($"{t.prnum}-{t.chk_counter}"))
                    .GroupBy(d => new { d.descr, d.Cat })
                    .Select(grp => new Taxes
                    {
                        Category = grp.Key.Cat,
                        Description = grp.Key.descr,
                        Amount = grp.Sum(f => f.current),
                        Capped = grp.Sum(f => f.taxable),
                        Taxable = grp.Sum(f => f.Gross)
                    }).ToList();

                var dedsResult = await con.QueryAsync<PayrollDeductions>("[custom].[ep_ReportPayrollDeds] @Conum, @PrNums, @Empnum", new { conum, prnums, empNum });
                var deductions = dedsResult.Where(d => prnumsAndChkCounters.Contains($"{d.PAYROLL_NUM}-{d.CHK_COUNTER}"))
                    .GroupBy(d => d.Deductions)
                    .Select(grp => new
                    {
                        Description = grp.Key,
                        Amount = grp.Sum(f => f.Amount)
                    }).ToList();

                var result = new
                {
                    payDetailsSummary,
                    taxes, // or we can maybe group and divide ERTaxes/EETaxes, otherwise it will be divided in client side
                    deductions,
                };
                return Ok(result);
            }
        }

        [HttpGet("ExportPayHistory")]
        public async Task<ActionResult> ExportPayHistory(decimal empnum, DateTime? fromDate, DateTime? toDate, bool includeActivePayrolls)
        {
            var conum = HttpContext.User.GetConum();
            IEnumerable<CheckMaster> checkMasters = await employeeService.GetEmployeePayHistoryAsync(conum, empnum, fromDate, toDate, includeActivePayrolls);

            var stream = new MemoryStream();
            TextWriter writeFile = new StreamWriter(stream);
            using (var csv = new CsvWriter(writeFile, System.Globalization.CultureInfo.InvariantCulture))
            {
                csv.Context.RegisterClassMap<CheckMap>();
                csv.WriteRecords(checkMasters);
            }
            stream.Position = 0;
            return File(stream, "application/octet-stream", $"PayHistory_Emp.csv"); //{empnum}
        }
    }
}