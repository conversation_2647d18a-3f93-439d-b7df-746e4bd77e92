﻿using Brands.DataModels;
using BrandsWebApp.Authentication;
using BrandsWebApp.Exceptions;
using BrandsWebApp.Models.Auth;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    public class BaseController : ControllerBase
    {
        protected readonly ILogger<BaseController> logger;
        protected readonly UserManager<BrandsAuthUser> userManager;

        public BaseController(IServiceProvider serviceProvider)
        {
            this.userManager = serviceProvider.GetRequiredService<UserManager<BrandsAuthUser>>();
            this.logger = serviceProvider.GetRequiredService<ILogger<BaseController>>();
        }

        protected async Task<ImpersonateInfo> GetImpersonateInfoFromTokenAsync(BrandsAuthUser user)
        {
            if (User.GetImpersonatedByUserId().HasValue)
            {
                var impersonateInfo = new ImpersonateInfo
                {
                    ImpersonatedByUserId = User.GetImpersonatedByUserId().Value,
                    ImpersonateUser = user
                };

                return impersonateInfo;
            }

            if (User.GetUsingCopiedPermissionsFromUserId().HasValue)
            {
                BrandsAuthUser userToCopyPermissionsFrom = await userManager.FindByIdAsync(User.GetUsingCopiedPermissionsFromUserId()?.ToString());
                if (userToCopyPermissionsFrom == null)
                {
                    logger.LogWarning("userToCopyPermissionsFrom with ID {CopyPermissionsFromPaydeckUserId} was not found.", User.GetUsingCopiedPermissionsFromUserId());
                    throw new BadRequestException("Invalid data");
                }

                var userEmployeeToCopyPermissionsFrom = userToCopyPermissionsFrom.BrandsUserEmployees
                    .SingleOrDefault(ue => ue.Conum == HttpContext.User.GetUsingCopiedPermissionsFromConum());

                var impersonateInfo = new ImpersonateInfo
                {
                    ImpersonateCompanyNumber = User.GetImpersonatingCompanyNumber().Value,
                    CopyPermissionsFromUser = userToCopyPermissionsFrom,
                    CopyPermissionsFromBrandsUserEmployee = userEmployeeToCopyPermissionsFrom
                };

                return impersonateInfo;
            }

            return null;
        }
    }
}
