﻿using Brands.DAL;
using BrandsWebApp.Authentication;
using BrandsWebApp.Filters;
using BrandsWebApp.Models;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.LiveReport;
using BrandsWebApp.Services;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Reports
{
    [Route("api/{conum}/LiveReports")]
    [ApiController]
    [Authorize(Policy = AuthPolicies.RunReportsCompanyOrCpa)]
    public class LiveReportsController : ControllerBase
    {
        private readonly EPDATAContext ePDATAContext;
        private readonly ILogger<LiveReportsController> logger;
        private readonly ISqlConnectionService sqlConnectionService;
        private readonly LiveReportsService liveReportsService;

        public LiveReportsController(EPDATAContext ePDATAContext, ILogger<LiveReportsController> logger, ISqlConnectionService sqlConnectionService, LiveReportsService liveReportsService)
        {
            this.ePDATAContext = ePDATAContext;
            this.logger = logger;
            this.sqlConnectionService = sqlConnectionService;
            this.liveReportsService = liveReportsService;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            logger.LogDebug("Entering get");
            try
            {
                IQueryable<Guid> rptAccess = liveReportsService.GetReportAccess();
                var reports = await ePDATAContext.LiveReports
                    .Where(r => rptAccess.Contains(r.id))
                    .Select(r => new
                    {
                        r.id,
                        r.name,
                        r.description,
                        r.tag,
                        r.category
                    }).ToListAsync();
                return Ok(reports);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in Get");
                throw;
            }
        }

        [ServiceFilter(typeof(AccessLiveReportAsyncFilter))]
        [HttpGet("Parameters/{reportId}")]
        public async Task<IActionResult> GetParameters([FromRoute] decimal conum, [FromRoute] Guid reportId)
        {
            logger.LogDebug("Entering GetParameters {reportId}", reportId);

            try
            {
                var parameters = await ePDATAContext.LiveReportParameters
                    .Where(p => p.report_id == reportId && p.variable != "ClientId")
                    .Select(p => new ParameterResults
                    {
                        Id = p.id,
                        Name = p.name,
                        SqlType = p.sql_type,
                        Position = p.position,
                        Variable = p.variable
                    }).ToListAsync();

                foreach (var item in parameters)
                {
                    if (item.Variable == "CurrentPayrollId")
                    {
                        var list = ePDATAContext.Payrolls
                            .Where(pr => pr.CONUM == conum && pr.CHECK_DATE.HasValue && pr.CHECK_DATE.Value > DateTime.Now.AddYears(-1))
                            .OrderByDescending(pr => pr.PRNUM)
                            .Select(pr => new ReportParameterItems
                            {
                                Key = pr.PRNUM.ToString(),
                                Value = $"{pr.PRNUM} - ({pr.CHECK_DATE.Value.ToShortDateString()})"
                            }).ToList();
                        item.Items = list;
                    }
                    else if (item.Variable == "EmployeeId")
                    {
                        var list = ePDATAContext.Employees
                            .Where(e => e.Conum == conum)
                            .OrderByDescending(e => e.Empnum)
                            .Select(e => new ReportParameterItems
                            {
                                Key = $"{e.Empnum}",
                                Value = $"{e.Empnum} - {e.FirstName} {e.LastName}"
                            }).ToList();
                        item.Items = list;
                    }
                }

                return Ok(new
                {
                    Parameters = parameters,
                    Report = await ePDATAContext.LiveReports.Select(r => new { r.id, r.name }).SingleAsync(r => r.id == reportId)
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetParameters. {reportId}", reportId);
                throw;
            }
        }

        [ServiceFilter(typeof(AccessLiveReportAsyncFilter))]
        [HttpPost("Run/{reportId}")]
        public async Task<IActionResult> RunReport([FromRoute] decimal conum, [FromRoute] Guid reportId, List<ReportParameters> reportParameters)
        {
            logger.LogDebug("Entering RunReport");
            var report = await ePDATAContext.LiveReports.SingleOrDefaultAsync(r => r.id == reportId);
            if (report == null)
            {
                logger.LogWarning("Report was not found. {id}", reportId);
                return NotFound();
            }

            DynamicParameters dapperParameter = await GetReportParameters(conum, reportId, reportParameters);
            await using var con = sqlConnectionService.GetSqlConnection();
            var data = await con.QueryAsync<dynamic>(report.run_statement, dapperParameter);
            var columns = await ePDATAContext.LiveReportColumns
                .Where(c => c.ReportId == reportId && !c.MarkAsDeleted && c.Type == "R")
                .ToListAsync();
            var columnsResult = columns.GroupBy(c => c.Name).Select(g =>
            {
                var attributes = new List<ExpandoObject>();
                var column = new ExpandoObject() as IDictionary<string, Object>;
                column.Add("Name", g.Key);
                g.ToList().ForEach(i =>
                {
                    column.Add(i.AttributeName, i.AttributeValue);
                });

                return column as ExpandoObject;
            });

            return Ok(new
            {
                ReportData = data,
                Columns = columnsResult
            });
        }

        [ServiceFilter(typeof(AccessLiveReportAsyncFilter))]
        [HttpGet("{reportId}/Layout")]
        public async Task<IActionResult> GetReportLayout([FromRoute] decimal conum, [FromRoute] Guid reportId)
        {
            var reportGridLayout = await liveReportsService.GetReportLayoutAsync(conum, User.GetPaydeckUserId(), reportId);
            return Ok(reportGridLayout);
        }

        [ServiceFilter(typeof(AccessLiveReportAsyncFilter))]
        [HttpPut("{reportId}/Layout")]
        public async Task<IActionResult> UpdateReportLayout([FromRoute] decimal conum, [FromRoute] Guid reportId, [FromBody] LiveReportGridLayoutSettings reportLayout)
        {
            var reportGridLayout = await liveReportsService.UpdateReportLayoutAsync(conum, User.GetPaydeckUserId(), reportId, reportLayout);
            return Ok(reportGridLayout);
        }

        private async Task<DynamicParameters> GetReportParameters(decimal conum, Guid reportId, List<ReportParameters> reportParameters)
        {
            DynamicParameters dapperParameter = new DynamicParameters();

            var parameters = await ePDATAContext.LiveReportParameters.Where(r => r.report_id == reportId).ToListAsync();
            var conumParam = parameters.Single(p => p.variable == "ClientId");
            if (reportParameters.Any(r => r.Id == conumParam.id))
            {
                logger.LogWarning("User tried passing in conum parameter");
                throw new Exception();
            }
            dapperParameter.Add(conumParam.sql_name, conum);
            foreach (var item in reportParameters)
            {
                var tableParameter = parameters.SingleOrDefault(p => p.id == item.Id);
                if (tableParameter == null)
                {
                    logger.LogWarning("Parameter was not found in table. {id}", item.Id);
                    throw new Exception();
                }
                dapperParameter.Add(tableParameter.sql_name, item.Value);
            }

            return dapperParameter;
        }

        public class ReportParameters
        {
            public Guid Id { get; set; }
            public string Value { get; set; }
        }

        private class ParameterResults
        {
            public Guid Id { get; set; }
            public string Name { get; set; }
            public string SqlType { get; set; }
            public byte Position { get; set; }
            public string Variable { get; set; }
            public List<ReportParameterItems> Items { get; set; }
        }
    }
}
