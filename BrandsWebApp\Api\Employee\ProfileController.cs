﻿using Brands.DAL;
using Brands.DataModels;
using BrandsWebApp.Authentication;
using BrandsWebApp.Exceptions;
using BrandsWebApp.Models.Employee;
using BrandsWebApp.Models.Employee.DirectDeposit;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Employee;
using BrandsWebApp.Services.Validation.EmployeeValidation;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Employee
{
    [ApiController]
    [Route("api/Employees/{empnum}")]
    [Authorize(Policy = nameof(Permission.ManageEmployees))]
    public class EmployeeProfileController : ControllerBase
    {
        private readonly EPDATAContext ePDATAContext;
        private readonly ILogger<EmployeeController> logger;
        private readonly ISqlConnectionService sqlConnectionService;
        private readonly EmployeeService employeeService;
        private readonly EmployeeDirectDepositService employeeDirectDepositService;
        private readonly ExecuPayApiClient execuPayApiClient;
        private readonly EmployeeScheduledPaysService employeeScheduledPaysService;

        public EmployeeProfileController(
            EPDATAContext ePDATAContext,
            ILogger<EmployeeController> logger,
            ISqlConnectionService sqlConnectionService,
            EmployeeService employeeService,
            EmployeeDirectDepositService employeeDirectDepositService,
            ExecuPayApiClient execuPayApiClient,
            EmployeeScheduledPaysService employeeScheduledPaysService)
        {
            this.ePDATAContext = ePDATAContext;
            this.logger = logger;
            this.sqlConnectionService = sqlConnectionService;
            this.employeeService = employeeService;
            this.employeeDirectDepositService = employeeDirectDepositService;
            this.execuPayApiClient = execuPayApiClient;
            this.employeeScheduledPaysService = employeeScheduledPaysService;
        }

        [HttpGet]
        public async Task<IActionResult> GetEmployeeByEmpnum(int empnum, [FromServices] Services.SwipeClockApiService swipeClockApiService)
        {
            try
            {
                logger.LogDebug("Entering GetEmployeeByEmpnum. {Empnum}", empnum);
                var companyUdfs = await GetCompanyUdfs();

                Employee_T employee = await employeeService.GetEmployeeWithPayScheduleAsync(User.GetConum(), empnum);
                var empAdditionalInfo = await GetEmployeeAdditionalInfo(empnum);
                List<string> accruals = await GetCompanyAccruals();
                bool hasScheduledPayDeductionMemo = await employeeScheduledPaysService.HasScheduledPayDedMemo(User.GetConum(), empnum, employee.DivNum, employee.DeptNum);

                var basicInfo = new EmployeePersonalInfo(employee, empAdditionalInfo);
                var empRates = await employeeService.GetNextScheduledEmployeeRate(User.GetConum(), empnum);

                var payDistribution = await employeeService.GetEmployeePayDistribution(employee.Conum, empnum);
                var pay = new EmployeePay(employee, empRates);
                var job = new EmployeeJob(employee, empAdditionalInfo, hasScheduledPayDeductionMemo);
                var pto = new EmployeePto(employee, empAdditionalInfo, accruals);
                var udf = new EmployeeUdfItems(employee, companyUdfs);
                var FedW4 = new EmployeeW4(employee);
                EmployeeDirectDeposit dd = await employeeDirectDepositService.GetEmployeeDirectDepositAsync(User.GetConum(), empnum, employee);
                var root = new EmployeeRoot
                {
                    Conum = User.GetConum(),
                    Empnum = empnum,
                    EmployeePersonalInfo = basicInfo,
                    EmployeePay = pay,
                    EmployeeJob = job,
                    EmployeePTO = pto,
                    EmployeeW4 = FedW4,
                    DirectDeposit = dd,
                    EmployeeUdf = udf,
                    HasTimeAndLabor = await employeeService.GetTimeAndLaborProviderId(User.GetConum()) != null,
                    PayDistribution = payDistribution
                };
                return Ok(root);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetEmployeeByEmpnum");
                throw;
            }
        }

        [HttpPost("Terminate")]
        public async Task<IActionResult> TerminateEmployee(UpdateEmployeeStatus.TerminateEmployee terminateEmployee, [FromRoute] int empnum)
        {
            logger.LogDebug("Entering TerminateEmployee. {@terminateEmployee}", terminateEmployee);
            try
            {
                Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);
                employee.TermDate = terminateEmployee.TerminationDate;
                employee.TermReason = terminateEmployee.TerminationReason;
                await ePDATAContext.SaveChangesAsync();
                var displayValues = await GetEmployeeAdditionalInfo(empnum);
                var employeePersonalInfo = new EmployeePersonalInfo(employee, displayValues);
                await employeeService.TryTimeWorksPlusSync(User.GetConum(), employee);
                return Ok(employeePersonalInfo);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in TerminateEmployee");
                throw;
            }
        }

        [HttpPost("Rehire")]
        public async Task<IActionResult> RehireEmployee(UpdateEmployeeStatus.ReHireEmployee rehireEmployee, [FromRoute] int empnum)
        {
            logger.LogDebug("Entering RehireEmployee. {@rehireEmployee}", rehireEmployee);
            try
            {
                Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);
                employee.TermDate = null;
                employee.StartDate = rehireEmployee.StartDate;
                await ePDATAContext.SaveChangesAsync();
                var displayValues = await GetEmployeeAdditionalInfo(empnum);
                var employeePersonalInfo = new EmployeePersonalInfo(employee, displayValues);
                await employeeService.TryTimeWorksPlusSync(User.GetConum(), employee);
                return Ok(employeePersonalInfo);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in RehireEmployee");
                throw;
            }
        }

        [HttpPost("PersonalInfo")]
        public async Task<IActionResult> UpdateEmployeePersonalInfo([FromBody] EmployeePersonalInfo employeePersonalInfo, [FromRoute] int empnum, [FromServices] EmployeePersonalInfoValidation validations)
        {
            logger.LogDebug("Entering UpdateEmployeePersonalInfo, employeePersonalInfo: {@employeePersonalInfo}", employeePersonalInfo);
            try
            {
                Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);
                employeePersonalInfo.EmpType = employee.EmpType;
                employeePersonalInfo.Conum = employee.Conum;
                var validationResult = await validations.ValidateAsync(employeePersonalInfo);
                if (!validationResult.IsValid)
                {
                    return BadRequest(validationResult);
                }
                employeePersonalInfo.Copy(employee);
                await ePDATAContext.SaveChangesAsync();
                await employeeService.TryTimeWorksPlusSync(User.GetConum(), employee);
                var displayValues = await GetEmployeeAdditionalInfo(empnum);
                employeePersonalInfo = new EmployeePersonalInfo(employee, displayValues);
                return Ok(employeePersonalInfo);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdateEmployeePersonalInfo. Emp#: {Empnum}", empnum);
                throw;
            }
        }

        [HttpPost("EmployeePay")]
        public async Task<IActionResult> UpdateEmployeePay([FromBody] UpdateEmployeePay employeePay, [FromRoute] int empnum, [FromServices] UpdateEmployeePayValidation validations)
        {
            employeePay.Empnum = empnum;
            logger.LogDebug("Entering UpdateEmployeePay, employeePay: {@employeePay}", employeePay);

            var validationResult = await validations.ValidateAsync(employeePay);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }
            var result = await employeeService.UpdateEmployeePayAsync(User.GetConum(), employeePay);
            return Ok(result);
        }

        [HttpPost("EmployeePay/Rate")]
        public async Task<IActionResult> UpdateEmployeePayRate([FromBody] PayRate payRate, [FromRoute] int empnum, [FromServices] EmployeePayRateValidation validations)
        {
            logger.LogDebug("Entering UpdateEmployeePayRate. empnum: {empnum} payRate: {@payrate}", payRate, empnum);
            try
            {
                var validationResult = await validations.ValidateAsync(payRate);
                if (!validationResult.IsValid)
                {
                    return BadRequest(validationResult);
                }
                Employee_T employee = await employeeService.GetEmployeeWithPayScheduleAsync(User.GetConum(), empnum);
                var employeeRate = payRate.Copy(employee);
                if (employeeRate != null && !employee.TermDate.HasValue)
                {
                    var dupEmpRate = await ePDATAContext.EmployeeRates
                        .SingleOrDefaultAsync(er => er.Conum == employeeRate.Conum && er.Empnum == employeeRate.Empnum && er.EffectiveDate == employeeRate.EffectiveDate && er.Rate == employeeRate.Rate);
                    if (dupEmpRate != null)
                    {
                        ePDATAContext.EmployeeRates.Remove(dupEmpRate);
                    }
                    ePDATAContext.EmployeeRates.Add(employeeRate);
                }
                await ePDATAContext.SaveChangesAsync();
                await employeeService.TryTimeWorksPlusSync(User.GetConum(), employee);
                await ePDATAContext.Entry(employee).ReloadAsync();
                var empRates = await employeeService.GetNextScheduledEmployeeRate(User.GetConum(), empnum);
                var employeePay = new EmployeePay(employee, empRates);
                return Ok(employeePay);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdateEmployeePayRate");
                throw;
            }
        }

        [HttpGet("RateSchedule")]
        public async Task<IActionResult> GetRateSchedule([FromRoute] int empnum, int id, string payType)
        {
            logger.LogDebug("Entering GetRateSchedule {empnum} {id} {payType}", empnum, id, payType);
            var rates = await GetEmployeeRates(empnum, GetRateName(id, payType))
                .Select(rs => new EmployeeRateSchedule(rs))
                .ToListAsync();
            return Ok(rates);
        }

        [HttpPost("RateSchedule")]
        public async Task<IActionResult> AddEmployeeRate(int empnum, EmployeeRateSchedule payload)
        {
            try
            {
                logger.LogDebug("Entering AddEmployeeRate");
                var isDuplicate = await GetEmployeeRates(empnum, GetRateName(payload.Id, payload.PayType)).Where(r => r.EffectiveDate == payload.EffectiveDate.Date).AnyAsync();
                if (isDuplicate)
                {
                    return Ok(new
                    {
                        warning = true,
                        message = $"The New Effective Date: {payload.EffectiveDate:d} already exists."
                    });
                }

                var employee = await employeeService.GetEmployeeWithPayScheduleAsync(User.GetConum(), empnum);
                bool applyCurrentRateToEmployee = false;
                if (payload.EffectiveDate.Date <= DateTime.Today)
                {
                    //check if has raise on top of this one.
                    var nextRate = await GetEmployeeRates(empnum, payload.RateName)
                        .Where(r => r.EffectiveDate > payload.EffectiveDate && r.EffectiveDate <= DateTime.Today)
                        .OrderByDescending(r => r.EffectiveDate)
                        .FirstOrDefaultAsync();
                    if (nextRate != null && !nextRate.History.FromYesNo() && !payload.History)
                    {
                        return Ok(new
                        {
                            message = $"The New Effective Date should be bigger than: {nextRate.EffectiveDate:d}.{Environment.NewLine}Would you like to save this rate and do not apply it to the employee record(history only)?",
                            warning = true,
                            allowHistoryOnly = true
                        });
                    }

                    var currentRate = payload.GetCurrentRate(employee);
                    if (!currentRate.LastRaiseDate.HasValue && !payload.ConfirmedYesNo)
                    {
                        return Ok(new
                        {
                            message = $"Employee record does not have the last raise date set up.{Environment.NewLine}Would you like to update this rate anyway ?",
                            warning = true,
                            requireConfirmedYesNo = true
                        });
                    }
                    else
                    {
                        applyCurrentRateToEmployee = true;
                    }
                    if (currentRate.LastRaiseDate > payload.EffectiveDate && payload.EffectiveDate <= DateTime.Today)
                    {
                        payload.History = true;
                    }
                }

                EmployeeRate newEmployeeRate = payload.Copy(User.GetConum(), empnum);
                ePDATAContext.EmployeeRates.Add(newEmployeeRate);
                string message = "";
                if (!employee.TermDate.HasValue)
                {
                    if (applyCurrentRateToEmployee && newEmployeeRate.History != "YES")
                    {
                        message = ApplyRateToEmployee(empnum, newEmployeeRate, employee);
                    }
                    else
                    {
                        var lastRaise = await GetEmployeeRates(empnum, payload.RateName).Where(r => r.EffectiveDate <= DateTime.Today)
                            .OrderByDescending(er => er.EffectiveDate)
                            .FirstOrDefaultAsync();
                        if (lastRaise != null)
                        {
                            message = ApplyRateToEmployee(empnum, lastRaise, employee);
                        }
                    }
                }

                await ePDATAContext.SaveChangesAsync();
                var empRates = await employeeService.GetNextScheduledEmployeeRate(User.GetConum(), empnum);
                var pay = new EmployeePay(employee, empRates);

                if (!newEmployeeRate.History.FromYesNo())
                {
                    return Ok(new
                    {
                        message,
                        rates = await GetEmployeeRates(empnum, payload.RateName).Select(rs => new EmployeeRateSchedule(rs)).ToListAsync(),
                        EmployeePay = pay,
                    });
                }
                return Ok(new
                {
                    rates = await GetEmployeeRates(empnum, payload.RateName).Select(rs => new EmployeeRateSchedule(rs)).ToListAsync(),
                    EmployeePay = pay,
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in AddEmployeeRate");
                throw;
            }
        }

        [HttpDelete("RateSchedule")]
        public async Task<IActionResult> DeleteEmployeeRate(int empnum, EmployeeRateSchedule payload)
        {
            try
            {
                logger.LogDebug("Entering DeleteEmployeeRate");
                string message = "";
                var employeeRateToDelete = await GetEmployeeRates(empnum, payload.RateName).SingleOrDefaultAsync(er => er.EffectiveDate == payload.EffectiveDate.Date);
                if (employeeRateToDelete == null)
                {
                    return NotFound();
                }

                ePDATAContext.EmployeeRates.Remove(employeeRateToDelete);
                var employee = await employeeService.GetEmployeeWithPayScheduleAsync(User.GetConum(), empnum);
                if (!employee.TermDate.HasValue && !employeeRateToDelete.History.FromYesNo() && employeeRateToDelete.UpdatedDate.HasValue)
                {
                    message = await ApplyLastRate(empnum, payload, employee);
                }
                await ePDATAContext.SaveChangesAsync();
                var empRates = await employeeService.GetNextScheduledEmployeeRate(User.GetConum(), empnum);
                var pay = new EmployeePay(employee, empRates);
                return Ok(new
                {
                    message = message,
                    rates = await GetEmployeeRates(empnum, payload.RateName).Select(rs => new EmployeeRateSchedule(rs)).ToListAsync(),
                    EmployeePay = pay,
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in DeleteEmployeeRate");
                throw;
            }
        }

        [HttpPost("EmployeeUdf")]
        public async Task<IActionResult> UpdateEmployeeUdf([FromBody] EmployeeUdfItems employeeUdfItems, [FromRoute] int empnum)
        {
            logger.LogDebug("Entering UpdateEmployeeUdf, employeeUdfItems: {@employeeUdfItems}", employeeUdfItems);
            try
            {
                Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);
                employeeUdfItems.Copy(employee);
                await ePDATAContext.SaveChangesAsync();
                return Ok(employeeUdfItems);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdateEmployeePay");
                throw;
            }
        }

        [HttpPost("EmployeeJob")]
        public async Task<IActionResult> UpdateEmployeeJob([FromBody] EmployeeJob employeeJob, [FromRoute] int empnum, [FromServices] EmployeeJobValidation validations)
        {
            logger.LogDebug("Entering UpdateEmployeeJob, employeeJob: {@employeeJob}", employeeJob);
            try
            {
                employeeJob.Conum = User.GetConum();
                var validationResult = await validations.ValidateAsync(employeeJob);
                if (!validationResult.IsValid)
                {
                    return BadRequest(validationResult);
                }
                Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);
                if (employeeJob.ApplyDivDeptChangeToSchedules == true
                    && (employee.DivNum != employeeJob.DivNum || employee.DeptNum != employeeJob.DeptNum)
                    && (employeeJob.DivNum.HasValue && employeeJob.DeptNum.HasValue))
                {
                    await employeeScheduledPaysService.UpdateScheduledPayDedMemoAsync(User.GetConum(), empnum, employee.DivNum, employee.DeptNum, employeeJob.DivNum.Value, employeeJob.DeptNum.Value);
                }
                employeeJob.Copy(employee);
                await employeeService.UpdateEmployeeWorkersCompUdfAsync(employee, employeeJob);
                await employeeService.UpdateEmployeeJobUdfsAsync(employee, employeeJob);
                await ePDATAContext.SaveChangesAsync();
                await employeeService.TryTimeWorksPlusSync(User.GetConum(), employee);
                var displayValues = await GetEmployeeAdditionalInfo(empnum);
                bool hasScheduledPayDeductionMemo = await employeeScheduledPaysService.HasScheduledPayDedMemo(User.GetConum(), empnum, employee.DivNum, employee.DeptNum);
                employeeJob = new EmployeeJob(employee, displayValues, hasScheduledPayDeductionMemo);
                return Ok(employeeJob);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdateEmployeeJob");
                throw;
            }
        }

        [HttpPost("PayDistribution")]
        public async Task<IActionResult> UpdatePayDistribution([FromRoute] int empnum, [FromBody] EmployeePayDistributions payDistributions, [FromServices] EmployeePayDistributionsValidation validations)
        {
            var validationResult = await validations.ValidateAsync(payDistributions);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            await employeeService.UpdateEmployeePayDistributions(User.GetConum(), empnum, payDistributions);
            return NoContent();
        }

        [HttpPost("AccrualPlan")]
        public async Task<IActionResult> UpdateAccrualPlan([FromRoute] int empnum, [FromBody] EmployeeAccrualPlan employeeAccrualPlan)
        {
            logger.LogDebug("Entering UpdateAccrualPlan, EmployeeAccrualPlan: {@EmployeeAccrualPlan}", employeeAccrualPlan);
            try
            {
                Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);
                employee.EmpStatus = employeeAccrualPlan.AccrualPlan;
                await ePDATAContext.SaveChangesAsync();
                var empAdditionalInfo = await GetEmployeeAdditionalInfo(empnum);
                var pto = new EmployeePto(employee, empAdditionalInfo, await GetCompanyAccruals());
                return Ok(pto);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdateAccrualPlan");
                throw;
            }
        }

        private async Task<EmployeeAdditionalInfo> GetEmployeeAdditionalInfo(int empnum)
        {
            await using var con = sqlConnectionService.GetSqlConnection();
            return await con.QuerySingleAsync<EmployeeAdditionalInfo>(EmployeeAdditionalInfoSql, new { Conum = User.GetConum(), empnum });
        }

        private async Task<List<UdfItem>> GetCompanyUdfs()
        {
            try
            {
                await using var con = sqlConnectionService.GetSqlConnection();
                var companyUdfs = await con.QueryAsync<UdfItem>(
                            $@"SELECT 
                                description = UDF_DESCR,
	                            displayName = UDF_STRING,
								udfNumber = CONVERT(INT, REPLACE(REPLACE(UDF_DESCR, 'Employee UDF',''), 'Label (Client)',''))
                            FROM CO_UDFS_T 
                            WHERE CONUM = @conum
							AND [UDF_DESCR] LIKE N'Employee UDF%Label (Client)'
							AND ISNUMERIC(REPLACE(REPLACE(UDF_DESCR, 'Employee UDF',''), 'Label (Client)','')) = 1",
                            new { Conum = User.GetConum() });
                return companyUdfs.ToList();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetCompanyUdfs");
                throw;
            }
        }

        private IQueryable<EmployeeRate> GetEmployeeRates(int empnum, string rate)
        {
            var employeeRates = ePDATAContext.EmployeeRates
                .Where(er => er.Conum == User.GetConum() && er.Empnum == empnum && er.Rate == rate)
                .OrderBy(er => er.EffectiveDate);
            return employeeRates;
        }

        private string GetRateName(int id, string payType)
        {
            if (id == 0 && payType == "Salary")
            {
                return "Salary";
            }
            else if (payType == "Rate")
            {
                switch (id)
                {
                    case 1: return "Rate 1";
                    case 2: return "Rate 2";
                    case 3: return "Rate 3";
                    default:
                        throw new BadRequestException($"Invalid id: {id}");
                }
            }
            else
            {
                throw new BadRequestException($"Invalid Employee Rate");
            }
        }

        private async Task<string> ApplyLastRate(int empnum, EmployeeRateSchedule payload, Employee_T employee)
        {
            var previousRate = await GetEmployeeRates(empnum, payload.RateName)
                                    .Where(er => er.EffectiveDate != payload.EffectiveDate.Date && er.EffectiveDate <= DateTime.Today)
                                    .OrderByDescending(er => er.EffectiveDate)
                                    .FirstOrDefaultAsync();
            if (previousRate != null)
            {
                return ApplyRateToEmployee(empnum, previousRate, employee);
            }
            else if (payload.EffectiveDate.Date <= DateTime.Today)
            {
                return ClearEmployeeRate(empnum, payload, employee);
            }
            return String.Empty;
        }

        private string ApplyRateToEmployee(int empnum, EmployeeRate rateToApply, Employee_T employee)
        {
            string message = "";
            rateToApply.History = false.ToYesNo();
            rateToApply.UpdatedDate = DateTime.Now;
            var currentRate = EmployeeRateSchedule.GetCurrentRate(employee, rateToApply);
            if (currentRate.Amount != rateToApply.Amount || currentRate.LastRaiseDate?.Date != rateToApply.EffectiveDate.Date)
            {
                if (currentRate.LastRaiseDate.HasValue && rateToApply.timestamp != null)
                {
                    message = $"{rateToApply.Rate} in employee record  has been rolled back to the previous value: {rateToApply.Amount}";
                }
                else
                {
                    message = $"{rateToApply.Rate} in employee record  has been updated to the new value: {rateToApply.Amount}";

                }
                var payRate = new PayRate(rateToApply);
                payRate.Copy(employee);
            }

            return message;
        }

        private string ClearEmployeeRate(int empnum, EmployeeRateSchedule payload, Employee_T employee)
        {
            string message = "";
            var currentRate = payload.GetCurrentRate(employee);
            if (currentRate.Amount.HasValue || currentRate.LastRaiseDate.HasValue)
            {
                message = $"{payload.RateName} in employee record  has been rolled back to the previous value: Nothing";
                var payRate = new PayRate { Amount = null, EffectiveDate = null, Id = payload.Id, PayType = payload.PayType };
                payRate.Copy(employee);
            }

            return message;
        }

        private async Task<List<string>> GetCompanyAccruals()
        {
            return await ePDATAContext.AccrualMaters.Where(a => a.Conum == User.GetConum()).Select(a => a.EmpStatus).Distinct().ToListAsync();
        }

        const string EmployeeAdditionalInfoSql = @"
--DECLARE @CoNum DECIMAL(6,0) = 2073, @empnum DECIMAL(6,0) = 1
            SELECT 
	            et.CONUM,
	            et.EMPNUM,
                DivisionName = (SELECT TOP 1 CONCAT(D.DDIVNUM, ' - ', D.DDIVNAME) FROM DIVISION_T d WHERE D.CONUM = et.CONUM AND D.DDIVNUM = et.DIVNUM),
	            DepartmentName = (SELECT TOP 1 CONCAT(d.DEPTNUM, ' - ', D.DEPT_DESC) FROM DEPARTMENT_T d WHERE d.CONUM = et.CONUM AND d.DEPTNUM = et.DEPTNUM AND d.DIVNUMD = et.DIVNUM),
	            Job1Active = CASE WHEN cj.active = 'YES' THEN 1 ELSE 0 END,
                Job2Active = CASE WHEN j2.Active_Job = 'YES' THEN 1 ELSE 0 END,
                Job3Active = CASE WHEN j3.Active_Job = 'YES' THEN 1 ELSE 0 END,
                Job4Active = CASE WHEN j4.Active_Job = 'YES' THEN 1 ELSE 0 END,
                Job5Active = CASE WHEN j5.Active_Job = 'YES' THEN 1 ELSE 0 END,
	            ManagedByDisplay = (SELECT TOP 1 
		            ISNULL(F_NAME,'') + ' ' + ISNULL(M_NAME, '') + CASE WHEN ISNULL(M_NAME,'') = '' THEN '' ELSE ' ' END + ISNULL(L_NAME,'') 
	            FROM EMPLOYEE_T WHERE conum = et.CONUM AND EMPNUM = et.managed_by),
	            GenderDisplay = CASE WHEN GENDER = 0 THEN	'Male' WHEN GENDER = 1 THEN 'Female' ELSE 'UnSpecified' END,
				DivisionFieldName = custom.PaydeckCoUdfString(@CoNum, 'Level 1 Label - Division (Client)', 'Division'),
				DepartmentFieldName = custom.PaydeckCoUdfString(@CoNum, 'Level 2 Label - Department (Client)', 'Department'),
				Job1FieldName = custom.PaydeckCoUdfString(@CoNum, 'Level 3 Label - Job 1 (Client)', ''),
				Job2FieldName = custom.PaydeckCoUdfString(@CoNum, 'Level 4 Label - Job 2 (Client)', ''),
				Job3FieldName = custom.PaydeckCoUdfString(@CoNum, 'Level 5 Label - Job 3 (Client)', ''),
				Job4FieldName = custom.PaydeckCoUdfString(@CoNum, 'Level 6 Label - Job 4 (Client)', ''),
				Job5FieldName = custom.PaydeckCoUdfString(@CoNum, 'Level 7 Label - Job 5 (Client)', ''),
				OverrideDefaultWC = (SELECT MAX(eut.UDF_NUMBER) FROM EE_UDFS_T eut WHERE eut.CONUM = @CoNum AND eut.EMPNUM = @empnum AND eut.UDF_DESCR = '%DefaultWC'),
				eu.SickEarned, eu.SickAvailable, eu.SickUsed, eu.VacationEarned, eu.VacationAvailable, eu.VacationUsed, eu.PersonalEarned, eu.PersonalAvailable, eu.PersonalUsed
            FROM EMPLOYEE_T et 
	            LEFT JOIN CO_JOBS_T cj ON et.CONUM = cj.conum AND et.job1 = cj.job_id
	            LEFT JOIN Job2_T j2 ON et.CONUM = j2.conum AND et.job2 = j2.Job_Code
	            LEFT JOIN Job3_T j3 ON et.CONUM = j3.conum AND et.job3 = j3.Job_Code
	            LEFT JOIN Job4_T j4 ON et.CONUM = j4.conum AND et.job4 = j4.Job_Code
	            LEFT JOIN Job5_T j5 ON et.CONUM = j5.conum AND et.job5 = j5.Job_Code
				LEFT JOIN (SELECT TOP 1
							eu.CONUM, eu.EMPNUM,
							ISNULL((SELECT UDF_NUMBER FROM EE_UDFS WHERE (CONUM = @CoNum) AND (EMPNUM = @EmpNum) AND (UDF_DESCR = 'SickEarned')), 0) AS SickEarned,
							ISNULL((SELECT UDF_NUMBER FROM EE_UDFS AS EE_UDFS_2 WHERE (CONUM = @CoNum) AND (EMPNUM = @EmpNum) AND (UDF_DESCR = 'SickAvailable')), 0) AS SickAvailable,
							ISNULL((SELECT UDF_NUMBER FROM EE_UDFS AS EE_UDFS_9 WHERE (CONUM = @CoNum) AND (EMPNUM = @EmpNum) AND (UDF_DESCR = 'SickUsed')), 0) AS SickUsed,
							ISNULL((SELECT UDF_NUMBER FROM EE_UDFS AS EE_UDFS_8 WHERE (CONUM = @CoNum) AND (EMPNUM = @EmpNum) AND (UDF_DESCR = 'VacationEarned')), 0) AS VacationEarned,
							ISNULL((SELECT UDF_NUMBER FROM EE_UDFS AS EE_UDFS_7 WHERE (CONUM = @CoNum) AND (EMPNUM = @EmpNum) AND (UDF_DESCR = 'VacationAvailable')), 0) AS VacationAvailable,
							ISNULL((SELECT UDF_NUMBER FROM EE_UDFS AS EE_UDFS_6 WHERE (CONUM = @CoNum) AND (EMPNUM = @EmpNum) AND (UDF_DESCR = 'VacationUsed')), 0) AS VacationUsed,
							ISNULL((SELECT UDF_NUMBER FROM EE_UDFS AS EE_UDFS_5 WHERE (CONUM = @CoNum) AND (EMPNUM = @EmpNum) AND (UDF_DESCR = 'PersonalEarned')), 0) AS PersonalEarned,
							ISNULL((SELECT UDF_NUMBER FROM EE_UDFS AS EE_UDFS_4 WHERE (CONUM = @CoNum) AND (EMPNUM = @EmpNum) AND (UDF_DESCR = 'PersonalAvailable')), 0) AS PersonalAvailable,
							ISNULL((SELECT UDF_NUMBER FROM EE_UDFS AS EE_UDFS_3 WHERE (CONUM = @CoNum) AND (EMPNUM = @EmpNum) AND (UDF_DESCR = 'PersonalUsed')), 0) AS PersonalUsed
						FROM EE_UDFS AS eu
						WHERE eu.conum = @CoNum AND eu.empnum = @EmpNum
				) eu ON eu.CONUM = @CoNum AND eu.EMPNUM = @empnum
            WHERE et.CONUM = @conum AND et.EMPNUM = @empnum";
    }
}
