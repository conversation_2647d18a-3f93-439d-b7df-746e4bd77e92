﻿using Brands.DataModels;
using Brands.DataModels.Enums;
using BrandsWebApp.Authentication;
using BrandsWebApp.Helpers;
using BrandsWebApp.Models.Account;
using BrandsWebApp.Models.TwoFactorAuth;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Auth;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/TwoFactorAuth")]
    [ApiController]
    [Authorize]
    public class TwoFactorAuthController : ControllerBase
    {
        private readonly UserManager<BrandsAuthUser> userManager;
        private readonly UserPhoneNumberService userPhoneNumberService;
        private readonly UserEventService userEventService;
        private readonly ILogger<TwoFactorAuthController> logger;
        private readonly ErrorMessageHelper errorMessageHelper;
        private readonly UserIdentityService userIdentityService;
        private readonly BrandsLoginService brandsLoginService;
        private readonly IUserEmailService userEmailService;

        public TwoFactorAuthController(
            UserManager<BrandsAuthUser> userManager,
            UserPhoneNumberService userPhoneNumberService,
            UserEventService userEventService,
            ILogger<TwoFactorAuthController> logger,
            ErrorMessageHelper errorMessageHelper,
            UserIdentityService userIdentityService,
            BrandsLoginService brandsLoginService,
            IUserEmailService userEmailService)
        {
            this.userManager = userManager;
            this.userPhoneNumberService = userPhoneNumberService;
            this.userEventService = userEventService;
            this.logger = logger;
            this.errorMessageHelper = errorMessageHelper;
            this.userIdentityService = userIdentityService;
            this.brandsLoginService = brandsLoginService;
            this.userEmailService = userEmailService;
        }

        [HttpGet]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<TwoFactorAuthDetails>> GetTwoFactorAuthDetails()
        {
            logger.LogDebug("Entering GetTwoFactorAuthDetails.");
            try
            {
                var user = await userManager.FindByIdAsync(HttpContext.User.GetUserId());
                var minMfaLevelCompany = await brandsLoginService.GetMinimumMfaLevel(user, HttpContext.User.IsEssProfilesOnly());

                var minMfaLevelUser = MfaLevel.None;

                if (user.TwoFactorType == TwoFactorAuthType.Email)
                {
                    minMfaLevelUser = MfaLevel.Email;
                }
                else if (user.TwoFactorType == TwoFactorAuthType.Call || user.TwoFactorType == TwoFactorAuthType.Sms)
                {
                    minMfaLevelUser = MfaLevel.Phone;
                }

                var result = new TwoFactorAuthDetails
                {
                    Enabled = user.TwoFactorEnabled,
                    AuthType = (int?)user.TwoFactorType,
                    PhoneNumber = user.PhoneNumber,
                    Email = user.Email,
                    MinMfaLevel = (MfaLevel)Math.Max((int)minMfaLevelCompany, (int)minMfaLevelUser),
                };
                return Ok(result);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetTwoFactorAuthDetails");
                var user = await userManager.FindByIdAsync(User.GetUserId());
                await userEventService.SaveUserEvent(user, "2FA_DETAILS", false, message: $"Error in GetTwoFactorAuthDetails: {ex.Message}");
                return BadRequest();
            }
        }

        [HttpPost("SendCode")]
        [AllowAnonymous]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<IActionResult> SendTwoFactorAuthCode(SetUpTwoFactorAuth setUpTwoFactorAuth)
        {
            logger.LogDebug
            (
                "Entering SendCode. Auth type: {TwoFactorAuthType}, Phone number: {PhoneNumber}",
                setUpTwoFactorAuth.TwoFactorAuthType, setUpTwoFactorAuth.PhoneNumber
            );

            BrandsAuthUser user = await AuthenticateUser(setUpTwoFactorAuth);
            if (user == null)
            {
                return Unauthorized();
            }

            if (setUpTwoFactorAuth.TwoFactorAuthType != TwoFactorAuthType.Email)
            {
                if (setUpTwoFactorAuth.PhoneNumber.IsNotNullOrWhiteSpace() && user.PhoneNumber != setUpTwoFactorAuth.PhoneNumber)
                {
                    var (success, error) = await TrySwitchPhoneNumber(user, setUpTwoFactorAuth);
                    if (!success)
                    {
                        return BadRequest(error);
                    }
                }
            }

            try
            {
                if (await userManager.UpdateSecurityStampAsync(user) != IdentityResult.Success)
                {
                    logger.LogWarning("Failed to update the security stamp. EmailAddress: {EmailAddress}", user.Email);
                    return BadRequest();
                }

                var tokenProvider = setUpTwoFactorAuth.TwoFactorAuthType == TwoFactorAuthType.Email ? TokenOptions.DefaultEmailProvider : TokenOptions.DefaultPhoneProvider;
                var token = await userManager.GenerateTwoFactorTokenAsync(user, tokenProvider);

                if (setUpTwoFactorAuth.TwoFactorAuthType == TwoFactorAuthType.Email)
                {
                    await userEmailService.SendOtpEmailAsync(token, user.Email);
                    await userEventService.SaveUserEvent(user, "2FA_OTP_SENT", true, message: $"2FA OTP sent to  {user.Email}");
                }
                else
                {
                    bool sendViaCall = setUpTwoFactorAuth.TwoFactorAuthType == TwoFactorAuthType.Call;
                    var otpSenderProvider = await userPhoneNumberService.SendOtpCode(user, token, user.PhoneNumber, sendViaCall);
                    await userEventService.SaveUserEvent(user, "2FA_OTP_SENT", true, message: $"2FA OTP sent to {user.PhoneNumber} via {user.TwoFactorType} ({otpSenderProvider})");
                }

                user.TwoFactorType = setUpTwoFactorAuth.TwoFactorAuthType;
                await userManager.UpdateAsync(user);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in SendCode. Email{Email}", user.Email);
                await userEventService.SaveUserEvent(user, "LOGIN", false, message: $"Error in 2FA Login, OTP sent to {user.PhoneNumber}");
                return BadRequest();
            }
            return NoContent();
        }

        [HttpPost("VerifyCode")]
        [AllowAnonymous]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<UserAuthorizationResults>> VerifyTwoFactorAuthCode(VerifyCode verifyCode)
        {
            logger.LogDebug("Entering VerifyCode.");

            BrandsAuthUser user = null;
            var authResult = await HttpContext.AuthenticateAsync();
            if (authResult.Succeeded)
            {
                user = await userManager.FindByIdAsync(authResult.Principal.GetUserId());
            }
            else
            {
                var identityResults = await userIdentityService.ValidateUserIdentity(RedirectTo.TwoFactorAuthSetup);
                if (!identityResults.IsValid)
                {
                    logger.LogWarning("User was not validated");
                    return Unauthorized();
                }
                user = identityResults.User;
            }

            if (user.TwoFactorType == null)
            {
                string message = "Two-factor authentication is not set up.";
                logger.LogWarning(message + "User ID: {userId}", user.Id);
                await userEventService.SaveUserEvent(user, "2FA_SETUP_VERIFY", false, message: message);
                return BadRequest(message);
            }

            var tokenProvider = user.TwoFactorType == TwoFactorAuthType.Email ? TokenOptions.DefaultEmailProvider : TokenOptions.DefaultPhoneProvider;
            var verificationResult = await userManager.VerifyTwoFactorTokenAsync(user, tokenProvider, verifyCode.Code);
            if (!verificationResult)
            {
                string errorMessage = $"Incorrect OTP code provided. ({verifyCode.Code})";
                await userEventService.SaveUserEvent(user, "2FA_SETUP_VERIFY", false, message: errorMessage);
                logger.LogWarning(errorMessage);
                return BadRequest(errorMessage);
            }

            if (user.TwoFactorType != TwoFactorAuthType.Email)
            {
                user.PhoneNumberConfirmed = true;
            }

            user.TwoFactorEnabled = true;
            await userManager.UpdateAsync(user);
            await userEventService.SaveUserEvent(user, "2FA_SETUP_OTP_CODE_VERIFIED", true);

            UserAuthorizationResults userAuthResults;
            try
            {
                userAuthResults = await brandsLoginService.GetUserAuthorizationResultsAsync(user, null);
            }
            catch (UnauthorizedAccessException e)
            {
                logger.LogWarning("GetUserAuthorizationResultsAsync. {Unauthorized}", e.Message);
                await userEventService.SaveUserEvent(user, "VERIFY_2FA_CODE", false, null, e.Message);
                return Unauthorized(e.Message);
            }

            if (userAuthResults.RedirectTo == RedirectTo.Dashboard)
            {
                user.LastLoginConum = userAuthResults.Conum;
            }

            return Ok(userAuthResults);
        }

        private async Task<BrandsAuthUser> AuthenticateUser(SetUpTwoFactorAuth setUpTwoFactorAuth)
        {
            BrandsAuthUser user = null;
            var authResult = await HttpContext.AuthenticateAsync();
            if (authResult.Succeeded)
            {
                return await userManager.FindByIdAsync(authResult.Principal.GetUserId());
            }
            else
            {
                var identityResults = await userIdentityService.ValidateUserIdentity(RedirectTo.TwoFactorAuth, RedirectTo.TwoFactorAuthSetup);
                if (!identityResults.IsValid)
                {
                    logger.LogWarning("User was not validated");
                    return null;
                }

                user = identityResults.User;
                if (identityResults.AuthenticateResult.Principal.GetRedirectTo() == RedirectTo.TwoFactorAuth && user.PhoneNumber != null && user.PhoneNumberConfirmed)
                {
                    setUpTwoFactorAuth.PhoneNumber = user.PhoneNumber;
                }
            }
            return user;
        }

        private async Task<(bool success, string error)> TrySwitchPhoneNumber(BrandsAuthUser user, SetUpTwoFactorAuth setUpTwoFactorAuth)
        {
            var validationResults = userPhoneNumberService.IsPhoneNumberValid(setUpTwoFactorAuth.PhoneNumber);
            if (!validationResults.IsValid)
            {
                await userEventService.SaveUserEvent(user, "2FA_SETUP", false, message: $"Invalid Phone Number ({setUpTwoFactorAuth.PhoneNumber}). {validationResults.Message}");
                logger.LogWarning(validationResults.Message);
                return (false, validationResults.Message);
            }
            var oldPhoneNumber = user.PhoneNumber;
            var result = await userManager.SetPhoneNumberAsync(user, validationResults.Number);
            if (!result.Succeeded)
            {
                await userEventService.SaveUserEvent(user, "2FA_SETUP", false, message: $"Error in SetPhoneNumberAsync ({validationResults.Number}). {errorMessageHelper.GetErrorString(result.Errors)}");
                logger.LogWarning(errorMessageHelper.GetErrorString(result.Errors));
                return (false, (errorMessageHelper.GetErrorString(result.Errors)));
            }
            await userEventService.SaveUserEvent(user, "2FA_SETUP_SWITCHED_PHONE_NUMBER", true, message: $"Switched Phone Number From: ({oldPhoneNumber}) > ({setUpTwoFactorAuth.PhoneNumber})");
            return (true, null);
        }
    }
}
