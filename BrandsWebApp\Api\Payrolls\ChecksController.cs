﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Filters;
using BrandsWebApp.Models.Payroll.Check;
using BrandsWebApp.Models.Payroll.Settings;
using BrandsWebApp.Services.Payroll;
using BrandsWebApp.Services.Validation.CheckValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RpcMessagingModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Payrolls
{
    [Route("api/Payrolls/{payrollNumber}/Checks")]
    [ApiController]
    [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
    public class ChecksController : ControllerBase
    {
        private readonly CreatePayrollService createPayrollService;
        private readonly PayrollChecksService checksService;
        private readonly ExecupayApiService execupayApiService;
        private readonly TaxesService taxesService;
        private readonly CheckLinesService checkLinesService;
        private readonly CheckDistributionService checkDistributionService;
        private readonly PayrollSettingsService payrollSettingsService;

        public ChecksController(
            CreatePayrollService createPayrollService,
            PayrollChecksService checksService,
            ExecupayApiService execupayApiService,
            TaxesService taxesService,
            CheckLinesService checkLinesService,
            CheckDistributionService checkDistributionService,
            PayrollSettingsService payrollSettingsService)
        {
            this.createPayrollService = createPayrollService;
            this.checksService = checksService;
            this.execupayApiService = execupayApiService;
            this.taxesService = taxesService;
            this.checkLinesService = checkLinesService;
            this.checkDistributionService = checkDistributionService;
            this.payrollSettingsService = payrollSettingsService;
        }

        /// <summary>
        /// Adds check to payroll
        /// </summary>
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpPost]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<IActionResult> CreateChecks([FromRoute] decimal payrollNumber, [FromBody] CreateChecksPayload addChecks)
        {
            await createPayrollService.CreateChecksAsync(User.GetConum(), payrollNumber, addChecks);
            return Accepted();
        }

        /// <summary>
        /// Deletes payroll checks
        /// </summary>
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpDelete]
        public async Task<IActionResult> DeleteChecks([FromRoute] decimal payrollNumber, [FromQuery] string[] checkKeys)
        {
            var updatedChecks = await checksService.DeleteChecksAsync(User.GetConum(), payrollNumber, User.GetPaydeckUserId(), checkKeys);
            return Ok(updatedChecks);
        }

        /// <summary>
        /// Updates checks
        /// </summary>
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpPut]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<IEnumerable<Check>>> UpdateChecks([FromRoute] decimal payrollNumber, [FromBody] UpdateChecks checks, [FromServices] UpdateChecksValidation validations)
        {
            var validationResult = await validations.ValidateAsync(checks);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            var updatedChecks = await checksService.UpdateChecksAsync(User.GetConum(), payrollNumber, checks.Checks);
            return Ok(updatedChecks);
        }

        /// <summary>
        /// Updates checks net override
        /// </summary>
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpPut("NetOverride")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<IEnumerable<Check>>> UpdateChecksNetOverride([FromRoute] decimal payrollNumber, [FromBody] UpdateChecksNetOverride checks, [FromServices] UpdateChecksNetOverrideValidation validations)
        {
            var validationResult = await validations.ValidateAsync(checks);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            var updatedChecks = await checksService.UpdateChecksNetOverrideAsync(User.GetConum(), payrollNumber, checks.Checks);
            return Ok(updatedChecks);
        }


        /// <summary>
        /// Removes check from payroll
        /// </summary>
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpDelete("{checkKey}")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<IEnumerable<Check>>> RemoveCheck([FromRoute] decimal payrollNumber, [FromRoute] string checkKey)
        {
            var checks = await checksService.DeleteCheckAsync(User.GetConum(), checkKey);
            return Ok(checks);
        }

        /// <summary>
        /// Updates check
        /// </summary>
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [ServiceFilter(typeof(UpdatePayrollCheckAsyncFilter))]
        [HttpPatch("{checkKey}")]
        public async Task<IActionResult> UpdateCheck([FromRoute] string checkKey, [FromBody] UpdateCheck check, [FromQuery] string checkUpdateGuid, [FromServices] UpdateCheckValidation validations)
        {
            check.Key = checkKey;
            var validationResult = await validations.ValidateAsync(check);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            var updatedChecks = await checksService.UpdateCheckAsync(User.GetConum(), check);
            return Ok(updatedChecks);
        }

        /// <summary>
        /// Distributes checks
        /// </summary>
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpPost("Distribution")]
        public async Task<IActionResult> DistributeChecks([FromRoute] decimal payrollNumber, [FromBody] DistributeChecks distributeChecks)
        {
            var updatedChecks = await checkDistributionService.DistributeChecksAsync(User.GetConum(), payrollNumber, distributeChecks.CheckKeys);
            return Ok(updatedChecks);
        }

        /// <summary>
        /// Calculates check
        /// </summary>
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [ServiceFilter(typeof(UpdatePayrollCheckAsyncFilter))]
        [HttpPost("{checkKey}")]
        public async Task<IActionResult> CalculateCheck([FromRoute] string checkKey, [FromBody] CalculateCheck calculateCheck, [FromQuery] string checkUpdateGuid)
        {
            var key = new CheckKey(checkKey);
            if (key.Conum != User.GetConum())
            {
                return Forbid();
            }

            await execupayApiService.CalculateCheckAsync(key, false, calculateCheck.IgnoreMissingData);
            return NoContent();
        }

        /// <summary>
        /// Gets check taxes
        /// </summary>
        [HttpGet("{checkKey}/Taxes")]
        public async Task<IActionResult> GetCheckTaxes([FromRoute] string checkKey)
        {
            var taxes = await taxesService.GetCheckTaxesAsync(User.GetConum(), checkKey);
            return Ok(taxes);
        }

        /// <summary>
        /// Overrides check taxes
        /// </summary>
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [ServiceFilter(typeof(UpdatePayrollCheckAsyncFilter))]
        [HttpPut("{checkKey}/Taxes")]
        public async Task<IActionResult> OverrideCheckTaxes([FromRoute] string checkKey, [FromBody] OverrideCheckTaxes overrideCheckTaxes, [FromQuery] string checkUpdateGuid)
        {
            overrideCheckTaxes.CheckKey = checkKey;
            var updatedData = await taxesService.OverrideCheckTaxesAsync(User.GetConum(), overrideCheckTaxes);
            return Ok(updatedData);
        }

        /// <summary>
        /// Clear check taxes overrides
        /// </summary>
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [ServiceFilter(typeof(UpdatePayrollCheckAsyncFilter))]
        [HttpDelete("{checkKey}/Taxes/Override")]
        public async Task<IActionResult> ClearCheckTaxesOverride([FromRoute] string checkKey, [FromQuery] string checkUpdateGuid)
        {
            var updatedData = await taxesService.ClearCheckTaxesOverrideAsync(User.GetConum(), checkKey);
            return Ok(updatedData);
        }

        /// <summary>
        /// Gets checks import batch filters
        /// </summary>
        [HttpGet("Filters/ImportBatch")]
        public async Task<IActionResult> GetChecksImportBatchFilters([FromRoute] decimal payrollNumber)
        {
            var filters = await checksService.GetChecksImportBatchAsyncFilters(User.GetConum(), payrollNumber);
            return Ok(filters);
        }

        /// <summary>
        /// Gets check line types
        /// </summary>
        [HttpGet("LineTypes")]
        public async Task<IActionResult> GetCheckLineTypes()
        {
            var lineTypes = await checkLinesService.GetCheckLineTypesAsync(User.GetConum());
            return Ok(lineTypes);
        }

        /// <summary>
        /// Updates checks sort settings
        /// </summary>
        [HttpPost("SortSettings")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<PayrollChecksSortSettings>> UpdateChecksSortSettings([FromBody] PayrollChecksSortSettings settings)
        {
            var result = await payrollSettingsService.UpdatePayrollChecksSortSettings(User.GetConum(), User.GetPaydeckUserId(), settings);
            return Ok(result);
        }

        /// <summary>
        /// Updates checks column settings
        /// </summary>
        [HttpPost("ColumnSettings")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<PayrollChecksColumnSettings>> UpdateChecksColumnSettings([FromBody] PayrollChecksColumnSettings settings)
        {
            var result = await payrollSettingsService.UpdatePayrollChecksColumnSettings(User.GetConum(), User.GetPaydeckUserId(), settings);
            return Ok(result);
        }
    }
}
