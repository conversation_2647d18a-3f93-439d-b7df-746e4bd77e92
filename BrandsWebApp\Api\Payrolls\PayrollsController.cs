﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Filters;
using BrandsWebApp.Models.Payroll;
using BrandsWebApp.Models.Payroll.Settings;
using BrandsWebApp.Services.Payroll;
using BrandsWebApp.Services.Validation.PayrollValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using RpcMessagingModels.CalculateCheck;
using RpcMessagingModels.CreateCheck;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Payrolls
{
    [Route("api/Payrolls")]
    [ApiController]
    [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
    public class PayrollsController : ControllerBase
    {
        private readonly PayrollsService payrollsService;
        private readonly ILogger<PayrollsController> logger;
        private readonly CreatePayrollService createPayrollService;
        private readonly IAuthorizationService authorizationService;
        private readonly PayrollSignalRUpdateService payrollSignalRUpdateService;
        private readonly CheckLinesService checkLinesService;
        private readonly PayrollChecksService checksService;
        private readonly PayrollSettingsService payrollSettingsService;

        public PayrollsController(
            PayrollsService payrollsService,
            ILogger<PayrollsController> logger,
            CreatePayrollService createPayrollService,
            IAuthorizationService authorizationService,
            PayrollSignalRUpdateService payrollSignalRUpdateService,
            CheckLinesService checkLinesService,
            PayrollChecksService checksService,
            PayrollSettingsService payrollSettingsService)
        {
            this.payrollsService = payrollsService;
            this.logger = logger;
            this.createPayrollService = createPayrollService;
            this.authorizationService = authorizationService;
            this.payrollSignalRUpdateService = payrollSignalRUpdateService;
            this.checkLinesService = checkLinesService;
            this.checksService = checksService;
            this.payrollSettingsService = payrollSettingsService;

        }

        /// <summary>
        /// Gets payrolls
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetPayrolls()
        {
            var result = await payrollsService.GetPayrollsAsync(User.GetConum());
            return Ok(result);
        }

        /// <summary>
        /// Gets company payroll status
        /// </summary>
        [HttpGet("Status")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<PayrollStatusData>> GetCompanyPayrollStatus()
        {
            var paydeckPayrollAllowed = (await authorizationService.AuthorizeAsync(User, Permission.PaydeckPayroll)).Succeeded;
            var status = await payrollsService.GetPayrollStatusAsync(User.GetConum(), User.GetPaydeckUserId(), paydeckPayrollAllowed);
            return Ok(status);
        }

        /// <summary>
        /// Gets payroll payee options
        /// </summary>
        [HttpGet("PayeeOptions")]
        public async Task<IActionResult> GetPayrollPayeeOptions([FromQuery] PayrollPayeeOptionsQueryFilter queryFilter)
        {
            var status = await payrollsService.GetPayrollPayeeOptionsAsync(User.GetConum(), queryFilter.CalendarIds, queryFilter.UnscheduledPayroll);
            return Ok(status);
        }

        /// <summary>
        /// Gets submitted checks
        /// </summary>
        [HttpGet("SubmittedChecks")]
        public async Task<IActionResult> GetSubmittedChecks([FromQuery] decimal checkNumber)
        {
            var result = await checksService.GetSubmittedChecksByCheckNumberAsync(User.GetConum(), checkNumber);
            return Ok(result);
        }

        /// <summary>
        /// Gets payroll
        /// </summary>
        [HttpGet("{payrollNumber}")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<Payroll>> GetPayroll([FromRoute] decimal payrollNumber)
        {
            var result = await payrollsService.GetPayrollAsync(User.GetConum(), payrollNumber, User.GetPaydeckUserId());
            return Ok(result);
        }

        /// <summary>
        /// Creates payroll
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> CreatePayroll([FromBody] CreatePayrollPayload payroll, [FromServices] CreatePayrollValidation validations)
        {
            var validationResult = await validations.ValidateAsync(payroll);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            var payrollInfo = await payrollsService.GetPayrollInfoAsync(User.GetConum());
            if (payrollInfo.HasPayrollPassword)
            {
                logger.LogWarning("Creating payroll is not allowed. Company has payroll password.");
                return BadRequest("Creating payroll is not allowed.");
            }

            if (payrollInfo.ActivePayroll != null)
            {
                logger.LogWarning("Creating payroll is not allowed. There is another open payroll. ActivePayrollNumber: {activePayrollNumber}", payrollInfo.ActivePayroll.PrNum);
                return BadRequest("There is another open payroll.");
            }

            var payrollNumber = await createPayrollService.CreatePayrollAsync(User.GetConum(), User.GetFullName(), payroll);
            return Ok(new { payrollNumber });
        }

        /// <summary>
        /// Deletes payroll
        /// </summary>
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpDelete("{payrollNumber}")]
        public async Task<IActionResult> DeletePayroll([FromRoute] decimal payrollNumber)
        {
            await payrollsService.DeletePayrollAsync(User.GetConum(), payrollNumber);
            return NoContent();
        }

        /// <summary>
        /// Gets check line setup
        /// </summary>
        [HttpGet("{prnum}/Setup/{lineType}/{code}")]
        public async Task<IActionResult> GetCheckLineSetup([FromRoute] decimal prnum, [FromRoute] char lineType, [FromRoute] decimal code, [FromQuery] decimal[] empnums)
        {
            var checkLineOptions = await checkLinesService.GetNewCheckLineSetupAsync(User.GetConum(), prnum, empnums, lineType, code);
            return Ok(checkLineOptions);
        }

        /// <summary>
        /// Pushes updated calculcations to active SignalR connections
        /// </summary>
        [AllowAnonymous]
        [HttpPost("TaxTotalsUpdate")]
        public async Task<IActionResult> TaxTotalsUpdate([FromBody] CalculateCheckResponse calculateCheckResponse)
        {
            if (!HttpContext.Request.Headers.TryGetValue(Models.Constants.SIGNAL_R_SECRET_KEY_HEADER_NAME, out var key) || !key.All(v => v == Models.Constants.SIGNAL_R_SECRET_KEY_HEADER_VALUE))
            {
                logger.LogWarning("Unauthorized in TaxTotalsUpdate");
                return Unauthorized();
            }

            await payrollSignalRUpdateService.TaxTotalsUpdateAsync(calculateCheckResponse);
            return NoContent();
        }

        /// <summary>
        /// Pushes created checks to active SignalR connections
        /// </summary>
        [AllowAnonymous]
        [HttpPost("ChecksUpdate")]
        public async Task<IActionResult> ChecksUpdate([FromBody] ChecksResponse createChecksResponse)
        {
            logger.LogDebug("Entering ChecksUpdate");
            if (!HttpContext.Request.CheckSignalRHeader())
            {
                logger.LogWarning("Unauthorized in ChecksUpdate");
                return Unauthorized();
            }

            await payrollSignalRUpdateService.ChecksUpdateAsync(createChecksResponse);
            return NoContent();
        }

        /// <summary>
        /// Update payroll settings
        /// </summary>
        [HttpPost("PayrollSettings")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<PayrollSettings>> UpdatePayrollSettings([FromBody] PayrollSettings settings)
        {
            var result = await payrollSettingsService.UpdatePayrollSettings(User.GetConum(), User.GetPaydeckUserId(), settings);
            return Ok(result);
        }
    }
}
