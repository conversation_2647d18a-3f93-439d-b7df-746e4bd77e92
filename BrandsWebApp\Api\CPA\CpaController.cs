﻿using Brands.DataModels;
using BrandsWebApp.Authentication;
using BrandsWebApp.Constants;
using BrandsWebApp.Models.Account;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.Cpa;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Auth;
using BrandsWebApp.Services.Validation.CpaValidation;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.CPA
{
    [Route("api/Cpa/{firmId}")]
    [ApiController]
    public class CpaController : BaseController
    {
        private readonly CpaService cpaService;
        private readonly UserEventService userEventService;
        private readonly BrandsLoginService brandsLoginService;
        private readonly UserIdentityService userIdentityService;

        public CpaController(CpaService cpaService, UserEventService userEventService, BrandsLoginService brandsLoginService, UserIdentityService userIdentityService, IServiceProvider serviceProvider) : base(serviceProvider)
        {
            this.cpaService = cpaService;
            this.brandsLoginService = brandsLoginService;
            this.userEventService = userEventService;
            this.userIdentityService = userIdentityService;
        }

        [HttpGet]
        public async Task<ActionResult> GetFirmDetails([FromRoute] int firmId)
        {
            var identityResults = await userIdentityService.ValidateUserIdentity(RedirectTo.AddCpaDetails);
            if (!identityResults.IsValid || identityResults.User == null)
            {
                logger.LogWarning("User was not validated");
                return Unauthorized();
            }

            BrandsAuthUser user = identityResults.User;
            CpaCompanyDetails cpaDetails = await cpaService.GetFirmDetailsAsync(user.Id, firmId);
            return Ok(cpaDetails);
        }

        [HttpPut]
        public async Task<ActionResult> AddFirmDetais([FromRoute] int firmId, [FromBody] AddCpaCompanyDetails cpaCompanyDetails)
        {
            var identityResults = await userIdentityService.ValidateUserIdentity(RedirectTo.AddCpaDetails);
            if (!identityResults.IsValid || identityResults.User == null)
            {
                logger.LogWarning("User was not validated");
                return Unauthorized();
            }

            BrandsAuthUser user = identityResults.User;
            user.FirstName = cpaCompanyDetails.UserFirstName;
            user.LastName = cpaCompanyDetails.UserLastName;
            await userManager.UpdateAsync(user);
            await cpaService.AddFirmDetailsAsync(user.Id, firmId, cpaCompanyDetails);

            ImpersonateInfo impersonateInfo = await GetImpersonateInfoFromTokenAsync(user);
            UserAuthorizationResults userAuthResults = await brandsLoginService.GetUserAuthorizationResultsAsync(user, new Profile(ProfileType.Cpa, firmId), impersonateInfo: impersonateInfo);

            await userEventService.SaveUserEvent(user, UserEvents.AddCpaDetails, true, userAuthResults.Profile);

            return Ok(userAuthResults);
        }

        [HttpPost("Replace")]
        public async Task<ActionResult> ReplaceFirm([FromRoute] int firmId, [FromBody] ReplaceCpaFirm replacementCpa)
        {
            var identityResults = await userIdentityService.ValidateUserIdentity(RedirectTo.AddCpaDetails);
            if (!identityResults.IsValid || identityResults.User == null)
            {
                logger.LogWarning("User was not validated");
                return Unauthorized();
            }

            BrandsAuthUser user = identityResults.User;
            await cpaService.ReplaceCpaFirmAsync(user.Id, firmId, replacementCpa.ReplacementFirmId);

            ImpersonateInfo impersonateInfo = await GetImpersonateInfoFromTokenAsync(user);
            UserAuthorizationResults userAuthResults = await brandsLoginService.GetUserAuthorizationResultsAsync(user, new Profile(ProfileType.Cpa, replacementCpa.ReplacementFirmId), impersonateInfo: impersonateInfo);

            await userEventService.SaveUserEvent(user, UserEvents.ReplaceCpaFirm, true, userAuthResults.Profile);

            return Ok(userAuthResults);
        }

        [HttpPost("Switch")]
        public async Task<IActionResult> SwitchCpa([FromRoute] int firmId)
        {
            BrandsAuthUser user = null;

            var result = await HttpContext.AuthenticateAsync();
            if (result.Succeeded)
            {
                user = await userManager.FindByIdAsync(result.Principal.GetUserId());
            }
            else
            {
                var identityResults = await userIdentityService.ValidateUserIdentity(RedirectTo.IPRestricted, RedirectTo.SelectCompany, RedirectTo.CpaPortal);
                if (!identityResults.IsValid)
                {
                    logger.LogWarning("User was not validated");
                    return Unauthorized();
                }

                user = identityResults.User;
            }

            if (user == null)
            {
                logger.LogWarning("User was not validated");
                return Unauthorized();
            }

            ImpersonateInfo impersonateInfo = await GetImpersonateInfoFromTokenAsync(user);
            UserAuthorizationResults userAuthResults = await brandsLoginService.GetUserAuthorizationResultsAsync(user, new Profile(ProfileType.Cpa, firmId), impersonateInfo: impersonateInfo);

            await userEventService.SaveUserEvent(user, UserEvents.SwitchCpaFirm, true, userAuthResults.Profile);

            return Ok(userAuthResults);
        }

        [HttpGet("CurrentUser")]
        [Authorize(Policy = AuthPolicies.CpaFirmAccess)]
        public async Task<IActionResult> GetCurrentUserProfile()
        {
            CpaUserProfile profile = await cpaService.GetCurrentUserProfileAsync();

            return Ok(profile);
        }

        [HttpGet("Profile")]
        [Authorize(Policy = AuthPolicies.CpaFirmOwnerAccess)]
        public async Task<IActionResult> GetCpaProfile([FromRoute] int firmId)
        {
            CpaProfile profile = await cpaService.GetCpaProfileAsync(firmId);

            return Ok(profile);
        }

        [HttpPut("Profile")]
        [Authorize(Policy = AuthPolicies.CpaFirmOwnerAccess)]
        public async Task<IActionResult> UpdateCpaProfile([FromRoute] int firmId, [FromBody] CpaProfile cpaProfile)
        {
            cpaProfile.Id = firmId;
            await cpaService.UpdateCpaProfileAsync(cpaProfile);

            return NoContent();
        }

        [HttpGet("Clients")]
        [Authorize(Policy = AuthPolicies.CpaFirmAccess)]
        public async Task<IActionResult> GetCpaClients([FromRoute] int firmId)
        {
            IEnumerable<CpaClient> cpaClients = await cpaService.GetCurrentUserCpaClientsAsync(firmId);

            return Ok(cpaClients);
        }

        [HttpGet("Clients/Details")]
        [Authorize(Policy = AuthPolicies.CpaFirmAccess)]
        public async Task<IActionResult> GetCpaClientsDetails([FromRoute] int firmId, [FromQuery] string name)
        {
            IEnumerable<CpaClientDetails> cpaClientsDetails = await cpaService.GetCpaClientsDetailsAsync(firmId, name);

            return Ok(cpaClientsDetails);
        }

        [HttpGet("Clients/{clientId}")]
        [Authorize(Policy = AuthPolicies.CpaFirmAccess)]
        public async Task<IActionResult> GetCpaClientDetails([FromRoute] int firmId, [FromRoute] decimal clientId)
        {
            CpaClientDetails cpaClientDetails = await cpaService.GetCpaClientDetailsAsync(firmId, clientId);

            return Ok(cpaClientDetails);
        }

        [HttpPost("Clients/{clientId}/Pins")]
        [Authorize(Policy = AuthPolicies.CpaFirmAccess)]
        public async Task<IActionResult> PinCpaClient([FromRoute] int firmId, [FromRoute] decimal clientId)
        {
            await cpaService.PinCpaClientAsync(firmId, clientId);

            return NoContent();
        }

        [HttpDelete("Clients/{clientId}/Pins")]
        [Authorize(Policy = AuthPolicies.CpaFirmAccess)]
        public async Task<IActionResult> UnpinCpaClient([FromRoute] int firmId, [FromRoute] decimal clientId)
        {
            await cpaService.UnpinCpaClientAsync(firmId, clientId);

            return NoContent();
        }

        [HttpGet("Users")]
        [Authorize(Policy = AuthPolicies.CpaFirmOwnerAccess)]
        public async Task<IActionResult> GetCpaUsers([FromRoute] int firmId, [FromQuery] string email)
        {
            IEnumerable<CpaUser> cpaUsers = await cpaService.GetCpaUsersAsync(firmId, email);

            return Ok(cpaUsers);
        }

        [HttpPost("Users")]
        [Authorize(Policy = AuthPolicies.CpaFirmOwnerAccess)]
        public async Task<IActionResult> AddUser([FromRoute] int firmId, [FromBody] AddCpaUser cpaUser, [FromServices] AddCpaUserValidation validations)
        {
            var validationResult = await validations.ValidateAsync(cpaUser);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            var user = await userManager.FindByIdAsync(HttpContext.User.GetPaydeckUserId().ToString());
            await cpaService.AddCpaUserAsync(firmId, cpaUser);
            await userEventService.SaveUserEvent(user, UserEvents.AddCpaUser, true, HttpContext.User.GetConum(), profile: new Profile(ProfileType.Cpa, firmId));

            return NoContent();
        }

        [HttpPut("Users/{userId}")]
        [Authorize(Policy = AuthPolicies.CpaFirmOwnerAccess)]
        public async Task<IActionResult> EditUser([FromRoute] int firmId, [FromRoute] int userId, [FromBody] EditCpaUser cpaUser, [FromServices] EditCpaUserValidation validations)
        {
            var validationResult = await validations.ValidateAsync(cpaUser);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            var user = await userManager.FindByIdAsync(HttpContext.User.GetPaydeckUserId().ToString());
            await cpaService.EditCpaUserAsync(firmId, userId, cpaUser);
            await userEventService.SaveUserEvent(user, UserEvents.EditCpaUser, true, HttpContext.User.GetConum(), profile: new Profile(ProfileType.Cpa, firmId));

            return NoContent();
        }

        [HttpDelete("Users/{userId}")]
        [Authorize(Policy = AuthPolicies.CpaFirmOwnerAccess)]
        public async Task<IActionResult> DeleteUser([FromRoute] int firmId, [FromRoute] int userId)
        {
            await cpaService.DeleteCpaUserAsync(firmId, userId, User.GetPaydeckUserId());

            return NoContent();
        }

        [HttpPost("Users/{userId}/ResendInvite")]
        public async Task<IActionResult> ResendCpaInvite([FromRoute] int firmId, [FromRoute] int userId)
        {
            await cpaService.ResendCpaInvite(firmId, userId);

            return NoContent();
        }

        [HttpPost("User/Name")]
        public async Task<IActionResult> AddCurrentUserName([FromRoute] int firmId, AddUserName userNames)
        {
            BrandsAuthUser user;

            var authResult = await HttpContext.AuthenticateAsync();
            if (authResult.Succeeded)
            {
                user = await userManager.FindByIdAsync(authResult.Principal.GetUserId());
            }
            else
            {
                var identityResults = await userIdentityService.ValidateUserIdentity(RedirectTo.AddCpaUserName);
                if (!identityResults.IsValid)
                {
                    logger.LogWarning("User was not validated");
                    return Unauthorized();
                }

                user = identityResults.User;
            }

            if (user == null)
            {
                logger.LogWarning("User was not validated");
                return Unauthorized();
            }

            user.FirstName = userNames.FirstName;
            user.LastName = userNames.LastName;
            await userManager.UpdateAsync(user);

            ImpersonateInfo impersonateInfo = await GetImpersonateInfoFromTokenAsync(user);
            UserAuthorizationResults userAuthResults = await brandsLoginService.GetUserAuthorizationResultsAsync(user, new Profile(ProfileType.Cpa, firmId), impersonateInfo: impersonateInfo);

            await userEventService.SaveUserEvent(user, UserEvents.AddUserName, true, User.GetConum(), profile: new Profile(ProfileType.Cpa, firmId));

            return Ok(userAuthResults);
        }
    }
}
