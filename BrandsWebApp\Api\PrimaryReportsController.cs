﻿using Brands.DAL;
using BrandsWebApp.Authentication;
using BrandsWebApp.Exceptions;
using BrandsWebApp.Models.ReportHistory;
using BrandsWebApp.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/PrimaryReports")]
    [Route("api/ReportHistory")]
    [ApiController]
    [Authorize(Policy = nameof(Permission.RunReports))]
    public class PrimaryReportsController : ControllerBase
    {
        private readonly ILogger<PrimaryReportsController> logger;
        private readonly EPDATAContext epDataContext;
        private readonly PrimaryReportsService primaryReportsService;

        public PrimaryReportsController(ILogger<PrimaryReportsController> logger,EPDATAContext epDataContext,PrimaryReportsService primaryReportsService)
        {
            this.logger = logger;
            this.epDataContext = epDataContext;
            this.primaryReportsService = primaryReportsService;
        }

        [HttpGet("PayrolNumbers")]
        public async Task<IActionResult> GetPayrollNumbers()
        {
            var list = await primaryReportsService.GetPayrollNumbersAsync(User.GetConum());

            return Ok(list);
        }

        [HttpGet("AvailYears")]
        public async Task<IActionResult> GetAvailYears(string type)
        {
            var years = await primaryReportsService.GetAvailYearsAsync(type, User.GetConum());

            return Ok(years);
        }

        [HttpGet("PayrollReports")]
        public async Task<ActionResult<IList<GroupedReports>>> GetPayrollReports([FromQuery] ReportFilter reportFilter)
        {
            var results = await primaryReportsService.GetPayrollReportsAsync(reportFilter, User.GetConum(), User.GetPaydeckUserId());

            return Ok(results);
        }

        [HttpGet("DistributedPayStubsReport")]
        public async Task<ActionResult<IList<GroupedReports>>> GetDistributedPayStubsReport([FromQuery] ReportFilter reportFilter)
        {
            var results = await primaryReportsService.GetDistributedPayStubsReportsAsync(reportFilter, User.GetConum());

            return Ok(results);
        }

        [HttpGet("PeriodEndReports")]
        public async Task<ActionResult<IList<GroupedReports>>> GetPeriodEndReports([FromQuery] ReportFilter reportFilter)
        {
            var results = await primaryReportsService.GetPeriodEndReportsAsync(reportFilter, User.GetConum(), User.GetPaydeckUserId());

            return Ok(results);
        }

        [HttpGet("DistributedW2s")]
        public async Task<ActionResult<IList<GroupedReports>>> GetDistributedW2s([FromQuery] ReportFilter reportFilter)
        {
            var results = await primaryReportsService.GetDistributedW2sAsync(reportFilter, User.GetConum());

            return Ok(results);
        }

        [HttpGet("DownloadReport/{id}")]
        public async Task<IActionResult> DownloadReport(Guid id)
        {
            try
            {
                var report = await epDataContext.ViewReportsUnions.SingleOrDefaultAsync(r => r.Id == id && r.Conum == HttpContext.User.GetConum());
                if (report == null)
                {
                    logger.LogWarning("Report not found.  ReportId: {Id}", id);
                    return NotFound();
                }

                if (report.NeedApproval.IsNotNullOrWhiteSpace())
                {
                    return StatusCode(551, $"Please contact customer support to access these reports.");
                }

                return File(report.ReportFile, "application/pdf", report.ReportName.Replace("/", "_"));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in DownloadReport. Id: {Id}", id);
                throw;
            }
        }

        [HttpPost("DownloadGroupedReports")]
        public async Task<IActionResult> DownloadGroupedReports(DownloadGroupedReportsModel downloadGroupedReportsModel)
        {
            if (new[] { ReportType.DistributedW2s, ReportType.PeriodEnd }.Contains(downloadGroupedReportsModel.ReportType))
            {
                var needApproval = await primaryReportsService.CheckNeedApprovalAsync(downloadGroupedReportsModel, User.GetConum());
                if (needApproval.IsNotNullOrWhiteSpace())
                {
                    return StatusCode(551, new { Message = $"Please contact customer support to access these reports." });
                }
            }

            using (logger.BeginScope("Entering DownloadGroupedReports ReportType: {ReportType} {@DownloadGroupedReportsModel}", downloadGroupedReportsModel.ReportType, downloadGroupedReportsModel))
            {
                try
                {
                    var groupedReportsFileInfo = await primaryReportsService.GetGroupedReportsFileInfoAsync(downloadGroupedReportsModel, User.GetConum());

                    using (var newPdf = new DevExpress.Pdf.PdfDocumentProcessor())
                    {
                        newPdf.CreateEmptyDocument();
                        await primaryReportsService.AddReportsToFileAsync(groupedReportsFileInfo.Data, downloadGroupedReportsModel.ReportType, newPdf, User.GetConum());

                        if (newPdf.Document.Pages.Count == 0)
                        {
                            return NotFound();
                        }

                        using (var ms = new MemoryStream())
                        {
                            newPdf.SaveDocument(ms, true);
                            return File(ms.ToArray(), "application/pdf", $"{groupedReportsFileInfo.Name}.pdf");
                        }
                    }
                }
                catch (ArgumentNullException ex)
                {
                    return BadRequest(ex.Message);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error creating PDf file for grouped reports");
                    throw;
                }
            }
        }

        [HttpPost("{reportId}/Stars")]
        public async Task<IActionResult> StarReport([FromRoute] int reportId)
        {
            try
            {
                await primaryReportsService.StarReportAsync(reportId, User.GetPaydeckUserId(), User.GetConum());
            }
            catch (DatabaseRecordNotFoundException e)
            {
                return NotFound(e.Message);
            }
            catch (InvalidOperationException e)
            {
                return BadRequest(e.Message);
            }
            
            return NoContent();
        }

        [HttpDelete("{reportId}/Stars")]
        public async Task<IActionResult> UnstarReport([FromRoute] int reportId)
        {
            try
            {
                await primaryReportsService.UnstarReportAsync(reportId, User.GetPaydeckUserId(), User.GetConum());
            }
            catch (DatabaseRecordNotFoundException e)
            {
                return NotFound(e.Message);
            }
            catch (InvalidOperationException e)
            {
                return BadRequest(e.Message);
            }

            return NoContent();
        }
    }
}