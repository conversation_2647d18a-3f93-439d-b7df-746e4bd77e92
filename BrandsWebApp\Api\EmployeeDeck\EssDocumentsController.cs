﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Services.EmployeeDeck;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.EmployeeDeck
{
    [Authorize(Policy = AuthPolicies.IsEssUser)]
    [ApiController]
    [Route(ControllerPath)]
    public class EssDocumentsController : ControllerBase
    {
        const string ControllerPath = "api/employee-deck/my/documents";

        private readonly EssDocumentService documentService;
        private readonly JwtFactory jwtFactory;

        public EssDocumentsController(EssDocumentService documentService, JwtFactory jwtFactory)
        {
            this.documentService = documentService;
            this.jwtFactory = jwtFactory;
        }

        [HttpGet("annual")]
        public async Task<ActionResult> GetAnnualDocuments([FromQuery] int? year = null)
        {
            var result = await documentService.GetEmployeeDocumentsAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value, year);
            return Ok(result);
        }

        [HttpPost("annual/{id}/download")]
        public ActionResult DownloadAnnualDocumentURL([FromRoute] Guid id)
        {
            var path = GetAnnualDownloadPath(id);
            var empCoNumClaim = new Claim("EssCoNum", User.GetEssEmpConum().Value.ToString());
            var empNumClaim = new Claim("EssEmpNum", User.GetEssEmpNum().Value.ToString());
            var token = jwtFactory.IssueResourceExpiringToken(path, TimeSpan.FromMinutes(15), empCoNumClaim, empNumClaim);
            var url = $"/{path}?token={token}";
            return Ok(new
            {
                DownloadURL = url
            });
        }

        [HttpGet("annual/{id}/download.file")]
        [AllowAnonymous]
        public async Task<ActionResult> DownloadAnnualDocument([FromRoute] Guid id, [FromQuery] string token)
        {
            var resourceId = GetAnnualDownloadPath(id);
            var jwt = jwtFactory.ValidateResourceExpiringToken(resourceId, token);
            var coNumClaim = jwt.Claims.FirstOrDefault(c => c.Type == "EssCoNum");
            var empNumClaim = jwt.Claims.FirstOrDefault(c => c.Type == "EssEmpNum");
            decimal coNum = 0;
            decimal empNum = 0;
            if (coNumClaim == null || empNumClaim == null || !decimal.TryParse(coNumClaim.Value, out coNum) || !decimal.TryParse(empNumClaim.Value, out empNum))
            {
                return Unauthorized();
            }

            var result = await documentService.GetEmployeeDocumentAsync(coNum, empNum, id);
            if (result == null || result.ReportFile == null)
            {
                return NotFound();
            }

            var fileDate = result.DisplayDate ?? DateTime.Now;
            var fileDownloadName = $"Document_{fileDate:yyyy-MM-dd}.pdf";
            var fileContentType = PayrollFileType.GetMimeTypeOrDefault(result.FileType);

            this.Response.Headers.Append("X-Filename", fileDownloadName);
            this.Response.Headers.Append("Content-Disposition", $"inline; filename=\"{fileDownloadName}\"");

            var stream = new MemoryStream(result.ReportFile);

            // TODO: Fix the error that happens AFTER serving inline file.
            // NOTE: The call with 3 arguments to `return File` works fine...
            // return File(stream, fileContentType, fileDownloadName);
            return File(stream, fileContentType);
        }

        string GetAnnualDownloadPath(Guid id)
        {
            return $"{ControllerPath}/annual/{id}/download.file";
        }
    }
}
