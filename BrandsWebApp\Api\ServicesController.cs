﻿using Brands.DAL;
using BrandsWebApp.Authentication;
using BrandsWebApp.DataAccess.OnboardingData;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.Service;
using BrandsWebApp.Services;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [ApiController]
    [Route("api/services")]
    public class ServicesController : ControllerBase
    {
        private readonly ILogger<ServicesController> logger;
        private readonly EPDATAContext ePDATAContext;
        private readonly ISqlConnectionService sqlConnectionService;
        private readonly OnboardingDataService onboardingDataService;
        private readonly IAuthorizationService authorizationService;

        public ServicesController(
            ILogger<ServicesController> logger,
            EPDATAContext ePDATAContext,
            ISqlConnectionService sqlConnectionService,
            OnboardingDataService onboardingDataService,
            IAuthorizationService authorizationService)
        {
            this.logger = logger;
            this.ePDATAContext = ePDATAContext;
            this.sqlConnectionService = sqlConnectionService;
            this.onboardingDataService = onboardingDataService;
            this.authorizationService = authorizationService;
        }

        [Authorize(Policy = AuthPolicies.ServicesOrOnboarding)]
        [HttpGet("Groups")]
        public async Task<IActionResult> GetServiceGroups()
        {
            logger.LogDebug("Entering GetServiceGroups");
            try
            {
                await using var con = sqlConnectionService.GetSqlConnection();
                var services = await con.QueryAsync<CompanyServiceGroup>(@"custom.prc_PaydeckServicesWebControls", new
                {
                    conum = HttpContext.User.GetConum()
                }, commandType: System.Data.CommandType.StoredProcedure);
                services.ToList().ForEach(s => s.IsFeatured = s.FeaturedUntil != null && s.FeaturedUntil > DateTime.Now);

                return Ok(services);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetServiceGroups");
                return BadRequest();
            }
        }

        [Authorize(Policy = AuthPolicies.ServicesOrOnboarding)]
        [HttpGet("Groups/{serviceGroupId}/Services")]
        public async Task<IActionResult> GetGroupServices(int serviceGroupId)
        {
            logger.LogDebug("Entering GetGroupServices");
            try
            {
                await using var con = sqlConnectionService.GetSqlConnection();
                var services = await con.QueryAsync<Models.Service.CompanyService>(
                    @"custom.prc_PaydeckServicesWebControls",
                    new { conum = HttpContext.User.GetConum(), PaydeckServiceGroupId = serviceGroupId },
                    commandType: System.Data.CommandType.StoredProcedure);

                foreach (var item in services)
                {
                    item.Features = await ePDATAContext.PaydeckServiceFeatures.Where(f => f.PaydeckServiceId == item.Id).OrderBy(f => f.Sort).ToListAsync();
                    item.Pricing = await ePDATAContext.PaydeckServicePricings.Where(p => p.PaydeckServiceId == item.Id).OrderBy(p => p.Sort).ToListAsync();
                }

                return Ok(services);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetGroupServices");
                return BadRequest();
            }
        }

        [Authorize(Policy = nameof(Permission.Services))]
        [HttpGet("Dashboard")]
        public async Task<IActionResult> GetDashboardServices()
        {
            logger.LogDebug("Entering GetDashboardServices");
            try
            {
                await using var con = sqlConnectionService.GetSqlConnection();
                var services = await con.QueryAsync<Models.Service.CompanyService>(
                    @"custom.prc_PaydeckServicesWebControls",
                    new { conum = HttpContext.User.GetConum(), DashboardServicesOnly = true },
                    commandType: System.Data.CommandType.StoredProcedure);

                foreach (var item in services)
                {
                    item.Features = await ePDATAContext.PaydeckServiceFeatures.Where(f => f.PaydeckServiceId == item.Id).OrderBy(f => f.Sort).ToListAsync();
                    item.Pricing = await ePDATAContext.PaydeckServicePricings.Where(p => p.PaydeckServiceId == item.Id).OrderBy(p => p.Sort).ToListAsync();
                }

                return Ok(services);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetDashboardServices");
                return BadRequest();
            }
        }

        [Authorize(Policy = AuthPolicies.ServicesOrOnboarding)]
        [HttpPost("EnableService")]
        public async Task<IActionResult> EnableService(UpdateService updateService)
        {
            try
            {
                logger.LogDebug("Entering EnableService. {@updateService}", updateService);

                var accessGranted = await ValidateAccess();
                if (!accessGranted)
                {
                    logger.LogDebug("Access not allowed. Additional services onboarding step is disabled.");
                    return Forbid();
                }

                await using var con = sqlConnectionService.GetSqlConnection();
                var result = await con.QuerySingleAsync<UpdateServiceResult>(@"custom.prc_PayDeckSvc_Setup", new
                {
                    Conum = HttpContext.User.GetConum(),
                    ServiceIdentifier = updateService.ServiceIdentifier,
                    ServiceId = updateService.ServiceId,
                    PaydeckUserId = HttpContext.User.GetPaydeckUserId()
                }, commandType: System.Data.CommandType.StoredProcedure);
                return Ok(result);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in EnableService");
                return BadRequest();
            }
        }

        [Authorize(Policy = AuthPolicies.ServicesOrOnboarding)]
        [HttpPost("DisableService")]
        public async Task<IActionResult> DisableService(UpdateService updateService, [FromServices] ZendeskApiService zendeskApiService, [FromServices] QueueBuilder.QueueBuilder queueBuilder)
        {
            try
            {
                logger.LogDebug("Entering DisableService. {@updateService}", updateService);

                var accessGranted = await ValidateAccess();
                if (!accessGranted)
                {
                    logger.LogDebug("Access not allowed. Additional services onboarding step is disabled.");
                    return Forbid();
                }

                await using var con = sqlConnectionService.GetSqlConnection();
                var result = await con.QuerySingleAsync<UpdateServiceResult>(@"custom.prc_PayDeckSvc_Disable", new
                {
                    Conum = HttpContext.User.GetConum(),
                    ServiceIdentifier = updateService.ServiceIdentifier,
                    ServiceId = updateService.ServiceId,
                    PaydeckUserId = HttpContext.User.GetPaydeckUserId()
                }, commandType: System.Data.CommandType.StoredProcedure);

                //queueBuilder.WithConum(HttpContext.User.GetConum())
                //    .WithDescription("Disable Service")
                //    .WithRequesterInfo(HttpContext.User.GetEmail(), "PPX_WebUser", HttpContext.User.GetEmpConum(), HttpContext.User.GetEmpnum());

                //var jsonObject = new
                //{
                //    Conum = HttpContext.User.GetConum(),
                //    PaydeckUserId = HttpContext.User.GetPaydeckUserId(),
                //    FirstName = HttpContext.User.GetFirstName(),
                //    LastName = HttpContext.User.GetLastName(),
                //    updateService.ServiceIdentifier,
                //    updateService.ServiceId
                //};
                //queueBuilder.GetEmailBuilder()
                //    .WithDeliverViaZendesk(true)
                //    .WithReportTemplate("DisableService_EmailTemplateId", jsonObject)
                //    .WithExecuteMacro("Zendesk_DisableService_MacroId")
                //    .Build();

                //await queueBuilder.SaveAndPublishAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in DisableService");
                return BadRequest();
            }
        }

        private async Task<bool> ValidateAccess()
        {
            var servicesAuth = await authorizationService.AuthorizeAsync(User, Permission.Services);
            if (servicesAuth.Succeeded)
            {
                return true;
            }
            else
            {
                return !(await onboardingDataService.IsAdditionalServicesDisabled());
            }
        }
    }
}
