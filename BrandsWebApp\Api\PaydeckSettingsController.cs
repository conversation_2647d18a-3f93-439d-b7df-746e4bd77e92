﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models;
using BrandsWebApp.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/PaydeckSettings")]
    [ApiController]
    [Authorize(Policy = nameof(Permission.ManageContactsAndUsers))]
    public class PaydeckSettingsController : ControllerBase
    {
        private readonly PaydeckSettingsService paydeckSettingsService;
        private readonly ILogger<PaydeckSettingsController> logger;

        public PaydeckSettingsController(PaydeckSettingsService paydeckSettingsService,ILogger<PaydeckSettingsController> logger)
        {
            this.paydeckSettingsService = paydeckSettingsService;
            this.logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetPaydeckSettings()
        {
            logger.LogDebug("Entering GetPaydeckSettings.");
            var settings = await paydeckSettingsService.GetPaydeckSettings(User.GetConum());
            return Ok(settings);
        }

        [HttpPost]
        public async Task<IActionResult> UpdatePaydeckSettingsSettings(PaydeckSettings paydeckSettings)
        {
            logger.LogDebug("Entering UpdateDisplayNameSetting.");

            await paydeckSettingsService.UpdateDisplayNameSetting(User.GetConum(), paydeckSettings);

            return NoContent();
        }
    }
}
