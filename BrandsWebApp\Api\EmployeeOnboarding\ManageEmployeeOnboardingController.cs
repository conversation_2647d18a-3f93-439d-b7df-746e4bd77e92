﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models.EmployeeOnboarding;
using BrandsWebApp.Models.Onboarding;
using BrandsWebApp.Services.EmployeeDeck;
using BrandsWebApp.Services.Validation.EmployeeOnboardingValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.EmployeeOnboarding
{
    [Authorize]
    [ApiController]
    [Route("api/EmployeeOnboardingManager")]
    [Authorize(Policy = nameof(Permission.ManageEmployees))]
    public class ManageEmployeeOnboardingController : ControllerBase
    {
        private readonly EssOnboardingService essOnboardingService;
        private readonly EssOnboardingDocumentService essOnboardingDocumentService;
        private readonly EssTaxService essTaxService;

        public ManageEmployeeOnboardingController(EssOnboardingService essOnboardingService, EssOnboardingDocumentService essOnboardingDocumentService, EssTaxService essTaxService)
        {
            this.essOnboardingService = essOnboardingService;
            this.essOnboardingDocumentService = essOnboardingDocumentService;
            this.essTaxService = essTaxService;
        }

        /// <summary>
        /// Gets onboarding employees
        /// </summary>
        [HttpGet("Employees")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<IEnumerable<OnboardingEmployee>>> GetOnboardingEmployees()
        {
            var employees = await essOnboardingService.GetOnboardingEmployeesAsync(User.GetConum());
            return Ok(employees);
        }

        /// <summary>
        /// Gets onboarding employee details by onboarding employee number
        /// </summary>
        [HttpGet("Employees/{empNum}")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<EmployeeDetails>> GetOnboardingEmployee([FromRoute] decimal empNum)
        {
            var employees = await essOnboardingService.GetOnboardingEmployeeDetailsAsync(User.GetConum(), empNum);
            return Ok(employees);
        }

        /// <summary>
        /// Gets onboarding employee identification info
        /// </summary>
        [HttpGet("Employees/{empNum}/IdentificationInfo")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<ReviewEmployeeIdentificationInfo>> GetOnboardingEmployeeIdentificationInfo([FromRoute] decimal empNum)
        {
            var info = await essOnboardingDocumentService.GetIdentificationInfoForReviewAsync(User.GetConum(), empNum);
            return Ok(info);
        }

        /// <summary>
        /// Updates onboarding employee identification info
        /// </summary>
        [HttpPut("Employees/{empNum}/IdentificationInfo")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<UpdateEmployeeIdDocumentsInfoResponse>> UpdateEmployeeIdentificationInfo(
            [FromRoute] decimal empNum,
            [FromBody] UpdateEmployeeIdDocumentsInfo identificationInfo,
            [FromServices] UpdateEmployeeIdDocumentsInfoValidation validations)
        {
            var validationResult = await validations.ValidateAsync(identificationInfo);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            var result = await essOnboardingDocumentService.UpdateIdentificationDocsAsync(User.GetConum(), empNum, identificationInfo);
            return Ok(result);
        }

        /// <summary>
        /// Adds new onboarding employee
        /// </summary>
        [HttpPost("Employees")]
        public async Task<IActionResult> AddOnboardingEmployee([FromBody] AddOnboardingEmployee employee, [FromServices] AddOnboardingEmployeeValidation validations)
        {
            var validationResult = await validations.ValidateAsync(employee);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            var result = await essOnboardingService.AddOnboardingEmployeeAsync(User.GetConum(), User.GetPaydeckUserId(), employee);
            return Ok(result);
        }

        /// <summary>
        /// Sends onboarding reminder to employee
        /// </summary>
        [HttpPost("Employees/{empNum}/Reminder")]
        public async Task<IActionResult> SendOnboardingReminderToEmployee([FromRoute] decimal empNum)
        {
            await essOnboardingService.SendOnboardingReminderAsync(User.GetConum(), empNum);
            return NoContent();
        }

        /// <summary>
        /// Approves employee onboarding
        /// </summary>
        [HttpPost("Employees/{empNum}")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<IActionResult> ApproveEmployeeOnboarding([FromRoute] decimal empNum, [FromBody] UpdateEmployeeInfo employeeInfo)
        {
            await essOnboardingService.ApproveOnboardingEmployeeAsync(User.GetConum(), empNum, employeeInfo);
            return NoContent();
        }

        /// <summary>
        /// Cancels employee onboarding
        /// </summary>
        [HttpDelete("Employees/{empNum}")]
        public async Task<IActionResult> CancelEmployeeOnboarding([FromRoute] decimal empNum)
        {
            var updatedEmployees = await essOnboardingService.DeleteOnboardingEmployeeAsync(User.GetConum(), empNum);
            return Ok(updatedEmployees);
        }

        /// <summary>
        /// Gets I-9 form for employee
        /// </summary>
        [HttpGet("Employees/{empNum}/IdentificationForm")]
        [ApiExplorerSettings(GroupName = "v2")]
        [ProducesResponseType(typeof(FileContentResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetIdentificationForm([FromRoute] decimal empNum)
        {
            var isFormReviewed = await essOnboardingDocumentService.IsIdentificationFormReviewedAsync(User.GetConum(), empNum);
            if (isFormReviewed)
            {
                var document = await essOnboardingDocumentService.GetSignedIdentificationFormAsync(User.GetConum(), empNum, true);
                if (document?.Data != null)
                {
                    return File(document.Data, "application/octet-stream", document.Name);
                }

                return NotFound();
            }

            var fileDate = DateTime.Now;
            var fileName = $"i-9_{empNum}_{fileDate:yyyy-MM-dd}.pdf";
            var fileContentType = "application/pdf";
            this.Response.Headers.Add("X-Filename", fileName);
            this.Response.Headers.Add("Content-Disposition", $"inline; filename=\"{fileName}\"");

            var stream = await essOnboardingService.CreateIdentificationFormStreamAsync(User.GetConum(), empNum, User.GetPaydeckUserId());
            return File(stream, fileContentType);
        }

        /// <summary>
        /// Signs I-9 form as employer
        /// </summary>
        [HttpPost("Employees/{empNum}/IdentificationForm")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<SignEmployeeIdentificationFormResult>> SignIdentificationForm([FromRoute] decimal empNum, [FromForm] SignEmployeeIdentificationForm signForm)
        {
            if (signForm == null || signForm.Signature == null)
            {
                return BadRequest("Signature is required");
            }

            using var stream = await essOnboardingService.CreateIdentificationFormStreamAsync(User.GetConum(), empNum, User.GetPaydeckUserId(), signForm.Signature);
            var docID = await essOnboardingDocumentService.SaveSignedI9FormAsync(stream.ToArray(), User.GetConum(), empNum, User.GetFullName(), true);
            return Ok(new SignEmployeeIdentificationFormResult
            {
                DownloadURL = $"api/EmployeeOnboardingManager/{empNum}/IdentificationForm/Reviewed"
            });
        }

        /// <summary>
        /// Gets W-4 form for employee by type (Federal or State)
        /// </summary>
        [HttpGet("Employees/{empNum}/W4Forms/{type}")]
        [ApiExplorerSettings(GroupName = "v2")]
        [ProducesResponseType(typeof(FileContentResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetTaxForm([FromRoute] decimal empNum, [FromRoute] string type)
        {
            DownloadDocument form = null;
            var formType = type?.ToLower();
            if (formType == "federal" || formType == "state")
            {
                form = await essTaxService.GetW4SignedFormAsync(User.GetConum(), empNum, formType, true);
            }

            if (form?.Data != null)
            {
                return File(form.Data, "application/octet-stream", form.Name);
            }

            return NotFound();
        }
    }
}
