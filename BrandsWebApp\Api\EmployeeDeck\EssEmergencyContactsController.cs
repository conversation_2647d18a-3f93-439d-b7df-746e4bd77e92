﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Filters;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.Ess.EmergencyContact;
using BrandsWebApp.Services.EmployeeDeck;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.EmployeeDeck
{
    [Authorize(Policy = AuthPolicies.IsEssUser)]
    [ApiController]
    [ServiceFilter(typeof(AccessEssEmergencyContactAsyncFilter))]
    [Route("api/employee-deck/my/emergency-contacts")]
    public class EssEmergencyContactsController : ControllerBase
    {
        private readonly EssEmergencyContactService contactService;

        public EssEmergencyContactsController(EssEmergencyContactService contactService)
        {
            this.contactService = contactService;
        }

        [HttpPost]
        public async Task<ActionResult> CreateEmergencyContact([FromBody] EmergencyContact emergencyContact)
        {
            var contactId = await contactService.AddEmergencyContactAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value, emergencyContact);
            return Ok(new { Id = contactId });
        }

        [HttpGet]
        public async Task<ActionResult> GetEmergencyContacts()
        {
            var contacts = await contactService.GetEmergencyContactsAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value);
            return Ok(contacts);
        }

        [HttpGet("relationship-types")]
        public async Task<ActionResult> GetEmergencyContactRelationshipTypes()
        {
            var types = await contactService.GetEmergencyContactRelationshipTypesAsync();
            return Ok(types);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult> GetEmergencyContact([FromRoute] int id)
        {
            var contact = await contactService.GetEmergencyContactAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value, id);
            return Ok(contact);
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteEmergencyContact([FromRoute] int id)
        {
            await contactService.DeleteEmergencyContactAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value, id);
            return NoContent();
        }

        [HttpPut("{id}")]
        public async Task<ActionResult> UpateEmergencyContact([FromRoute] int id, [FromBody] EmergencyContact contact)
        {
            contact.Id = id;
            await contactService.UpdateEmergencyContactAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value, contact);
            return NoContent();
        }
    }
}
