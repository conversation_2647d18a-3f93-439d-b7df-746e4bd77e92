﻿using Brands.DAL;
using Brands.DataModels;
using BrandsWebApp.Authentication;
using BrandsWebApp.Exceptions;
using BrandsWebApp.Models.Employee;
using BrandsWebApp.Models.Employee.EarningsAndDeductions;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Employee;
using BrandsWebApp.Services.PaymentAddresses;
using BrandsWebApp.Services.Validation.EmployeeValidation;
using Dapper;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using EmployeeDeduction = BrandsWebApp.Models.Employee.EarningsAndDeductions.EmployeeDeduction;
using EmployeeMemo = BrandsWebApp.Models.Employee.EarningsAndDeductions.EmployeeMemo;

namespace BrandsWebApp.Api.Employee
{
    [ApiController]
    [Route("api/Employees/{empnum}/Payroll")]
    [Authorize(Policy = nameof(Permission.ManageEmployees))]
    public class EmployeePayrollController : ControllerBase
    {
        private readonly EPDATAContext ePDATAContext;
        private readonly ILogger<EmployeeController> logger;
        private readonly ISqlConnectionService sqlConnectionService;
        private readonly EmployeeService employeeService;
        private readonly ExecuPayApiClient execuPayApiClient;
        private readonly EmployeeScheduledPaysService employeePayItemsService;
        private readonly PaymentAddressService paymentAddressService;

        public EmployeePayrollController(
            EPDATAContext ePDATAContext,
            ILogger<EmployeeController> logger,
            ISqlConnectionService sqlConnectionService,
            EmployeeService employeeService,
            ExecuPayApiClient execuPayApiClient,
            EmployeeScheduledPaysService employeePayItemsService,
            PaymentAddressService paymentAddressService)
        {
            this.ePDATAContext = ePDATAContext;
            this.logger = logger;
            this.sqlConnectionService = sqlConnectionService;
            this.employeeService = employeeService;
            this.execuPayApiClient = execuPayApiClient;
            this.employeePayItemsService = employeePayItemsService;
            this.paymentAddressService = paymentAddressService;
        }

        [HttpGet("DeductionsAndPays")]
        public async Task<IActionResult> GetDeductionAndPays([FromRoute] int empnum)
        {
            try
            {
                logger.LogDebug("Entering GetDeductionAndPays. {Empnum}", empnum);
                await using var con = sqlConnectionService.GetSqlConnection();
                var dedsAndPays = await con.QueryAsync<prc_GetAutoPaysForPayroll>(@"custom.prc_GetAutoPaysForPayroll", new
                {
                    Conum = User.GetConum(),
                    Prnum = (int?)null,
                    EmployeeId = empnum,
                    RetunUnscheduled = true,
                    Allscheduled = true
                }, commandType: System.Data.CommandType.StoredProcedure);
                var keys = dedsAndPays.Select(d => d.RecordKey);
                List<EmployeeDeductionAmountSchedule> schedules = await ePDATAContext.EmployeeDeductionAmountSchedules
                    .Where(ed => (!ed.EndDate.HasValue || ed.EndDate > DateTime.Today) && keys.Contains(ed.DeductionKey))
                    .ToListAsync();


                var results = dedsAndPays
                    .Select(d => new EmployeeScheduledPayListItem(d, schedules.Where(s => s.DeductionKey == d.RecordKey).ToList()))
                    .ToList();

                var memos = await (from em in ePDATAContext.EmployeeMemos
                                   join m in ePDATAContext.Memos on new { em.Conum, em.MemoNumber } equals new { m.Conum, m.MemoNumber }
                                   where em.Conum == User.GetConum() && em.Empnum == empnum
                                   select new { EmployeeMemo = em, Memo = m }).ToListAsync();
                results.AddRange(memos.Select(m => new EmployeeScheduledPayListItem(m.EmployeeMemo, m.Memo)));

                return Ok(results);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetDeductionAndPays");
                throw;
            }
        }

        [HttpGet("Deductions/{key}")]
        public async Task<ActionResult<EmployeeDeduction>> GetDeduction([FromRoute] int empnum, [FromRoute] Guid key)
        {
            EmployeeDeduction deduction = await employeePayItemsService.GetDeductionAsync(User.GetConum(), empnum, key);
            return Ok(deduction);
        }

        [ApiExplorerSettings(GroupName = "v2")]
        [HttpPost("Deductions")]
        public async Task<ActionResult<EmployeeDeduction>> UpdateDeduction([FromRoute] int empnum, EmployeeDeduction deduction, [FromServices] EmployeeDeductionValidation validations)
        {
            deduction.Empnum = empnum;
            deduction.Conum = User.GetConum();
            var validationResult = await validations.ValidateAsync(deduction);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);
            EmployeeDeduction updatedDeduction = await employeePayItemsService.UpdateDeductionAsync(User.GetConum(), empnum, deduction);
            return Ok(updatedDeduction);
        }

        [HttpGet("Earnings/{key}")]
        public async Task<ActionResult<EmployeeEarning>> GetEarning([FromRoute] int empnum, [FromRoute] Guid key)
        {
            EmployeeEarning earning = await employeePayItemsService.GetEarningAsync(User.GetConum(), empnum, key);
            return Ok(earning);
        }

        [ApiExplorerSettings(GroupName = "v2")]
        [HttpPost("Earnings")]
        public async Task<ActionResult<EmployeeEarning>> UpdateEarning([FromRoute] int empnum, EmployeeEarning earning, [FromServices] EmployeeEarningValidation validations)
        {
            earning.Empnum = empnum;
            earning.Conum = User.GetConum();
            var validationResult = await validations.ValidateAsync(earning);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);
            EmployeeEarning deduction = await employeePayItemsService.UpdateEarningAsync(User.GetConum(), empnum, earning);
            return Ok(deduction);
        }

        [HttpGet("Deductions/Options")]
        public async Task<IActionResult> GetDeductionsOptions([FromRoute] int empnum)
        {
            logger.LogDebug("Entering GetDeductionsOptions");
            try
            {
                Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);

                var deductions = await ePDATAContext.Deductions
                    .Where(d => d.Conum == User.GetConum() && d.DACTIVE == "YES")
                    .ToListAsync();

                var results = deductions.Select(d => new EmployeeScheduledPayListItem(d, employee)).ToList();
                var calcs = await ePDATAContext.CoCalcs.Where(c => c.CONUM == User.GetConum()).Select(c => c.CALC_NAME).ToListAsync();
                var paymentAddresses = await paymentAddressService.GetPaymentAddressesAsync(User.GetConum());

                return Ok(new
                {
                    Deductions = results,
                    Calculations = calcs,
                    paymentAddresses
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error GetDeductionsOptions");
                throw;
            }
        }

        [HttpGet("Earnings/Options")]
        public async Task<IActionResult> GetEarningsOptions([FromRoute] int empnum)
        {
            logger.LogDebug("Entering GetEarningsOptions");
            try
            {
                Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);
                var earnings = await ePDATAContext.OtherPays
                    .Where(d => d.Conum == User.GetConum() && d.OACTIVE == "YES")
                    .ToListAsync();

                var results = earnings.Select(d => new EmployeeScheduledPayListItem(d, employee)).ToList();

                return Ok(new
                {
                    Earnings = results
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetEarningsOptions");
                throw;
            }
        }

        [HttpDelete("DeductionsAndPays")]
        public async Task<IActionResult> DeleteDeductionAndPays([FromRoute] int empnum, DeleteDeductionPayload employeeDeduction)
        {
            try
            {
                logger.LogDebug("Entering DeleteDeductionAndPays. {@employeeDeduction}", employeeDeduction);
                if (employeeDeduction.Type == 'D')
                {
                    var ded = await ePDATAContext.EmployeeDeductions.SingleOrDefaultAsync(ed => ed.deduction_key == employeeDeduction.Id && ed.Empnum == empnum && ed.Conum == User.GetConum());
                    if (ded == null)
                    {
                        return NotFound();
                    }
                    ePDATAContext.EmployeeDeductions.Remove(ded);
                }
                else if (employeeDeduction.Type == 'P')
                {
                    var pay = await ePDATAContext.EmployeeOtherPays.SingleOrDefaultAsync(ed => ed.otherpay_key == employeeDeduction.Id && ed.Empnum == empnum && ed.Conum == User.GetConum());
                    if (pay == null)
                    {
                        return NotFound();
                    }
                    ePDATAContext.EmployeeOtherPays.Remove(pay);
                }
                else if (employeeDeduction.Type == 'M')
                {
                    var mem = await ePDATAContext.EmployeeMemos.SingleOrDefaultAsync(ed => ed.memo_key == employeeDeduction.Id && ed.Empnum == empnum && ed.Conum == User.GetConum());
                    if (mem == null)
                    {
                        return NotFound();
                    }
                    ePDATAContext.EmployeeMemos.Remove(mem);
                }
                else
                {
                    throw new ArgumentException("Invalind Type", "Type");
                }
                await ePDATAContext.SaveChangesAsync();
                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in DeleteDeductionAndPays.");
                throw;
            }
        }


        [HttpGet("Memos/Options")]
        public async Task<IActionResult> GetMemosOptions([FromRoute] int empnum)
        {
            logger.LogDebug("Entering GetMemosOptions");
            try
            {
                Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);
                var savedMemos = await ePDATAContext.EmployeeMemos
                    .Where(d => d.Conum == User.GetConum() && d.Empnum == empnum)
                    .Select(d => d.MemoNumber)
                    .ToListAsync();

                var memos = await ePDATAContext.Memos
                    .Where(d => d.Conum == User.GetConum() && d.MACTIVE == "YES")
                    .Where(d => !savedMemos.Contains(d.MemoNumber))
                    .ToListAsync();

                var results = memos.Select(m => new EmployeeScheduledPayListItem(m, employee)).ToList();
                var calcs = await ePDATAContext.CoCalcs.Where(c => c.CONUM == User.GetConum()).Select(c => c.CALC_NAME).ToListAsync();

                return Ok(new
                {
                    Memos = results,
                    Calculations = calcs
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error GetMemosOptions");
                throw;
            }
        }

        [HttpGet("Memos/{key}")]
        public async Task<ActionResult<EmployeeMemo>> GetMemo([FromRoute] int empnum, [FromRoute] Guid key)
        {
            EmployeeMemo memo = await employeePayItemsService.GetMemoAsync(User.GetConum(), empnum, key);  
            return Ok(memo);
        }

        [ApiExplorerSettings(GroupName = "v2")]
        [HttpPost("Memos")]
        public async Task<IActionResult> UpdateMemo([FromRoute] int empnum, EmployeeMemo memo, [FromServices] EmployeeMemoValidation validations)
        {
            memo.Empnum = empnum;
            memo.Conum = User.GetConum();
            var validationResult = await validations.ValidateAsync(memo);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            var updatedMemo = await employeePayItemsService.UpdateMemoAsync(User.GetConum(), empnum, memo);
            return Ok(updatedMemo);
        }

        [HttpGet("TimeAndLabor")]
        public async Task<IActionResult> GetEmployeeTimeAndLabor([FromRoute] int empnum)
        {
            logger.LogDebug("Entering GetEmployeeTimeAndLabor. empNum: {empnum}", empnum);
            try
            {
                var result = await GetEmployeeTimeAndLaborAsync(empnum);
                return Ok(result);
            }
            catch (DatabaseRecordNotFoundException)
            {
                return NotFound("Employee not found");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetEmployeeTimeAndLabor");
                throw;
            }
        }

        [ApiExplorerSettings(GroupName = "v2")]
        [HttpPost("TimeAndLabor")]
        public async Task<ActionResult<EmployeeTimeAndLabor>> UpdateEmployeeTimeAndLabor([FromRoute] int empnum, EmployeeTimeAndLabor employeeTimeAndLabor)
        {
            logger.LogDebug("Entering UpdateEmployeeTimeAndLabor. empNum: {empnum}", empnum);
            try
            {
                var providerId = await employeeService.GetTimeAndLaborProviderId(User.GetConum());
                Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);
                employee.TlmInclude = employeeTimeAndLabor.TlmInclude.ToYesNo();

                var parameters = await ePDATAContext.IntegrationParameters
                    .Where(p => p.Conum == User.GetConum() && p.Empnum == empnum && (p.FormId == "hr_twp" || p.FormId == "hr_twp_state" || p.FormId == "hr_tlm" || p.FormId == "hr_SwipeClock"))
                    .ToListAsync();

                var aodDropDowns = await ePDATAContext.PPGlobalLists
                    .Where(_ => providerId == 1)
                    .Where(g => g.CoNum == User.GetConum() && g.list_name == "tlm names")
                    .Select(g => new
                    {
                        g.list_value,
                        g.list_subvalue,
                        g.doc_scan
                    }).ToListAsync();

                foreach (var card in employeeTimeAndLabor.Cards)
                {
                    var existingCard = parameters.FirstOrDefault(p => p.FieldName == card.Name);
                    if (existingCard != null)
                    {
                        if (!string.IsNullOrEmpty(card.Value))
                        {
                            existingCard.FieldValue = card.Value;
                            ePDATAContext.Update(existingCard);
                        }
                        else
                        {
                            ePDATAContext.Remove(existingCard);
                        }
                    }
                    else if (!string.IsNullOrEmpty(card.Value))
                    {
                        var newCard = new IntegrationParameter()
                        {
                            Id = Guid.NewGuid(),
                            Conum = User.GetConum(),
                            Empnum = empnum,
                            FormId = "hr_twp",
                            EffectiveDate = DateTime.Today,
                            RowGuid = Guid.NewGuid(),
                            FieldName = card.Name,
                            FieldValue = card.Value
                        };
                        await ePDATAContext.AddAsync(newCard);
                    }
                }

                foreach (var field in employeeTimeAndLabor.TlmFields)
                {
                    var existingField = parameters.FirstOrDefault(p => p.FieldName == field.DisplayName && p.FormId == field.SqlFormId);
                    if (existingField != null && field.Value.IsNullOrWhiteSpace())
                    {
                        ePDATAContext.Remove(existingField);
                    }
                    else if (field.Value.IsNotNullOrWhiteSpace())
                    {
                        if (existingField == null)
                        {
                            existingField = new IntegrationParameter()
                            {
                                Id = Guid.NewGuid(),
                                Conum = User.GetConum(),
                                Empnum = empnum,
                                FormId = field.SqlFormId,
                                EffectiveDate = DateTime.Today,
                                RowGuid = Guid.NewGuid(),
                                FieldName = field.DisplayName
                            };
                            ePDATAContext.Add(existingField);
                        }
                        if (providerId == 1 && field.DisplayName != "Badge Number")
                        {
                            var intValue = aodDropDowns.Where(g => g.list_value == field.DisplayName && g.list_subvalue == field.Value).Select(g => g.doc_scan).FirstOrDefault();
                            existingField.FieldValue = intValue?.ToString();
                        }
                        else
                        {
                            existingField.FieldValue = field.Value;
                        }
                    }
                }
                await ePDATAContext.SaveChangesAsync();

                try
                {
                    await using var con = sqlConnectionService.GetSqlConnection();
                    await con.ExecuteAsync("[custom].[prc_TriggerTwpEventLog]", null, commandType: System.Data.CommandType.StoredProcedure);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error in UpdateEmployeeTimeAndLabor > prc_TriggerTwpEventLog");
                }

                var result = await GetEmployeeTimeAndLaborAsync(empnum);
                if (providerId > 1)
                {
                    try
                    {

                        var data = await execuPayApiClient.TimeWorksPlusSync(User.GetConum(), new List<int> { empnum });
                        result.TimeWorksPlusResponse = data.FirstOrDefault();
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Error in UpdateEmployeeTimeAndLabor > TimeWorksPlusSync API");
                    }
                }
                else
                {
                    result.TimeWorksPlusResponse = new Models.ExecuPayApi.TimeWorksPlusResponse { IsSuccess = true };
                }
                return Ok(result);
            }
            catch (DatabaseRecordNotFoundException)
            {
                return NotFound("Employee not found");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdateEmployeeTimeAndLabor");
                throw;
            }
        }

        private async Task<EmployeeTimeAndLabor> GetEmployeeTimeAndLaborAsync(int empnum)
        {
            Employee_T employee = await employeeService.GetEmployeeAsync(User.GetConum(), empnum);
            var result = new EmployeeTimeAndLabor
            {
                TlmInclude = employee.TlmInclude.FromYesNo(),
                Cards = new List<EmployeeCard>(),
                TlmFields = new List<EmployeeTlmField>()
            };

            var data = await ePDATAContext.IntegrationAccounts
                .Where(i => i.conum == User.GetConum() && (i.provider_id == 1 || i.provider_id == 2 || i.provider_id == 18) && i.active == "YES")
                .Select(i => new
                {
                    i.provider_id,
                    i.data
                })
                .FirstOrDefaultAsync();

            if (data?.provider_id == 1)
            {
                await PopulateAODFields(empnum, result);
                return result;
            }

            if (data == null || data.data.IsNullOrWhiteSpace())
            {
                return result;
            }

            await PopulateCardNumbers(empnum, result, data.data);
            await PopulateTlmFields(empnum, result, data.data);

            return result;
        }

        private async Task PopulateTlmFields(int empnum, EmployeeTimeAndLabor result, string data)
        {
            IntegrationData integrationData = JsonSerializer.Deserialize<IntegrationData>(data);
            if (integrationData.ExportBlock == "1")
            {
                result.TlmFields.Add(new EmployeeTlmField("Export Block", "hr_twp"));
            }
            if (integrationData.WebClockEnabled == "1")
            {
                result.TlmFields.Add(new EmployeeTlmField("Web Clock Enabled", "hr_twp"));
            }
            if (integrationData.Variables?.OPTIONS == "1")
            {
                result.TlmFields.Add(new EmployeeTlmField("Options", "hr_twp"));
            }
            if (integrationData.InitialPassword == "1")
            {
                result.TlmFields.Add(new EmployeeTlmField("Password", "hr_twp"));
            }
            if (integrationData.EnforceSchedule == "1")
            {
                result.TlmFields.Add(new EmployeeTlmField("Schedule", "hr_twp"));
            }
            if (integrationData.Variables?.OTTHRESHOLD == "1")
            {
                result.TlmFields.Add(new EmployeeTlmField("OTTHRESHOLD", "hr_twp"));
            }
            if (integrationData.Variables?.EXTEMPLOYEEID == "1")
            {
                result.TlmFields.Add(new EmployeeTlmField("EXTEMPLOYEEID", "hr_twp"));
            }

            var variables = integrationData.States?.FirstOrDefault()?.Variables;
            if (variables != null)
            {
                if (variables.HOME1 == "1")
                {
                    result.TlmFields.Add(new EmployeeTlmField("Home1", "hr_twp_state"));
                }
                if (variables.HOME2 == "1")
                {
                    result.TlmFields.Add(new EmployeeTlmField("Home2", "hr_twp_state"));
                }
                if (variables.HOME3 == "1")
                {
                    result.TlmFields.Add(new EmployeeTlmField("Home3", "hr_twp_state"));
                }
                if (variables.PAYRATE1 == "1")
                {
                    result.TlmFields.Add(new EmployeeTlmField("Pay Rate1", "hr_twp_state"));
                }
                if (variables.AUTOLUNCHHOURS == "1")
                {
                    result.TlmFields.Add(new EmployeeTlmField("Lunch Hours", "hr_twp_state"));
                }
                if (variables.PAYRATE2 == "1")
                {
                    result.TlmFields.Add(new EmployeeTlmField("Pay Rate2", "hr_twp_state"));
                }
                if (variables.LUNCHMINUTES == "1")
                {
                    result.TlmFields.Add(new EmployeeTlmField("Lunch", "hr_twp_state"));
                }
                if (variables.MAXHOURS == "1")
                {
                    result.TlmFields.Add(new EmployeeTlmField("MAXHOURS", "hr_twp_state"));
                }
                if (variables.SCHEDULE == "1")
                {
                    result.TlmFields.Add(new EmployeeTlmField("SCHEDULE", "hr_twp_state"));
                }
            }

            var parameters = await ePDATAContext.IntegrationParameters
                .Where(p => p.Conum == User.GetConum() && p.Empnum == empnum && (p.FormId == "hr_twp" || p.FormId == "hr_twp_state"))
                .ToListAsync();

            foreach (var field in result.TlmFields)
            {
                field.Value = parameters.FirstOrDefault(p => p.FieldName == field.DisplayName && p.FormId == field.SqlFormId)?.FieldValue;
            }
        }

        private async Task PopulateCardNumbers(int empnum, EmployeeTimeAndLabor result, string data)
        {
            var parsedData = JsonDocument.Parse(data);
            JsonElement identifiersJsonElement;
            if (!parsedData.RootElement.TryGetProperty("Identifiers", out identifiersJsonElement) || (identifiersJsonElement.ToString().IsNullOrWhiteSpace()))
            {
                return;
            }
            JsonElement idJsonElement = new JsonElement();
            var identifiers = identifiersJsonElement.EnumerateArray();
            if (identifiers.Count() == 0 || !identifiers.All(id => id.TryGetProperty("Id", out idJsonElement)))
            {
                return;
            }

            int cardNamesCount;
            if (!int.TryParse(idJsonElement.ToString(), out cardNamesCount))
            {
                return;
            }

            var cardNames = new List<string>();
            for (int i = 1; i <= cardNamesCount; i++)
            {
                cardNames.Add($"Card Number{i}");
            }
            var parameters = await ePDATAContext.IntegrationParameters
                .Where(p => p.Conum == User.GetConum() && p.Empnum == empnum && cardNames.Contains(p.FieldName))
                .ToListAsync();

            foreach (var item in cardNames)
            {
                result.Cards.Add(new EmployeeCard() { Name = item, Value = parameters.FirstOrDefault(p => p.FieldName == item)?.FieldValue });
            }
        }

        private async Task PopulateAODFields(int empnum, EmployeeTimeAndLabor result)
        {
            var dropDowns = await ePDATAContext.PPGlobalLists
                .Where(g => g.CoNum == User.GetConum() && g.list_name == "tlm names")
                .Select(g => new
                {
                    g.list_value,
                    g.list_subvalue,
                    g.doc_scan
                }).ToListAsync();

            var values = await ePDATAContext.IntegrationParameters
                .Where(p => p.Conum == User.GetConum() && p.Empnum == empnum && p.FormId == "hr_tlm")
                .Select(p => new
                {
                    p.FieldName,
                    p.FieldValue
                }).ToListAsync();


            result.TlmFields.Add(new EmployeeTlmField("Clock Group", "hr_tlm")
            {
                DropDownValues = dropDowns.Where(g => g.list_value == "Clock Group").Select(g => g.list_subvalue).ToArray(),
                Value = values.FirstOrDefault(v => v.FieldName == "Clock Group")?.FieldValue
            });

            result.TlmFields.Add(new EmployeeTlmField("Payclass Id", "hr_tlm")
            {
                DropDownValues = dropDowns.Where(g => g.list_value == "Payclass Id").Select(g => g.list_subvalue).ToArray(),
                Value = values.FirstOrDefault(v => v.FieldName == "Payclass Id")?.FieldValue
            });

            result.TlmFields.Add(new EmployeeTlmField("Schedule Pattern Id", "hr_tlm")
            {
                DropDownValues = dropDowns.Where(g => g.list_value == "Schedule Pattern Id").Select(g => g.list_subvalue).ToArray(),
                Value = values.FirstOrDefault(v => v.FieldName == "Schedule Pattern Id")?.FieldValue
            });

            foreach (var field in result.TlmFields)
            {
                var fieldValue = values.FirstOrDefault(v => v.FieldName == field.DisplayName)?.FieldValue;
                if (fieldValue == null)
                {
                    continue;
                }
                else if (int.TryParse(fieldValue, out int i))
                {
                    var stringValue = dropDowns.Where(g => g.list_value == field.DisplayName && g.doc_scan == i).Select(g => g.list_subvalue).FirstOrDefault();
                    field.Value = stringValue;
                }
                else
                {
                    throw new BrandsWebApp.Exceptions.BadRequestException($"Invalid value. field: [{field.DisplayName}] value: [{fieldValue}]");
                }
            }

            result.TlmFields.Insert(0, new EmployeeTlmField("Badge Number", "hr_tlm")
            {
                Value = values.FirstOrDefault(v => v.FieldName == "Badge Number")?.FieldValue
            });
        }

        public class DeleteDeductionPayload
        {
            public char Type { get; set; }
            public Guid Id { get; set; }
        }

        public class IntegrationData
        {
            public string RecordNumber { get; set; }
            public string EmployeeCode { get; set; }
            public string FirstName { get; set; }
            public string MiddleName { get; set; }
            public string LastName { get; set; }
            public string Designation { get; set; }
            public string Phone { get; set; }
            public string Email { get; set; }
            public string StartDate { get; set; }
            public string EndDate { get; set; }
            public string ExportBlock { get; set; }
            public string WebClockEnabled { get; set; }
            public string EnforceSchedule { get; set; }
            public string Name2 { get; set; }
            public string InitialPassword { get; set; }
            public Variables Variables { get; set; }
            public List<State> States { get; set; }
        }

        public class Variables
        {
            public string OPTIONS { get; set; }
            public string OTTHRESHOLD { get; set; }
            public string EXTEMPLOYEEID { get; set; }
        }

        public class State
        {
            public StateVariables Variables { get; set; }
        }

        public class StateVariables
        {
            public string EMPLOYEETYPE { get; set; }
            public string TITLE { get; set; }
            public string DEPARTMENT { get; set; }
            public string LOCATION { get; set; }
            public string SUPERVISOR { get; set; }
            public string HOME1 { get; set; }
            public string HOME2 { get; set; }
            public string HOME3 { get; set; }
            public string PAYRATE0 { get; set; }
            public string PAYRATE1 { get; set; }
            public string AUTOLUNCHHOURS { get; set; }
            public string PAYRATE2 { get; set; }
            public string LUNCHMINUTES { get; set; }
            public string MAXHOURS { get; set; }
            public string SCHEDULE { get; set; }
        }
    }
}
