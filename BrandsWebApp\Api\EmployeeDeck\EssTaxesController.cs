﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Filters;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.Ess.Options;
using BrandsWebApp.Models.Ess.Taxes;
using BrandsWebApp.Services.EmployeeDeck;
using BrandsWebApp.Services.Pdf;
using BrandsWebApp.Services.Pdf.Templates;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.EmployeeDeck
{
    [Authorize(Policy = AuthPolicies.IsEssUserOrOnboarding)]
    [Route("api/employee-deck/my/taxes")]
    [ApiController]
    public class EssTaxesController : ControllerBase
    {
        private readonly EssTaxService essTaxService;
        private readonly JwtFactory jwtFactory;
        private readonly EssOptions essOptions;
        private readonly PdfFillerService pdfFillerService;
        private readonly ILogger<EssTaxesController> logger;
        private readonly EssOnboardingService essOnboardingService;

        public EssTaxesController (EssTaxService essTaxService, JwtFactory jwtFactory, EssOptions essOptions, PdfFillerService pdfFillerService, ILogger<EssTaxesController> logger, EssOnboardingService essOnboardingService)
        {
            this.essTaxService = essTaxService;
            this.jwtFactory = jwtFactory;
            this.essOptions = essOptions;
            this.pdfFillerService = pdfFillerService;
            this.logger = logger;
            this.essOnboardingService = essOnboardingService;
        }

        [ServiceFilter(typeof(AccessEssW4AsyncFilter))]
        [HttpGet]
        public async Task<ActionResult> GetEmployeeW4Info()
        {
            var result = await essTaxService.GetEmployeeTaxesAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value, User.IsEssOnboarding());
            return Ok(result);
        }

        [ServiceFilter(typeof(AccessEssW4AsyncFilter))]
        [HttpGet("federal")]
        public async Task<ActionResult> GetEmployeeFederalTaxesInfo()
        {
            var result = await essTaxService.GetEmployeeFederalTaxesAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value, User.IsEssOnboarding());
            return Ok(result);
        }

        [ServiceFilter(typeof(AccessEssW4AsyncFilter))]
        [HttpGet("state")]
        public async Task<ActionResult> GetEmployeeStateTaxesInfo()
        {
            var result = await essTaxService.GetEmployeeStateTaxesAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value, User.IsEssOnboarding());
            return Ok(result);
        }

        [ServiceFilter(typeof(AccessEssW4AsyncFilter))]
        [HttpGet("review")]
        public ActionResult ReviewEmployeeW4InfoAsync([FromQuery] bool signed)
        {
            var path = signed ? "federal/signed/W4.pdf" : "review/W4.pdf";
            var downloadUrl = EmployeeW4PdfAuthUrl(path);
            return Ok(new
            {
                DownloadURL = downloadUrl
            });
        }

        [HttpGet("review/W4.pdf")]
        [AllowAnonymous]
        public async Task<ActionResult> GetEmployeeW4Pdf([FromQuery] string token)
        {
            if (token.IsNullOrWhiteSpace())
            {
                return BadRequest("Token query param is required");
            }

            var docPath = "review/W4.pdf";
            var baseUrl = EmployeeW4PdfUrl(docPath);
            var jwt = jwtFactory.ValidateResourceExpiringToken(baseUrl, token);
            var coNumClaim = jwt.Claims.FirstOrDefault(c => c.Type == "EssCoNum");
            var empNumClaim = jwt.Claims.FirstOrDefault(c => c.Type == "EssEmpNum");
            decimal coNum = 0;
            decimal empNum = 0;
            if (coNumClaim == null || empNumClaim == null || !decimal.TryParse(coNumClaim.Value, out coNum) || !decimal.TryParse(empNumClaim.Value, out empNum))
            {
                return Unauthorized();
            }

            var isEssOnboardingClaim = jwt.Claims.FirstOrDefault(c => c.Type == "IsEssOnboarding");
            var isEssOnboarding = false;
            if (isEssOnboardingClaim != null)
            {
                bool.TryParse(isEssOnboardingClaim.Value, out isEssOnboarding);
            }

            var info = await essTaxService.GetEmployeeFederalTaxesAsync(coNum, empNum, isEssOnboarding);
            var stream = await CreateW4PDFStream(coNum, empNum, isEssOnboarding, info);

            var fileDate = DateTime.Now;
            var fileName = $"W4_Review_{fileDate:yyyy-MM-dd}.pdf";
            var fileContentType = "application/pdf";
            this.Response.Headers.Add("X-Filename", fileName);
            this.Response.Headers.Add("Content-Disposition", $"inline; filename=\"{fileName}\"");

            // TODO: Fix the error that happens AFTER serving inline file.
            // NOTE: The call with 3 arguments to `return File` works fine...
            // return File(stream, fileContentType, fileName);
            return File(stream, fileContentType);
        }

        [AllowAnonymous]
        [HttpGet("{taxType}/signed/W4.pdf")]
        public async Task<IActionResult> GetFileAsBlob([FromRoute] string taxType, [FromQuery] string token)
        {
            if (taxType.ToLower() != "federal" && taxType.ToLower() != "state")
            {
                return BadRequest("Supported tax types are 'federal' and 'state'");
            }

            if (token.IsNullOrWhiteSpace())
            {
                return BadRequest("Token query param is required");
            }

            var docPath = $"{taxType}/signed/W4.pdf";
            var baseUrl = EmployeeW4PdfUrl(docPath);
            var jwt = jwtFactory.ValidateResourceExpiringToken(baseUrl, token);
            var coNumClaim = jwt.Claims.FirstOrDefault(c => c.Type == "EssCoNum");
            var empNumClaim = jwt.Claims.FirstOrDefault(c => c.Type == "EssEmpNum");
            decimal coNum = 0;
            decimal empNum = 0;
            if (coNumClaim == null || empNumClaim == null || !decimal.TryParse(coNumClaim.Value, out coNum) || !decimal.TryParse(empNumClaim.Value, out empNum))
            {
                return Unauthorized();
            }

            var isEssOnboardingClaim = jwt.Claims.FirstOrDefault(c => c.Type == "IsEssOnboarding");
            var isEssOnboarding = false;
            if (isEssOnboardingClaim != null)
            {
                bool.TryParse(isEssOnboardingClaim.Value, out isEssOnboarding);
            }

            var document = await essTaxService.GetW4SignedFormAsync(coNum, empNum, taxType, isEssOnboarding);
            if (document != null && document.Data != null)
            {
                return File(document.Data, "application/pdf", document.Name);
            }

            return NotFound();
        }

        [ServiceFilter(typeof(AccessEssW4AsyncFilter))]
        [HttpPut]
        public async Task<ActionResult> UpdateEmployeeFederalTaxes([FromBody] UpdateEssFederalTaxes info)
        {
            try
            {
                // Automatically set 2020 form style.
                info.FormStyle = W4Style.Post2020;
                await essTaxService.UpdateEmployeeFederalTaxesInfoAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value, User.IsEssOnboarding(), info);
                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdateEmployeeFederalTaxes");
                throw;
            }
        }

        [ServiceFilter(typeof(AccessEssW4AsyncFilter))]
        [HttpPost("signature")]
        public async Task<ActionResult> SignEmployeeFederalTaxesForm([FromForm] SignTaxesForm signForm)
        {
            if (signForm == null || signForm.Signature == null)
            {
                return BadRequest("Signature is required");
            }

            try
            {
                var conum = User.GetEssEmpConum().Value;
                var empnum = User.GetEssEmpNum().Value;
                var isOnboarding = User.IsEssOnboarding();
                if (isOnboarding)
                {
                    var employee = await essOnboardingService.GetActiveOnboardingEmployeeAsync(conum, empnum);
                    if (!employee.IsFederalTaxesInfoAdded)
                    {
                        return BadRequest("Federal taxes step must be completed before signing the form");
                    }
                }
                var info = await essTaxService.GetEmployeeFederalTaxesAsync(conum, empnum, isOnboarding);
                var titleSuffix = "-FedW4-Signed.pdf";
                using (var stream = await CreateW4PDFStream(conum, empnum, isOnboarding, info, signForm.Signature))
                {
                    await essTaxService.SaveW4DocumentAsync(conum, empnum, isOnboarding, User.GetFullName(), titleSuffix, stream);
                }

                return Ok(new
                {
                    DownloadURL = EmployeeW4PdfAuthUrl($"federal/signed/W4.pdf"),
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in SignEmployeeFederalTaxesForm");
                throw;
            }
        }

        [ServiceFilter(typeof(AccessEssW4AsyncFilter))]
        [HttpPut("state")]
        public async Task<ActionResult> UpdateEmployeeStateTaxes([FromBody] UpdateEssStateTaxes info)
        {
            await essTaxService.UpdateEmployeeStateTaxesInfoAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value, User.IsEssOnboarding(), info);
            return NoContent();
        }

        [ServiceFilter(typeof(AccessEssW4AsyncFilter))]
        [HttpPost("state/signature")]
        public async Task<ActionResult> UpdateEmployeeStateTaxes([FromForm] SignTaxesForm signForm)
        {
            if (signForm == null || signForm.Signature == null)
            {
                return BadRequest("Signature is required");
            }

            var conum = User.GetEssEmpConum().Value;
            var empnum = User.GetEssEmpNum().Value;
            var isOnboarding = User.IsEssOnboarding();
            if (isOnboarding)
            {
                var employee = await essOnboardingService.GetActiveOnboardingEmployeeAsync(conum, empnum);
                if (!employee.IsStateTaxesInfoAdded)
                {
                    return BadRequest("State taxes step must be completed before signing the form");
                }
            }

            var info = (await essTaxService.GetEmployeeStateTaxesAsync(conum, empnum, isOnboarding))?.WithFlattenedLocals();
            var titleSuffix = info.StateAbbr == "NY" ? "-NYIT2104-Signed.pdf" : "-NJW4-Signed.pdf";

            using (var stream = await CreateStateW4PDFStream(conum, empnum, isOnboarding, info, signForm.Signature))
            {
                await essTaxService.SaveW4DocumentAsync(conum, empnum, isOnboarding, User.GetFullName(), titleSuffix, stream);
            }

            return Ok(new
            {
                DownloadURL = EmployeeW4PdfAuthUrl($"state/signed/W4.pdf")
            });
        }

        [ServiceFilter(typeof(AccessEssW4AsyncFilter))]
        [HttpGet("state/review")]
        public ActionResult ReviewEmployeeStateW4Info([FromQuery] bool signed)
        {
            var path = signed ? "state/signed/W4.pdf" : "state/review/W4.pdf";
            return Ok(new
            {
                DownloadURL = EmployeeW4PdfAuthUrl(path),
            });
        }

        [HttpGet("state/review/W4.pdf")]
        [AllowAnonymous]
        public async Task<ActionResult> GetEmployeeStateW4PdfForReview([FromQuery] string token)
        {
            if (token.IsNullOrWhiteSpace())
            {
                return BadRequest("Token query param is required");
            }

            var docPath = $"state/review/W4.pdf";
            var baseUrl = EmployeeW4PdfUrl(docPath);
            var jwt = jwtFactory.ValidateResourceExpiringToken(baseUrl, token);

            var coNumClaim = jwt.Claims.FirstOrDefault(c => c.Type == "EssCoNum");
            var empNumClaim = jwt.Claims.FirstOrDefault(c => c.Type == "EssEmpNum");
            decimal coNum = 0;
            decimal empNum = 0;
            if (coNumClaim == null || empNumClaim == null || !decimal.TryParse(coNumClaim.Value, out coNum) || !decimal.TryParse(empNumClaim.Value, out empNum))
            {
                return Unauthorized();
            }

            var isEssOnboardingClaim = jwt.Claims.FirstOrDefault(c => c.Type == "IsEssOnboarding");
            var isEssOnboarding = false;
            if (isEssOnboardingClaim != null)
            {
                bool.TryParse(isEssOnboardingClaim.Value, out isEssOnboarding);
            }

            var info = (await essTaxService.GetEmployeeStateTaxesAsync(coNum, empNum, isEssOnboarding))?.WithFlattenedLocals();
            var stream = await CreateStateW4PDFStream(coNum, empNum, isEssOnboarding, info);

            var fileDate = DateTime.Now;
            var fileName = $"W4_Review_{fileDate:yyyy-MM-dd}.pdf";
            var fileContentType = "application/pdf";
            this.Response.Headers.Add("X-Filename", fileName);
            this.Response.Headers.Add("Content-Disposition", $"inline; filename=\"{fileName}\"");

            // TODO: Fix the error that happens AFTER serving inline file.
            // NOTE: The call with 3 arguments to `return File` works fine...
            // return File(stream, fileContentType, fileName);
            return File(stream, fileContentType);
        }

        private string EmployeeW4PdfAuthUrl(string path)
        {
            var baseUrl = EmployeeW4PdfUrl(path);
            var empCoNumClaim = new Claim("EssCoNum", User.GetEssEmpConum().Value.ToString());
            var empNumClaim = new Claim("EssEmpNum", User.GetEssEmpNum().Value.ToString());
            var isEssOnboarding = new Claim("IsEssOnboarding", User.IsEssOnboarding().ToString());
            var token = jwtFactory.IssueResourceExpiringToken(baseUrl, TimeSpan.FromMinutes(15), empCoNumClaim, empNumClaim, isEssOnboarding);
            return $"/{baseUrl}?token={token}";
        }

        private string EmployeeW4PdfUrl(string path)
        {
            var url = $"api/employee-deck/my/taxes/{path}";          
            return url;
        }

        private async Task<MemoryStream> CreateW4PDFStream(decimal coNum, decimal empNum, bool isOnboarding, UpdateEssFederalTaxes w4Info, IFormFile signature = null)
        {
            var personalInfo = await essTaxService.GetEmployeeW4PersonalInfoAsync(coNum, empNum, isOnboarding);
            var federalTaxes = await essTaxService.GetEmployeeFederalTaxesAsync(coNum, empNum, isOnboarding);
            var stream = new MemoryStream();
            var options = FederalW4.CreateFillOptions(personalInfo, w4Info, signature);

            string path = Path.Combine(essOptions.PdfFormsBasePath, options.Template.FileName);
            pdfFillerService.Fill(path, options, stream);
            return stream;
        }

        private async Task<MemoryStream> CreateStateW4PDFStream(decimal coNum, decimal empNum, bool isOnboarding, UpdateEssStateTaxes w4Info, IFormFile signature = null)
        {
            var personalInfo = await essTaxService.GetEmployeeW4PersonalInfoAsync(coNum, empNum, isOnboarding);
            var stream = new MemoryStream();
            var options = w4Info.StateAbbr == "NY" ? NY_IT2104.CreateFillOptions(personalInfo, w4Info, signature) : NJ_W4.CreateFillOptions(personalInfo, w4Info, signature);

            string path = Path.Combine(essOptions.PdfFormsBasePath, options.Template.FileName);
            pdfFillerService.Fill(path, options, stream);

            return stream;
        }
    }
}
