﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Exceptions;
using BrandsWebApp.Models;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Reports
{
    [Route("api/QbReports/{conum}")]
    [ApiController]
    [Authorize(Policy = AuthPolicies.PayrollExportOrCpa)]
    public class QbReportsController : ControllerBase
    {
        private readonly ILogger<QbReportsController> logger;
        private readonly QbReportsService qbReportsService;
        private readonly ReportQueuePathCredentials reportQueueCreds;
        private readonly IConfiguration configuration;

        public QbReportsController(
            ILogger<QbReportsController> logger,
            QbReportsService qbReportsService,
            IOptionsSnapshot<ReportQueuePathCredentials> reportQueueCreds,
            IConfiguration configuration)
        {
            this.logger = logger;
            this.qbReportsService = qbReportsService;
            this.reportQueueCreds = reportQueueCreds.Value;
            this.configuration = configuration;
        }

        [HttpGet]
        public async Task<IActionResult> GetQbReportsAsync([FromRoute] decimal conum, [FromQuery] bool recentOnly, [FromQuery] DateTime? fromDate, [FromQuery] DateTime? toDate)
        {
            logger.LogDebug("Entering GetQbReportsAsync.");

            var list = await qbReportsService.GetQbReportsAsync(conum, recentOnly, fromDate, toDate);

            return Ok(list);
        }

        [HttpGet("download/{id}")]
        public async Task<IActionResult> DownloadQbReport([FromRoute] decimal conum, [FromRoute] int id)
        {
            logger.LogDebug("Entering DownloadQbReport. {conum} {id}", conum, id);
            try
            {
                var qbFile = await qbReportsService.GetQbReportsById(id);
                if (qbFile == null)
                {
                    logger.LogWarning("DownloadQbReport Id: {id} not found", id);
                    return NotFound();
                }
                else if (qbFile.CoNum != conum)
                {
                    logger.LogWarning("MISMATCH CONUM. DownloadQbReport fileId: {id} Route Conum: {conum} File Conum {FileConum}", id, conum, qbFile.CoNum);
                    return NotFound();
                }

                string qbReportsUrl = configuration.GetValue<string>("QbReportsUrl");
                var apiKey = reportQueueCreds.ReportsUrlApiKey ?? throw new ArgumentNullException("ReportsUrlApiKey", "ReportsUrlApiKey is null");
                logger.LogDebug("QbReportsUrl: {QbReportsUrl} {ApiKey}", qbReportsUrl, apiKey);
                string fileName = System.IO.Path.GetFileName(qbFile.FileName);
                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Add("ApiKey", apiKey);
                    var result = await client.GetStreamAsync($"{qbReportsUrl}/{fileName}");
                    logger.LogDebug("PowerImportsApi Response. {result}", result);
                    new FileExtensionContentTypeProvider().TryGetContentType(fileName, out string contentType);
                    return File(result, contentType ?? "application/qbooks");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in DownloadQbReport");
                throw;
            }
        }

        [HttpPost("Completed/{reportId}")]
        public async Task<IActionResult> AddCompletedReport([FromRoute] int reportId)
        {
            logger.LogDebug("Entering AddCompletedReport.");

            try
            {
                await qbReportsService.AddCompletedQbReportAsync(reportId, User.GetPaydeckUserId());
            }
            catch (DatabaseRecordNotFoundException e)
            {
                logger.LogWarning(e.Message);
                return NotFound(e.Message);
            }
            catch (InvalidOperationException e)
            {
                logger.LogWarning(e.Message);
                return BadRequest(e.Message);
            }

            return NoContent();
        }

        [HttpDelete("Completed/{reportId}")]
        public async Task<IActionResult> RemoveCompletedReport([FromRoute] int reportId)
        {
            logger.LogDebug("Entering RemoveCompletedReport.");

            try
            {
                await qbReportsService.RemoveCompletedQbReportAsync(reportId);
            }
            catch (DatabaseRecordNotFoundException e)
            {
                logger.LogWarning(e.Message);
                return NotFound(e.Message);
            }

            return NoContent();
        }
    }
}
