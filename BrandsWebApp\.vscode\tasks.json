{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/BrandsWebApp.csproj"], "problemMatcher": "$tsc"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/BrandsWebApp.csproj"], "problemMatcher": "$tsc"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "${workspaceFolder}/BrandsWebApp.csproj"], "problemMatcher": "$tsc"}]}