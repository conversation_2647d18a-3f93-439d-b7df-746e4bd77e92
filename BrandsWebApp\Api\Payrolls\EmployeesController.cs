﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Constants;
using BrandsWebApp.Exceptions;
using BrandsWebApp.Filters;
using BrandsWebApp.Models.Payroll.Check;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Payroll;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Payrolls
{
    [Route("api/Payrolls/{payrollNumber}/Employees")]
    [ApiController]
    [Authorize(Policy = nameof(Permission.PaydeckPayroll))]
    public class EmployeesController : ControllerBase
    {
        private readonly StatesService statesService;
        private readonly EmployeeService employeeService;
        private readonly EmployeeDirectDepositService employeeDirectDepositService;
        private readonly PayrollChecksService payrollChecksService;

        public EmployeesController(
            StatesService statesService,
            EmployeeService employeeService,
            EmployeeDirectDepositService employeeDirectDepositService,
            PayrollChecksService payrollChecksService)
        {
            this.statesService = statesService;
            this.employeeService = employeeService;
            this.employeeDirectDepositService = employeeDirectDepositService;
            this.payrollChecksService = payrollChecksService;
        }

        /// <summary>
        /// Gets unemployment states
        /// </summary>
        [HttpGet("UnemploymentStates")]
        public async Task<IActionResult> GetUnemploymentStates()
        {
            var states = (await statesService.GetAvailableStatesAsync(User.GetConum(), true)).Select(s => s.State);
            return Ok(states);
        }

        /// <summary>
        /// Gets withholding states
        /// </summary>
        [HttpGet("{employeeNumber}/WithholdingStates")]
        public async Task<IActionResult> GetWithholdingStates([FromRoute] int employeeNumber)
        {
            var states = (await statesService.GetEligibleWorkStatesAsync(User.GetConum(), employeeNumber)).Select(s => s.State);
            return Ok(states);
        }

        /// <summary>
        /// Gets pay history
        /// </summary>
        [HttpGet("{employeeNumber}")]
        public async Task<IActionResult> GetEmployeeInfo([FromRoute] int employeeNumber, [FromQuery] int checksCount)
        {
            var employee = await employeeService.GetEmployeeInfoAsync(User.GetConum(), employeeNumber, checksCount);
            return Ok(employee);
        }

        /// <summary>
        /// Gets local tax types
        /// </summary>
        [HttpGet("{employeeNumber}/LocalTaxTypes")]
        public async Task<IActionResult> GetEmployeeLocalTaxTypes([FromRoute] int employeeNumber)
        {
            var localTaxTypes = await employeeService.GetEmployeeLocalTaxTypesAsync(User.GetConum(), employeeNumber);
            return Ok(localTaxTypes);
        }

        /// <summary>
        /// Updates type for checks in current payroll to direct deposit and activates direct deposit accounts for employee
        /// </summary>        
        [ServiceFilter(typeof(UpdatePayrollAsyncFilter))]
        [HttpPost("{employeeNumber}/DirectDeposit")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<Check>> UpdateChecksTypeToDirectDeposit([FromRoute] decimal payrollNumber, [FromRoute] decimal employeeNumber)
        {
            if (!Request.Query.TryGetValue(Models.Constants.CHECK_UPDATE_GUID_QUERY_NAME, out var key))
            {
                throw new BadRequestException(ErrorMessages.MissingPayrollCheckUpdateGuid);
            }

            await employeeDirectDepositService.ActivateDirectDepositAccountsAsync(User.GetConum(), employeeNumber);
            var result = await payrollChecksService.UpdateChecksToDirectDepositAsync(User.GetConum(), payrollNumber, employeeNumber);
            return Ok(result);
        }
    }
}
