﻿using BrandsWebApp.Models;
using BrandsWebApp.Services;
using CsvHelper;
using CsvHelper.Configuration;
using CsvHelper.TypeConversion;
using Flurl.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using static DevExpress.Data.Filtering.Helpers.SubExprHelper;

namespace BrandsWebApp.Api.Payrolls
{
    [Route("api/Payrolls/tlm")]
    [ApiController]
    [Authorize(Policy = "no_body_should_have_yet")]
    public class TlmController : ControllerBase
    {
        private readonly SwipeClockApiService swipeClockApiService;
        private readonly ILogger<TlmController> logger;
        private readonly IOptionsSnapshot<SwipeClockOptions> options;

        public TlmController(SwipeClockApiService swipeClockApiService, ILogger<TlmController> logger, IOptionsSnapshot<Models.SwipeClockOptions> options)
        {
            this.swipeClockApiService = swipeClockApiService;
            this.logger = logger;
            this.options = options;
        }

        [HttpGet]
        public async Task<IActionResult> Get([FromQuery] decimal conum, [FromQuery] string ids, [FromQuery] string beginDate, [FromQuery] string endDate)
        {
            logger.LogDebug("Entering Get");
            try
            {
                int? siteId = await swipeClockApiService.GetSwipeClockSiteId(conum); //89092
                if (!siteId.HasValue)
                {
                    return null;
                }
                var token = await swipeClockApiService.GetSwipeClockToken(siteId.Value, "login", options.Value.LoginName, "twppartner", "partner");
                var payrollActivitiesResults = await new Flurl.Url("https://twpapi.payrollservers.us/api/")
                   .AppendPathSegments(siteId, "payrollactivities")
                   .SetQueryParams(new { beginDate, endDate, format = "sum3csv", ids = ids }, Flurl.NullValueHandling.Ignore)
                   .WithOAuthBearerToken(token)
                   .AllowAnyHttpStatus()
                   .GetAsync();
                logger.LogDebug("SwipeClock payrollactivities result. {StatusCode}", payrollActivitiesResults.StatusCode);
                if (payrollActivitiesResults.StatusCode == Microsoft.AspNetCore.Http.StatusCodes.Status200OK)
                {
                    var payrollActivitiesResponse = await payrollActivitiesResults.GetJsonAsync<PayrollActivitiesResponse>();
                    var config = new CsvConfiguration(CultureInfo.InvariantCulture)
                    {
                        HasHeaderRecord = true,
                        Mode = CsvMode.RFC4180
                    };
                    using (var reader = new StringReader(payrollActivitiesResponse.FormatString))
                    using (var csv = new CsvReader(reader, config))
                    {
                        var records = csv.GetRecords<PayrollRecord>().ToList();
                        return Ok(new
                        {
                            payrollActivitiesResponse,
                            records
                        });
                    }
                }
                else
                {
                    throw new Exception($"Invalid status code {payrollActivitiesResults.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetPayrollActivities");
                throw;
            }
        }

        public class PayrollActivitiesResponse
        {
            public string Error { get; set; }
            public string FormatBinary { get; set; }
            public string FormatString { get; set; }
            public string MimeType { get; set; }
        }

        public class PayrollRecord
        {
            public string RecordType { get; set; }
            public string ClientName { get; set; }
            public string ClientTag { get; set; }
            public decimal EmployeeCode { get; set; }
            public string EmployeeName { get; set; }
            public string EmployeeSSN { get; set; }
            public string Category { get; set; }
            public decimal? CategoryCode { get; set; }
            public decimal? Hours { get; set; }
            public decimal? PayRate { get; set; }
            public decimal? PayRateUnmultiplied { get; set; }
            public string X { get; set; }
            public string Y { get; set; }
            public string Z { get; set; }
            public decimal? Pay { get; set; }
            public DateTime? StartDate { get; set; }
            public DateTime? EndDate { get; set; }
            public string HomeDepartment { get; set; }
            public string HomeLocation { get; set; }
            public string HomeSupervisor { get; set; }
        }

    }
}
