﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [ApiController]
    [Route("api/HubConnections")]
    public class HubConnectionsController : Controller
    {
        private readonly Brands.DAL.EPDATAContext ePDATAContext;
        private readonly ILogger<HubConnectionsController> logger;
        private const string hubKeyUdf = "X-API-SECRET-HUBCONNECTIONS-KEY";

        public HubConnectionsController(Brands.DAL.EPDATAContext ePDATAContext, ILogger<HubConnectionsController> logger)
        {
            this.ePDATAContext = ePDATAContext;
            this.logger = logger;
        }

        [HttpGet("MainHub")]
        public async Task<IActionResult> GetMainHubConnections()
        {
            try
            {
                var hubKey = await ePDATAContext.GetUdfValueAsync(hubKeyUdf);
                if (!HttpContext.Request.Headers.TryGetValue(hubKeyUdf, out var key) || !key.All(v => v == hubKey))
                {
                    logger.LogWarning("Unauthorized in GetMainHubConnections");
                    return Unauthorized();
                }
                return Ok(Hubs.MainHub.Connections.Values?.Select(v => v));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetMainHubConnections");
                throw;
            }
        }

        [HttpPost("InvokeMethod")]
        public async Task<IActionResult> InvokeMethod(string connectionId, string method, [FromServices] IHubContext<Hubs.MainHub> mainHubContext)
        {
            try
            {
                var hubKey = await ePDATAContext.GetUdfValueAsync(hubKeyUdf);
                if (!HttpContext.Request.Headers.TryGetValue(hubKeyUdf, out var key) || !key.All(v => v == hubKey))
                {
                    logger.LogWarning("Unauthorized in InvokeMethod");
                    return Unauthorized();
                }
                await mainHubContext.Clients.Client(connectionId).SendAsync(method);
                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in InvokeMethod");
                throw;
            }
        }
    }
}