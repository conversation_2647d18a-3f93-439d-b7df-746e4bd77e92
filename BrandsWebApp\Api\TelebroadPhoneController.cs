﻿using BrandsWebApp.Services;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Flurl.Http;
using Brands.DAL;
using System.Linq;
using System.Text.Json.Serialization;

namespace BrandsWebApp.Api
{
    [AllowAnonymous]
    [ApiController]
    [Route("api/telebroad_phone_ask_weburl")]
    public class TelebroadPhoneController : ControllerBase
    {
        private readonly ISqlConnectionService sqlConnectionService;
        private readonly ILogger<TelebroadPhoneController> logger;
        private readonly EPDATAContext ePDATAContext;

        public TelebroadPhoneController(ISqlConnectionService sqlConnectionService, ILogger<TelebroadPhoneController> logger, EPDATAContext ePDATAContext)
        {
            this.sqlConnectionService = sqlConnectionService;
            this.logger = logger;
            this.ePDATAContext = ePDATAContext;
        }

        [HttpPost]
        public async Task<ActionResult> GetAsync([FromForm] Dictionary<string, string> formData, [FromQuery] string key)
        {
            logger.LogDebug("Entering Telebroad Ask web url. {@formData}", formData);
            try
            {
                if (key != "aK8WcmvpD2Nz")
                {
                    logger.LogWarning("Invalid key. key: {key}", key);
                    return Unauthorized();
                }

                //call url
                var cred = await ePDATAContext.GetUdfValueSplittedAsync("TelebroadCred");
                var url = $"https://webserv.telebroad.com/api/teleconsole/rest/active/calls/panel";
                var results = await url.WithBasicAuth(cred[0], cred[1]).GetJsonAsync<ActiveCallResponse>();
                logger.LogDebug("Telebroad /active/calls/panel results: {@results}", results);
                if (results.Error != null && results.Error.Code.IsNotNullOrWhiteSpace())
                {
                    logger.LogError("Error calling Telebroad /active/calls/panel {Error}", results.Error);
                    return BadRequest();
                }

                //filter result. 
                var activeCall = results.Result.FirstOrDefault(c => c.Callid == formData["callid"]);
                if(activeCall == null)
                {
                    return NoContent();
                }
                var CalleridInternal = activeCall.CalleridInternal.IsNotNullOrWhiteSpace() ? activeCall.CalleridInternal : activeCall.Snumber;
                logger.LogDebug("Active call (filtered for callid: {callId}). CalleridInternal: {CalleridInternal} {@activeCall}", formData["callid"], CalleridInternal, activeCall);

                await using var con = sqlConnectionService.GetSqlConnection();
                var parameters = new
                {
                    @jsonData = Newtonsoft.Json.JsonConvert.SerializeObject(formData),
                    CalleridInternal,
                    activeCallJson = Newtonsoft.Json.JsonConvert.SerializeObject(activeCall),
                };
                logger.LogDebug("Calling SP custom.prc_TelebroadPhoneAskWebUrl with parameters: {@parameters}", parameters);
                var resultText = await con.QuerySingleAsync<string>(@"custom.prc_TelebroadPhoneAskWebUrl", parameters, commandType: System.Data.CommandType.StoredProcedure);
                logger.LogDebug("SP custom.prc_TelebroadPhoneAskWebUrl resultText: {resultText}", resultText);
                return Content(resultText, "text/plain");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in telebroad_phone_ask_weburl");
                throw;
            }
        }

        public class ActiveCallResponse
        {
            [JsonPropertyName("error")]
            public Error Error { get; set; }

            [JsonPropertyName("result")]
            public List<Result> Result { get; set; }
        }


        public class Result
        {
            [JsonPropertyName("server")]
            public object Server { get; set; }

            [JsonPropertyName("uniqueid")]
            public string Uniqueid { get; set; }

            [JsonPropertyName("callid")]
            public string Callid { get; set; }

            [JsonPropertyName("start")]
            public string Start { get; set; }

            [JsonPropertyName("answered")]
            public string Answered { get; set; }

            [JsonPropertyName("stype")]
            public string Stype { get; set; }

            [JsonPropertyName("snumber")]
            public string Snumber { get; set; }

            [JsonPropertyName("spresent")]
            public string Spresent { get; set; }

            [JsonPropertyName("stransferred")]
            public string Stransferred { get; set; }

            [JsonPropertyName("ctype")]
            public string Ctype { get; set; }

            [JsonPropertyName("cnumber")]
            public string Cnumber { get; set; }

            [JsonPropertyName("dtype")]
            public string Dtype { get; set; }

            [JsonPropertyName("dnumber")]
            public string Dnumber { get; set; }

            [JsonPropertyName("dtransferred")]
            public string Dtransferred { get; set; }

            [JsonPropertyName("ptransferred")]
            public string Ptransferred { get; set; }

            [JsonPropertyName("callerid_internal")]
            public string CalleridInternal { get; set; }

            [JsonPropertyName("callerid_external")]
            public string CalleridExternal { get; set; }

            [JsonPropertyName("callername_internal")]
            public string CallernameInternal { get; set; }

            [JsonPropertyName("callername_external")]
            public string CallernameExternal { get; set; }

            [JsonPropertyName("park")]
            public string Park { get; set; }

            [JsonPropertyName("status")]
            public string Status { get; set; }

            [JsonPropertyName("duration")]
            public string Duration { get; set; }

            [JsonPropertyName("caller")]
            public string Caller { get; set; }

            [JsonPropertyName("called")]
            public string Called { get; set; }

            [JsonPropertyName("callednumber")]
            public string Callednumber { get; set; }
        }

        public class Error
        {
            [JsonPropertyName("code")]
            public string Code { get; set; }

            [JsonPropertyName("message")]
            public string Message { get; set; }
        }
    }
}
