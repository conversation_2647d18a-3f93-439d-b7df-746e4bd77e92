﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models.PaymentAddress;
using BrandsWebApp.Models.PaymentAddresses;
using BrandsWebApp.Services.PaymentAddresses;
using BrandsWebApp.Services.Validation.PaymentAddressValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Employee
{
    [ApiController]
    [ApiExplorerSettings(GroupName = "v2")]
    [Authorize(Policy = nameof(Permission.ManageEmployees))]
    [Route("api/Employees/PaymentAddresses")]
    public class PaymentAddressesController : ControllerBase
    {
        private readonly PaymentAddressService paymentAddressService;

        public PaymentAddressesController(PaymentAddressService paymentAddressService)
        {
            this.paymentAddressService = paymentAddressService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<PaymentAddress>>> GetPaymentAddresses([FromQuery] bool includePublic)
        {
            var paymentAddresses = await paymentAddressService.GetPaymentAddressesAsync(User.GetConum(), includePublic);
            return Ok(paymentAddresses);
        }

        [HttpPost]
        public async Task<IActionResult> AddPaymentAddress([FromBody] UpsertPaymentAddress paymentAddress, [FromServices] UpsertPaymentAddressValidation validations)
        {
            var validationResult = await validations.ValidateAsync(paymentAddress);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            await paymentAddressService.AddPaymentAddressAsync(User.GetConum(), paymentAddress);
            return NoContent();
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdatePaymentAddress([FromRoute] Guid id, [FromBody] UpsertPaymentAddress paymentAddress, [FromServices] UpsertPaymentAddressValidation validations)
        {
            var validationResult = await validations.ValidateAsync(paymentAddress);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            await paymentAddressService.UpdatePaymentAddressAsync(User.GetConum(), id, paymentAddress);
            return NoContent();
        }
    }
}
