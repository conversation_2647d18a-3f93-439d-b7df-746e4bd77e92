﻿using Brands.DataModels;
using BrandsWebApp.Authentication;
using BrandsWebApp.DataAccess.OnboardingData;
using BrandsWebApp.Models.Account;
using BrandsWebApp.Models.Onboarding;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Validation.OnboardingValidation;
using FluentValidation.Results;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.ClientOnboarding
{
    [ApiController]
    [Route("api/onboarding")]
    [Authorize(Policy = nameof(Permission.Onboarding))]
    public partial class OnboardingController : ControllerBase
    {
        private readonly ILogger<OnboardingController> logger;
        private readonly OnboardingDataService onboardingDataService;
        private readonly BrandsLoginService brandsLoginService;
        private readonly UserManager<BrandsAuthUser> userManager;
        private readonly CalendlyService calendlyService;

        public OnboardingController(
            ILogger<OnboardingController> logger,
            OnboardingDataService onboardingDataService,
            BrandsLoginService brandsLoginService,
            UserManager<BrandsAuthUser> userManager,
            CalendlyService calendlyService)
        {
            this.logger = logger;
            this.onboardingDataService = onboardingDataService;
            this.brandsLoginService = brandsLoginService;
            this.userManager = userManager;
            this.calendlyService = calendlyService;
        }

        [HttpPost("new")]
        [AllowAnonymous]
        public async Task<IActionResult> NewCompanyOnboarding(BasicOnboardingDetails basicDetails, [FromServices] BasicOnboardingDetailsValidation validations)
        {
            ValidationResult validationResult = await validations.ValidateAsync(basicDetails);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult);
            }

            var (onboardingGuid, conum, isAuthenticated) = await onboardingDataService.SaveNewOnboardingEntry(basicDetails);
            if (isAuthenticated)
            {
                UserAuthorizationResults userAuthResults = await GetModifiedAuthorizationResults(conum.Value);

                return Ok(new
                {
                    conum,
                    userAuthResults,
                    isAuthenticated = true
                });
            }
            else
            {
                return Ok(new
                {
                    isAuthenticated,
                    onboardingGuid
                });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetCurrentStage()
        {
            Stage currentStage = await onboardingDataService.GetCurrentStage();
            var stageProgress = await onboardingDataService.GetStageProgress();
            return Ok(new
            {
                Stage = currentStage.ToString(),
                Progress = stageProgress
            });
        }

        [HttpGet("summary")]
        public async Task<IActionResult> GetOnboardingSummary()
        {
            var onboardingEntry = await onboardingDataService.GetOnboardingData();
            OnboardingInfo onboardingInfo = await onboardingDataService.GetOnboardingInfoAsync(onboardingEntry);
            var calendlyEventDetails = await calendlyService.GetCalendlyEventDetailsAsync(onboardingEntry);
            var stageData = await onboardingDataService.GetStageProgress();
            return Ok(new
            {
                OnboardingInfo = onboardingInfo,
                CalendlyEventDetails = calendlyEventDetails,
                StageProgress = stageData
            });
        }

        [HttpGet("applicationSummary")]
        public async Task<IActionResult> GetApplicationSummary()
        {
            var onboardingEntry = await onboardingDataService.GetOnboardingData();
            var summary = await onboardingDataService.GetApplicationSummaryAsync(onboardingEntry);
            return Ok(summary);
        }

        [HttpGet("phoneNumber")]
        public async Task<IActionResult> GetOnboardingPhoneNumber()
        {
            var phoneNumber = await onboardingDataService.GetOnboardingPhoneNumberAsync();
            return Ok(new
            {
                phoneNumber
            });
        }

        [HttpGet("dropdown/{dropdowntype}")]
        [AllowAnonymous]
        public async Task<IActionResult> GetDropdownValues([FromRoute] string dropdownType)
        {
            var dropdownValues = await onboardingDataService.GetDropdownData(dropdownType);
            return Ok(dropdownValues);
        }

        [HttpGet("{stage}")]
        public async Task<IActionResult> GetStageData([FromRoute] Stage stage)
        {
            var stageData = await onboardingDataService.GetStageData(stage);
            var stageProgress = await onboardingDataService.GetStageProgress();

            return Ok(new
            {
                stageData,
                stageProgress
            });
        }

        [HttpGet("progress")]
        public async Task<IActionResult> GetStageProgress()
        {
            var stageData = await onboardingDataService.GetStageProgress();
            return Ok(stageData);
        }

        [HttpPost("legal")]
        public async Task<IActionResult> SaveLegalInfo([FromForm] LegalInfo legalInfo, [FromServices] LegalInfoValidation validations)
        {
            ValidationResult validationResult = await validations.ValidateAsync(legalInfo);
            if (!validationResult.IsValid)
            {
                await onboardingDataService.SaveOnboardingValidationFailedEvent(OnboardingEventType.LegalInfo, validationResult);
                return BadRequest(validationResult);
            }

            var status = await onboardingDataService.SaveLegalInfoAsync(legalInfo);
            if (legalInfo.IrsDoc != null)
            {
                var (success, message) = await onboardingDataService.SaveIRSDoc(legalInfo.IrsDoc);
                if (!success)
                {
                    return BadRequest(message);
                }
            }

            if (status == StageStatus.Completed)
            {
                return await PrepareResponse(status);
            }
            else
            {
                return await PrepareResponse(StageStatus.Disabled);
            }
        }

        [HttpPost("contactInfo")]
        public async Task<IActionResult> SaveContactInfo(ContactInfo contactInfo, [FromServices] ContactInfoValidation validations)
        {
            ValidationResult validationResult = await validations.ValidateAsync(contactInfo);
            if (!validationResult.IsValid)
            {
                await onboardingDataService.SaveOnboardingValidationFailedEvent(OnboardingEventType.ContactInfo, validationResult);
                return BadRequest(validationResult);
            }

            var status = await onboardingDataService.SaveContactInfoAsync(contactInfo);
            return await PrepareResponse(status);
        }

        [HttpPost("companyAddress")]
        public async Task<IActionResult> SaveCompanyAddress(CompanyAddress address, [FromServices] CompanyAddressValidation validations)
        {
            ValidationResult validationResult = await validations.ValidateAsync(address);
            if (!validationResult.IsValid)
            {
                await onboardingDataService.SaveOnboardingValidationFailedEvent(OnboardingEventType.CompanyAddress, validationResult);
                return BadRequest(validationResult);
            }

            var status = await onboardingDataService.SaveCompanyAddressAsync(address);
            return await PrepareResponse(status);
        }

        [HttpPost("shippingAddress")]
        public async Task<IActionResult> SaveShippingAddress(ShippingAddress address)
        {
            var status = await onboardingDataService.SaveShippingAddressAsync(address);
            return await PrepareResponse(status);
        }

        [HttpPost("employeeStates")]
        public async Task<IActionResult> SaveEmployeeStates(EmployeeStates employeeStates)
        {
            var status = await onboardingDataService.SaveEmployeeStatesAsync(employeeStates);
            return await PrepareResponse(status);
        }

        [HttpGet("states")]
        public async Task<IActionResult> GetStatesList()
        {
            IEnumerable<(string, string)> result = await onboardingDataService.GetStatesList();
            return Ok(result.Select(x => new { code = x.Item1, state = x.Item2 }));
        }

        [HttpGet("download/{doctype}")]
        public async Task<IActionResult> GetFileAsBlob([FromRoute] OnboardingDataService.DocType docType)
        {
            var document = await onboardingDataService.GetFile(docType);
            if (document.Data != null)
            {
                return File(document.Data, "application/octet-stream", document.Name);
            }

            return NotFound();
        }

        [HttpPost("bankingInfo")]
        public async Task<IActionResult> SaveBankingInfo(BankingInfo bankingInfo, [FromServices] BankingInfoValidation validations)
        {
            var validationResult = await validations.ValidateAsync(bankingInfo);
            if (!validationResult.IsValid)
            {
                await onboardingDataService.SaveOnboardingValidationFailedEvent(OnboardingEventType.BankingInfo, validationResult);
                return BadRequest(validationResult);
            }

            var status = await onboardingDataService.SaveBankingInfoAsync(bankingInfo);
            return await PrepareResponse(status);
        }

        [HttpPost("bankingInfo/voidCheck")]
        public async Task<IActionResult> SaveVoidCheck(IFormFile voidCheck)
        {
            var (status, message) = await onboardingDataService.SaveVoidCheck(voidCheck).ConfigureAwait(false);
            if (status)
            {
                return Ok();
            }

            return BadRequest(new { message });
        }

        [HttpPost("payroll")]
        public async Task<IActionResult> SavePayroll(Models.Onboarding.Payroll payroll, [FromServices] PayrollValidation validations)
        {
            var validationResult = await validations.ValidateAsync(payroll);
            if (!validationResult.IsValid)
            {
                await onboardingDataService.SaveOnboardingValidationFailedEvent(OnboardingEventType.Payroll, validationResult);
                return BadRequest(validationResult);
            }

            var status = await onboardingDataService.SavePayrollAsync(payroll);
            return await PrepareResponse(status);
        }

        [HttpPost("termsAndConditions")]
        public async Task<IActionResult> SaveTermsAndConditions(TermsAndConditions termsAndConditions, [FromServices] TermsAndConditionsValidation validations)
        {
            var validationResult = await validations.ValidateAsync(termsAndConditions);
            if (!validationResult.IsValid)
            {
                await onboardingDataService.SaveOnboardingValidationFailedEvent(OnboardingEventType.TandC, validationResult);
                return BadRequest(validationResult);
            }

            var status = await onboardingDataService.SaveTermsAndConditions(termsAndConditions).ConfigureAwait(false);
            return await PrepareResponse(status);
        }

        [HttpPost("submitApplication")]
        public async Task<IActionResult> SubmitApplication()
        {
            var message = await onboardingDataService.SubmitApplication().ConfigureAwait(false);
            if (message.IsNotNullOrWhiteSpace())
            {
                return BadRequest(new
                {
                    Message = message
                });
            }

            return await PrepareResponse(StageStatus.Completed);
        }

        [HttpPost("payrollInfo")]
        public async Task<IActionResult> SavePayrollInfo(PayrollInfo payrollInfo, [FromServices] PayrollInfoValidation validations)
        {
            var validationResult = await validations.ValidateAsync(payrollInfo);
            if (!validationResult.IsValid)
            {
                await onboardingDataService.SaveOnboardingValidationFailedEvent(OnboardingEventType.PayrollInfo, validationResult);
                return BadRequest(validationResult);
            }

            var status = await onboardingDataService.SavePayrollInfo(payrollInfo).ConfigureAwait(false);
            return await PrepareResponse(status);
        }

        [HttpPost("priorPayroll")]
        public async Task<IActionResult> SavePriorPayroll([FromBody] AddPriorPayroll priorPayroll)
        {
            var status = await onboardingDataService.SavePriorPayroll(priorPayroll);
            return await PrepareResponse(status);
        }

        [HttpGet("priorPayroll/documents/info")]
        public async Task<IActionResult> GetPriorPayrollDocumentsInfo()
        {
            var info = await onboardingDataService.GetPriorPayrollDocumentsInfo();
            return Ok(info);
        }

        [HttpGet("priorPayroll/documents/{docId}")]
        public async Task<IActionResult> GetPriorPayrollDocument([FromRoute] int docId)
        {
            var document = await onboardingDataService.GetPriorPayrollDocument(docId);
            if (document.Data != null)
            {
                return File(document.Data, "application/octet-stream", document.Name);
            }

            return NotFound();
        }

        [HttpPost("priorPayroll/documents")]
        public async Task<IActionResult> SavePriorPayrollDocument(
            [FromForm] AddPriorPayrollDocument priorPayrollDocument,
            [FromServices] AddPriorPayrollDocumentValidation validations)
        {
            var validationResult = await validations.ValidateAsync(priorPayrollDocument);
            if (!validationResult.IsValid)
            {
                await onboardingDataService.SaveOnboardingValidationFailedEvent(OnboardingEventType.AddPriorPayrollDocument, validationResult);
                return BadRequest(validationResult);
            }

            await onboardingDataService.SavePriorPayrollDocument(priorPayrollDocument);
            return NoContent();
        }

        [HttpDelete("priorPayroll/documents/{docId}")]
        public async Task<IActionResult> DeletePriorPayrollDocument([FromRoute] int docId)
        {
            await onboardingDataService.DeletePriorPayrollDocument(docId);
            return NoContent();
        }

        [HttpPost("newYorkPrompTax")]
        public async Task<IActionResult> SaveNewYorkPrompTax(NewYorkPrompTax newYorkPrompTax)
        {
            var status = await onboardingDataService.SaveNewYorkPrompTax(newYorkPrompTax);
            return await PrepareResponse(status);
        }

        [HttpPost("unemploymentId")]
        public async Task<IActionResult> SaveUnemploymentId(Unemployment unemployment)
        {
            var status = await onboardingDataService.SaveUnemploymentId(unemployment);
            return await PrepareResponse(status);
        }

        [HttpPost("additionalServices")]
        public async Task<IActionResult> SaveAdditionalServices()
        {
            var status = await onboardingDataService.SaveAdditionalService();
            return await PrepareResponse(status);
        }

        [HttpPost("employeeImport")]
        public async Task<IActionResult> SaveEmployeeImport(IFormFile employeeImport)
        {
            var onboardingData = await onboardingDataService.GetOnboardingData();
            var progressEntry = await onboardingDataService.GetOnboardingProgress(onboardingData, Stage.ReviewedAndSignedConfirmed);
            if (progressEntry == null || progressEntry.Status != StageStatus.Completed.ToString())
            {
                return BadRequest("Agreement must be signed and confirmed first");
            }

            var (status, message) = await onboardingDataService.SaveEmployeeImport(employeeImport).ConfigureAwait(false);
            if (status)
            {
                return NoContent();
            }

            return BadRequest(new { message });
        }

        [HttpPost("deleteFile/{doctype}")]
        public async Task<IActionResult> DeleteFile([FromRoute] OnboardingDataService.DocType docType)
        {
            string eventType = null;
            if (docType == OnboardingDataService.DocType.IRS)
            {
                eventType = OnboardingEventType.DeleteIrsDoc;
            }    
            else if (docType == OnboardingDataService.DocType.VoidCheck)
            {
                eventType = OnboardingEventType.DeleteVoidCheck;
            }

            await onboardingDataService.DeleteFile(docType, eventType);
            return NoContent();
        }

        private async Task<IActionResult> PrepareResponse(StageStatus status)
        {
            var stageProgress = new
            {
                progress = await onboardingDataService.GetStageProgress(),
                message = status == StageStatus.Disabled ? "Stage is in disabled state" : "Success"
            };
            return status == StageStatus.Disabled ? Conflict(stageProgress) : Ok(stageProgress);
        }

        private async Task<UserAuthorizationResults> GetModifiedAuthorizationResults(decimal newConum)
        {
            logger.LogDebug("Entering GetModifiedToken.");

            BrandsAuthUser user = await userManager.FindByIdAsync(User.GetUserId());
            var userAuthResults = await brandsLoginService.GetUserAuthorizationResultsAsync(user, new Profile(ProfileType.Company, newConum));

            return userAuthResults;
        }
    }
}
