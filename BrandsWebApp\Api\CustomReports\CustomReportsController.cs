﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models.CustomReports;
using BrandsWebApp.Services.CustomReports;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.CustomReports
{
    [Route("api/CustomReports")]
    [Authorize(Policy = nameof(Permission.CustomReports))]
    [ApiController]
    [ApiExplorerSettings(GroupName = "v2")]
    public class CustomReportsController : ControllerBase
    {
        private readonly CustomReportsService customReportsService;
        private readonly PayCodesService payCodesService;

        public CustomReportsController(CustomReportsService customReportsService, PayCodesService payCodesService)
        {
            this.customReportsService = customReportsService;
            this.payCodesService = payCodesService;
        }

        /// <summary>
        /// Gets report layouts
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<CustomReportsListItem>>> GetCustomReports()
        {
            var result = await customReportsService.GetReportLayoutsAsync();
            return Ok(result);
        }

        /// <summary>
        /// Gets report layout by id
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<CustomReportDetails>> GetCustomReport([FromRoute] int id)
        {
            var result = await customReportsService.GetReportLayoutDetailsAsync(id);
            return Ok(result);
        }

        /// <summary>
        /// Adds report layout
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> AddCustomReport([FromBody] AddCustomReport reportLayout)
        {
            await customReportsService.AddReportLayoutAsync(reportLayout);
            return NoContent();
        }

        /// <summary>
        /// Runs report layout by id
        /// </summary>
        [HttpPost("{id}")]
        public async Task<ActionResult<IEnumerable<object>>> RunCustomReport([FromRoute] int id, [FromBody] RunCustomReport reportLayout)
        {
            var result = await customReportsService.RunReportLayoutAsync(id, reportLayout);
            return Ok(result);
        }

        /// <summary>
        /// Updates report layout by id
        /// </summary>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateCustomReport([FromRoute] int id, [FromBody] UpdateCustomReport reportLayout)
        {
            await customReportsService.UpdateReportLayoutAsync(id, reportLayout);
            return NoContent();
        }

        /// <summary>
        /// Deletes report layout by id
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteCustomReport([FromRoute] int id)
        {
            await customReportsService.DeleteReportLayoutAsync(id);
            return NoContent();
        }

        /// <summary>
        /// Share report layout
        /// </summary>
        [HttpPost("Shared/{id}")]
        public async Task<IActionResult> ShareCustomReport([FromRoute] int id, [FromBody] ShareCustomReport shareReportLayout)
        {
            await customReportsService.ShareReportLayoutAsync(id, shareReportLayout);
            return NoContent();
        }

        /// <summary>
        /// Gets pay codes
        /// </summary>
        [HttpGet("PayCodes")]
        public async Task<ActionResult<IEnumerable<PayCode>>> GetPayCodes()
        {
            var result = await payCodesService.GetPayCodesAsync(User.GetConum());
            return Ok(result);
        }
    }
}
