﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Filters;
using BrandsWebApp.Models.Account;
using BrandsWebApp.Models.Auth;
using BrandsWebApp.Models.EmployeeOnboarding;
using BrandsWebApp.Models.Ess.Options;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Auth;
using BrandsWebApp.Services.EmployeeDeck;
using BrandsWebApp.Services.Pdf;
using BrandsWebApp.Services.Pdf.Templates;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.EmployeeOnboarding
{
    [ApiController]
    [Route("api/EmployeeOnboarding")]
    [Authorize(Policy = AuthPolicies.IsEssOnboarding)]
    public class EmployeeOnboardingController : ControllerBase
    {
        private readonly EssOnboardingDocumentService essOnboardingDocumentService;
        private readonly EssOnboardingService essOnboardingService;
        private readonly PdfFillerService pdfFillerService;
        private readonly EssOptions essOptions;
        private readonly StatesService statesService;
        private readonly JwtFactory jwtFactory;
        private readonly BrandsLoginService brandsLoginService;
        private readonly UserEventService userEventService;

        public EmployeeOnboardingController(EssOnboardingDocumentService essOnboardingDocumentService, EssOnboardingService essOnboardingService, PdfFillerService pdfFillerService, EssOptions essOptions, StatesService statesService, JwtFactory jwtFactory, BrandsLoginService brandsLoginService, UserEventService userEventService)
        {
            this.essOnboardingDocumentService = essOnboardingDocumentService;
            this.essOnboardingService = essOnboardingService;
            this.pdfFillerService = pdfFillerService;
            this.essOptions = essOptions;
            this.statesService = statesService;
            this.jwtFactory = jwtFactory;
            this.brandsLoginService = brandsLoginService;
            this.userEventService = userEventService;
        }

        /// <summary>
        /// Adds new user account for employee
        /// </summary>
        [AllowAnonymous]
        [HttpPost("Accounts")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<UserAuthorizationResults>> AddNewEmployeeAccount([FromBody] AddEmployeeAccount account)
        {
            var user = await essOnboardingService.AddEmployeeAccountAsync(account);
            UserAuthorizationResults userAuthResults;
            userAuthResults = await brandsLoginService.GetUserAuthorizationResultsAsync(user, null);
            await userEventService.SaveUserEvent(user, "LOGIN_AFTER_ADDING_ESS_ACCOUNT", true, userAuthResults.Profile, jwtGuid: userAuthResults.JwtGuid);
            await brandsLoginService.AddLoginRequestRecordAsync(userAuthResults.UserName, "Login successful", true, user.Id);
            return Ok(userAuthResults);
        }

        /// <summary>
        /// Gets onboarding invite info
        /// </summary>
        [AllowAnonymous]
        [HttpGet("Invites/{onboardingCode}")]
        public async Task<IActionResult> GetInviteInfo([FromRoute] string onboardingCode)
        {
            var result = await essOnboardingService.GetInviteInfoAsync(onboardingCode);
            return Ok(result);
        }

        /// <summary>
        /// Opens onboarding invite
        /// </summary>
        [AllowAnonymous]
        [HttpPut("Invites/{onboardingCode}")]
        public async Task<IActionResult> UpdateInviteOpened([FromRoute] string onboardingCode)
        {
            await essOnboardingService.UpdateInviteOpenedAsync(onboardingCode);
            return NoContent();
        }

        /// <summary>
        /// Submits onboarding for review
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> SubmitOnboardingForReview()
        {
            await essOnboardingService.SubmitOnboardingForReviewAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value);
            return NoContent();
        }

        /// <summary>
        /// Gets employee identification info
        /// </summary>
        [HttpGet("IdentificationInfo")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<ActionResult<EmployeeIdentificationInfo>> GetEmployeeIdentificationInfo()
        {
            var identificationInfo = await essOnboardingDocumentService.GetEmployeeIdentificationInfoAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value);
            return Ok(identificationInfo);
        }

        /// <summary>
        /// Adds employee identification info
        /// </summary>
        [ServiceFilter(typeof(UpdateEmployeeOnboardingAsyncFilter))]
        [HttpPost("IdentificationInfo")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<IActionResult> AddEmployeeIdentificationInfo([FromBody] AddEmployeeIdentificationInfo employeeIdentificationInfo)
        {
            await essOnboardingDocumentService.AddEmpoyeeIdentificationInfoAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value, employeeIdentificationInfo);
            return NoContent();
        }

        /// <summary>
        /// Updates employee identification documents
        /// </summary>
        [HttpPut("IdentificationInfo/Documents")]
        [ApiExplorerSettings(GroupName = "v2")]
        public async Task<IActionResult> UpdateEmployeeIdentificationDocuments([FromForm] UpdateEmployeeIdDocuments documents)
        {
            var result = await essOnboardingDocumentService.UpdateEmpoyeeIdentificationDocsAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value, documents?.EmployeeIdentificationDocuments);
            return Ok(result);
        }

        /// <summary>
        /// Gets identification form url
        /// </summary>
        [HttpGet("IdentificationFormUrl")]
        public ActionResult ReviewEmployeeW4InfoAsync([FromQuery] bool signed)
        {
            var path = "IdentificationForm";
            var downloadUrl = EmployeeOnboardingAuthUrl(signed ? $"{path}/Signed" : path);
            return Ok(new
            {
                downloadUrl
            });
        }

        /// <summary>
        /// Gets identification form
        /// </summary>
        [AllowAnonymous]
        [HttpGet("IdentificationForm")]
        public async Task<IActionResult> GetIdentificationForm([FromQuery] string token)
        {
            if (token.IsNullOrWhiteSpace())
            {
                return BadRequest("Token query param is required");
            }

            var docPath = "IdentificationForm";
            var baseUrl = EmployeeOnboardingUrl(docPath);
            var jwt = jwtFactory.ValidateResourceExpiringToken(baseUrl, token);
            var coNumClaim = jwt.Claims.FirstOrDefault(c => c.Type == "EssCoNum");
            var empNumClaim = jwt.Claims.FirstOrDefault(c => c.Type == "EssEmpNum");
            decimal coNum = 0;
            decimal empNum = 0;
            if (coNumClaim == null || empNumClaim == null || !decimal.TryParse(coNumClaim.Value, out coNum) || !decimal.TryParse(empNumClaim.Value, out empNum))
            {
                return Unauthorized();
            }

            var fileDate = DateTime.Now;
            var fileName = $"i-9_{fileDate:yyyy-MM-dd}.pdf";
            var fileContentType = "application/pdf";
            this.Response.Headers.Add("X-Filename", fileName);
            this.Response.Headers.Add("Content-Disposition", $"inline; filename=\"{fileName}\"");

            var stream = await CreateIdentificationFormStream(coNum, empNum);
            return File(stream, fileContentType);
        }

        /// <summary>
        /// Gets signed identification form
        /// </summary>
        [AllowAnonymous]
        [HttpGet("IdentificationForm/Signed")]
        public async Task<IActionResult> GetSignedIdentificationForm([FromQuery] string token)
        {
            if (token.IsNullOrWhiteSpace())
            {
                return BadRequest("Token query param is required");
            }

            var docPath = "IdentificationForm/Signed";
            var baseUrl = EmployeeOnboardingUrl(docPath);
            var jwt = jwtFactory.ValidateResourceExpiringToken(baseUrl, token);
            var coNumClaim = jwt.Claims.FirstOrDefault(c => c.Type == "EssCoNum");
            var empNumClaim = jwt.Claims.FirstOrDefault(c => c.Type == "EssEmpNum");
            decimal coNum = 0;
            decimal empNum = 0;
            if (coNumClaim == null || empNumClaim == null || !decimal.TryParse(coNumClaim.Value, out coNum) || !decimal.TryParse(empNumClaim.Value, out empNum))
            {
                return Unauthorized();
            }

            var document = await essOnboardingDocumentService.GetSignedIdentificationFormAsync(coNum, empNum);
            if (document?.Data != null)
            {
                return File(document.Data, "application/pdf", document.Name);
            }

            return NotFound();
        }

        /// <summary>
        /// Signs identification form
        /// </summary>
        [ServiceFilter(typeof(UpdateEmployeeOnboardingAsyncFilter))]
        [HttpPost("IdentificationForm")]
        public async Task<IActionResult> SignIdentificationForm([FromForm] SignEmployeeIdentificationForm signForm)
        {
            if (signForm == null || signForm.Signature == null)
            {
                return BadRequest("Signature is required");
            }

            var conum = User.GetEssEmpConum().Value;
            var empnum = User.GetEssEmpNum().Value;
            using var stream = await CreateIdentificationFormStream(conum, empnum, signForm.Signature);
            var docID = await essOnboardingDocumentService.SaveSignedI9FormAsync(stream.ToArray(), User.GetEssEmpConum().Value, User.GetEssEmpNum().Value, User.GetFullName());
            return Ok(new
            {
                DownloadURL = EmployeeOnboardingAuthUrl("IdentificationForm/Signed")
            });
        }

        /// <summary>
        /// Gets employee onboarding status
        /// </summary>
        [HttpGet("Status")]
        public async Task<IActionResult> GetOnboardingStatus()
        {
            var status = await essOnboardingService.GetOnboardingStatusAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value);
            return Ok(status);
        }

        /// <summary>
        /// Gets employee onboarding forms
        /// </summary>
        [HttpGet("Forms")]
        public async Task<IActionResult> GetEmployeeForms()
        {
            var forms = await essOnboardingService.GetEmployeeFormsAsync(User.GetEssEmpConum().Value, User.GetEssEmpNum().Value);
            return Ok(forms);
        }

        /// <summary>
        /// Gets states
        /// </summary>
        [HttpGet("States")]
        public async Task<IActionResult> GetEmployeeStates()
        {
            var states = await statesService.GetAllStatesAsync();
            return Ok(states);
        }

        private async Task<MemoryStream> CreateIdentificationFormStream(decimal conum, decimal empnum, IFormFile signature = null)
        {
            var identificationInfo = await essOnboardingDocumentService.GetEmpoyeeIdentificationFormInfoAsync(conum, empnum);
            var stream = new MemoryStream();
            var options = I9Employee.CreateFillOptions(identificationInfo, signature);
            string path = Path.Combine(essOptions.PdfFormsBasePath, options.Template.FileName);
            pdfFillerService.Fill(path, options, stream);
            return stream;
        }

        private string EmployeeOnboardingAuthUrl(string path)
        {
            var baseUrl = EmployeeOnboardingUrl(path);
            var empCoNumClaim = new Claim("EssCoNum", User.GetEssEmpConum().Value.ToString());
            var empNumClaim = new Claim("EssEmpNum", User.GetEssEmpNum().Value.ToString());
            var isEssOnboarding = new Claim("IsEssOnboarding", User.IsEssOnboarding().ToString());
            var token = jwtFactory.IssueResourceExpiringToken(baseUrl, TimeSpan.FromMinutes(15), empCoNumClaim, empNumClaim, isEssOnboarding);
            return $"/{baseUrl}?token={token}";
        }

        private string EmployeeOnboardingUrl(string path)
        {
            var url = $"api/EmployeeOnboarding/{path}";
            return url;
        }
    }
}
