﻿using Brands.DAL;
using BrandsWebApp.Authentication;
using BrandsWebApp.Extensions;
using BrandsWebApp.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [ApiController]
    [Route("api/CrossCompanyUsers")]
    [Authorize(Policy = nameof(Permission.CrossCompanyUsers))]
    public class CrossCompanyUsersController : ControllerBase
    {
        private readonly EPDATAContext ePDATAContext;
        private readonly ILogger<CrossCompanyUsersController> logger;
        private readonly PermissionDataService permissionDataService;

        public CrossCompanyUsersController(EPDATAContext ePDATAContext, ILogger<CrossCompanyUsersController> logger, PermissionDataService permissionDataService)
        {
            this.ePDATAContext = ePDATAContext;
            this.logger = logger;
            this.permissionDataService = permissionDataService;
        }

        [HttpGet("Users")]
        public async Task<IActionResult> GetUsers()
        {
            logger.LogDebug("Entering GetRoles");
            try
            {
                List<CompanyThenUsers> companies = await GetAllowedCompanies();

                foreach (var company in companies)
                {
                    await SetCompanyUsers(company);
                }

                List<UserThenCompanies> usersThenCompanies = GetUsers(companies);

                return Ok(new
                {
                    companies = companies.ToDictionary(c => c.Conum, d => d),
                    users = usersThenCompanies.ToDictionary(u => u.UserId),
                    permissions = await GetPermissionsAsync()
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetRoles");
                throw;
            }
        }

        private static List<UserThenCompanies> GetUsers(List<CompanyThenUsers> companies)
        {
            var allUsers = companies.SelectMany(u => u.Users).DistinctBy(u => u.UserId);
            var usersThenCompanies = new List<UserThenCompanies>();
            foreach (var item in allUsers)
            {
                var comps = companies.Where(c => c.Users.Select(u => u.UserId).Contains(item.UserId)).ToList();
                var co = new UserThenCompanies()
                {
                    Name = item.UserName,
                    FullName = item.FullName,
                    UserId = item.UserId.ToString(),
                    UserName = item.UserName,
                    UserType = item.UserType,
                    Companies = comps.Select(c => new UserThenCompanies.Company
                    {
                        Conum = c.Conum,
                        Permissions = c.Users.Single(u => u.UserId == item.UserId).UserPermissions
                    }).ToList()
                };
                usersThenCompanies.Add(co);
            }

            return usersThenCompanies;
        }

        private async Task SetCompanyUsers(CompanyThenUsers company)
        {
            var users = await (from ue in ePDATAContext.BrandsUserEmployees
                               join u in ePDATAContext.Users on ue.UserId equals u.Id
                               where ue.Conum == company.Conum && ue.IsActive
                               select new
                               {
                                   user = u,
                                   brandsUserEmployee = ue
                               }).ToListAsync();

            //we might need to check for co contacts exist.
            var contacts = await (from ct in ePDATAContext.CoContacts_Ts
                                  join e in ePDATAContext.Employees on new { Conum = ct.EmpConum, ct.Empnum } equals new { e.Conum, e.Empnum }
                                  where ct.Conum == company.Conum
                                  select new
                                  {
                                      ct.EmpConum,
                                      e.Empnum,
                                      e.FirstName,
                                      e.MiddleName,
                                      e.LastName,
                                      e.UserEmail,
                                      e.ContactCellphone,
                                      e.EntryId,
                                  }).ToListAsync();

            var result = (from c in contacts
                          join u in users on new { c.EmpConum, c.Empnum } equals new { u.brandsUserEmployee.EmpConum, u.brandsUserEmployee.Empnum }
                          select new
                          {
                              Email = u.user.Email,
                              FullName = $"{c.FirstName} {c.LastName}",
                              UserId = u.user.Id,
                              UserName = u.user.Email,
                              u.brandsUserEmployee
                          }).ToList();
            foreach (var item in result)
            {
                company.Users.Add(new CompanyThenUsers.PaydeckUser
                {
                    UserId = item.UserId.ToString(),
                    FullName = item.FullName,
                    UserEmail = item.Email,
                    UserName = item.UserName,
                    UserType = "Paydeck",
                    UserPermissions = (await permissionDataService.GetUserPermission(item.brandsUserEmployee)).Where(u => u.Value).Select(u => u.Key).ToList()
                });
            }

            var epComps = await ePDATAContext.EpsUsers
                .Where(u => u.conum == company.Conum && u.conum.HasValue && u.user_type == "PlatinumPay")
                .Select(u => u.username).ToListAsync();
            epComps.AddRange(await ePDATAContext.EpsUsersDetails.Where(u => u.Conum == company.Conum).Select(u => u.UserName).ToListAsync());
            epComps.Distinct().ToList().ForEach(epu => company.Users.Add(new CompanyThenUsers.PaydeckUser
            {
                UserId = epu.ToString(),
                UserPermissions = new List<string> { "hasAccess" }
            }));
        }

        private async Task<List<CompanyThenUsers>> GetAllowedCompanies()
        {
            var brandsUser = await ePDATAContext.Users
                .Include(u => u.BrandsUserEmployees)
                .SingleOrDefaultAsync(u => u.Id == User.GetPaydeckUserId());
            var assignedConums = await permissionDataService.VerifyRelatedContactsExist(brandsUser);
            foreach (var co in assignedConums.ToArray())
            {
                var brandsUserEmployee = brandsUser.BrandsUserEmployees.Single(e => e.Conum == User.GetConum() && e.IsActive);
                if (!(await permissionDataService.GetUserPermission(brandsUserEmployee))[Permission.ManageContactsAndUsers])
                {
                    assignedConums.Remove(co);
                }
            }


            //pp companies
            var epComps = await ePDATAContext.EpsUsers
                .Where(u => u.conum == User.GetConum() && u.conum.HasValue && u.user_type == "PlatinumPay")
                .Select(u => u.conum.Value).ToListAsync();
            epComps.AddRange(await ePDATAContext.EpsUsersDetails.Where(u => u.Conum == User.GetConum()).Select(u => u.Conum).ToListAsync());
            assignedConums.AddRange(epComps.Where(ep => !assignedConums.Contains(ep)));

            var companies = new List<CompanyThenUsers>();
            foreach (var item in assignedConums)
            {
                var comp = await ePDATAContext.Companies.Select(c => new { c.Conum, c.CoName }).SingleAsync(c => c.Conum == item);
                companies.Add(new CompanyThenUsers
                {
                    Conum = item,
                    CoName = $"{comp.Conum} - {comp.CoName}"
                });
            }

            return companies.OrderBy(c => c.Conum).ToList();
        }

        public class UserThenCompanies
        {
            public string UserId { get; set; }
            public string UserName { get; set; }
            public string UserType { get; set; }
            public string Name { get; set; }
            public string FullName { get; set; }
            public List<Company> Companies { get; set; }

            public class Company
            {
                public decimal Conum { get; set; }
                public List<string> Permissions { get; set; }
            }
        }

        public class CompanyThenUsers
        {
            public CompanyThenUsers() => Users = new List<PaydeckUser>();

            public decimal Conum { get; set; }
            public string CoName { get; set; }
            public List<PaydeckUser> Users { get; set; }

            public class PaydeckUser
            {
                public string UserId { get; set; }
                public string UserName { get; set; }
                public string UserEmail { get; set; }
                public List<string> UserPermissions { get; set; }
                public string UserType { get; set; }
                public string FullName { get; set; }

                public override int GetHashCode() => UserId.GetHashCode() + UserName.GetHashCode();

                public override string ToString() => UserId.ToString();
            }
        }


        private async Task<List<PermissionsGroup>> GetPermissionsAsync()
        {
            var per = new List<PermissionsGroup>();
            var paydeckPermissions = await ePDATAContext.BrandsAuthClaims
                .Where(c => c.IsVisible)
                .Select(c => new PaydecPermission { Id = c.ClaimValue, Label = c.DisplayName })
                .ToListAsync();
            per.Add(new PermissionsGroup
            {
                Id = "Paydeck",
                Label = "PayDeck",
                Permissions = paydeckPermissions
            });
            per.Add(new PermissionsGroup
            {
                Id = "PlatinumPay",
                Label = "PlatinumPay",
                Permissions = new List<PaydecPermission> { new PaydecPermission { Id = "hasAccess", Label = "Has Access" } }
            });
            return per;
        }

        public class PermissionsGroup
        {
            public string Id { get; set; }
            public string Label { get; set; }
            public List<PaydecPermission> Permissions { get; set; }
        }

        public class PaydecPermission
        {
            public string Id { get; set; }
            public string Label { get; set; }
        }
    }
}
