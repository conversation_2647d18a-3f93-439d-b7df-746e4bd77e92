﻿using Brands.DAL;
using Brands.DataModels;
using BrandsWebApp.Authentication;
using BrandsWebApp.Extensions;
using BrandsWebApp.Models.Ess.BrandsAdmin;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Auth;
using BrandsWebApp.Services.EmployeeDeck;
using Dapper;
using Flurl.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [Route("api/ess")]
    [ApiController]
    [Authorize(Policy = nameof(Permission.ManageEmployeeDeck))]
    public class EssController : ControllerBase
    {
        private readonly EPDATAContext ePDATAContext;
        private readonly ILogger<EssController> logger;
        private readonly ISqlConnectionService sqlConnectionService;
        private readonly EssUserService essUserService;
        private readonly UserEventService userEventService;

        public EssController(
            EPDATAContext ePDATAContext,
            ILogger<EssController> logger,
            ISqlConnectionService sqlConnectionService,
            EssUserService essUserService,
            UserEventService userEventService)
        {
            this.ePDATAContext = ePDATAContext;
            this.logger = logger;
            this.sqlConnectionService = sqlConnectionService;
            this.essUserService = essUserService;
            this.userEventService = userEventService;
        }

        [HttpGet("IsAutoInviteSetup")]
        public async Task<IActionResult> IsAutoInviteSetup()
        {
            logger.LogDebug("Entering IsAutoInviteSetup");
            try
            {
                bool hasSetup = await HasActiveAutoInviteSetup();
                return Ok(hasSetup);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in IsAutoInviteSetup");
                return BadRequest();
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetEssCompanyUser([FromQuery] decimal[] empNums)
        {
            logger.LogDebug("Entering GetEssCompanyUser. empNums: {empNums}", empNums);
            try
            {
                var essUsers = await GetEssUsers(empNums);
                return Ok(essUsers);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetEssCompanyUser.");
                throw;
            }
        }

        [HttpGet("Employees")]
        public async Task<IActionResult> GetEssEmployees([FromQuery] decimal[] empNums)
        {
            logger.LogDebug("Entering GetEssEmployees. empNums: {empNums}", empNums);
            try
            {
                var essUsers = await GetEssUsers(empNums);
                return Ok(new
                {
                    Employees = essUsers,
                    AutoInvite = await HasActiveAutoInviteSetup()
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetEssCompanyUser.");
                throw;
            }
        }

        [HttpGet("V1/Employees")]
        public async Task<IActionResult> GetEmployeesV1()
        {
            logger.LogDebug("Entering GetEmployeeSummary.");
            try
            {
                await using var con = sqlConnectionService.GetSqlConnection();
                var employeeSummaries = await con.QueryAsync<EmployeeList>(SqlEmployeeList, new { Conum = HttpContext.User.GetConum() });
                return Ok(new
                {
                    employeeSummaries
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetEssCompanyUser.");
                throw;
            }
        }

        [HttpGet("EmployeeSummary")]
        public async Task<IActionResult> GetEmployeeSummary()
        {
            logger.LogDebug("Entering GetEmployeeSummary.");
            try
            {
                await using var con = sqlConnectionService.GetSqlConnection();
                var employeeSummaries = await con.QueryFirstAsync<EmployeeSummary>(SqlEmployeeSummary, new { Conum = HttpContext.User.GetConum() });
                return Ok(new
                {
                    employeeSummaries
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetEssCompanyUser.");
                throw;
            }
        }

        [HttpGet("GetSettings")]
        public async Task<IActionResult> GetSettings()
        {
            logger.LogDebug("Entering GetSettings");
            try
            {
                var hiddenPayrolls = await ePDATAContext.EssHiddenPayrolls.Where(x => x.CoNum == User.GetConum()).ToListAsync();
                var lastPayroll = await ePDATAContext.Payrolls
                        .Where(p => p.CONUM == User.GetConum() && !new[] { "Prior Payroll", "Prior Taxes" }.Contains(p.PyarollStatus))
                        .OrderByDescending(p => p.PRNUM)
                        .Select(p => new { p.PRNUM, p.CHECK_DATE })
                        .FirstOrDefaultAsync();

                CoOptionPayroll coOptionsPayroll = await ePDATAContext.CoOptionPayrolls.SingleOrDefaultAsync(x => x.Conum == HttpContext.User.GetConum());
                bool isAutoInviteActive = await HasActiveAutoInviteSetup();

                return Ok(new
                {
                    HiddenPayrolls = hiddenPayrolls,
                    LastPayroll = $"#{lastPayroll?.PRNUM} - {lastPayroll?.CHECK_DATE:d}",
                    LastPayrollNumber = lastPayroll?.PRNUM,
                    LastPayrollDate = lastPayroll?.CHECK_DATE,
                    allowDd = (!coOptionsPayroll?.IsDdBlocked.FromYesNo() ?? true),
                    allowEmContact = (!coOptionsPayroll?.IsEmContactBlocked.FromYesNo() ?? true),
                    allowW4 = (!coOptionsPayroll?.IsW4Blocked.FromYesNo() ?? true),
                    isAutoInviteActive
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetSettings.");
                throw;
            }
        }

        [HttpPost("AddHiddenPayrolls")]
        public async Task<IActionResult> AddHiddenPayrolls(EssHiddenPayrolls essHiddenPayrolls)
        {
            logger.LogDebug("Entering AddHiddenPayrolls");
            try
            {
                if (essHiddenPayrolls == null || essHiddenPayrolls.Prnum == 0 || essHiddenPayrolls.UntilDate == DateTime.MinValue)
                {
                    return BadRequest("Payroll number or date missing");
                }

                var isAlreadyHidden = await ePDATAContext.EssHiddenPayrolls.CountAsync(x => x.Prnum == essHiddenPayrolls.Prnum && x.CoNum == HttpContext.User.GetConum());
                if (isAlreadyHidden > 0)
                {
                    return BadRequest(new { message = "Payroll is already hidden" });
                }
                else
                {
                    var hiddenPayroll = new EssHiddenPayrolls
                    {
                        CoNum = User.GetConum(),
                        Prnum = essHiddenPayrolls.Prnum,
                        UntilDate = essHiddenPayrolls.UntilDate,
                        AddedOn = DateTime.Now,
                        AddedBy = HttpContext.User.GetUserId()
                    };
                    ePDATAContext.EssHiddenPayrolls.Add(hiddenPayroll);
                    await ePDATAContext.SaveChangesAsync();
                }

                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in AddHiddenPayrolls.");
                throw;
            }
        }

        [HttpPost("DeleteHiddenPayrolls/{prNum:decimal}")]
        public async Task<IActionResult> DeleteHiddenPayrolls([FromRoute] decimal prNum)
        {
            logger.LogDebug("Entering DeleteHiddenPayrolls");
            try
            {
                if (prNum == 0)
                {
                    return BadRequest("Invalid Payroll Number.");
                }

                var hiddenPr = await ePDATAContext.EssHiddenPayrolls.FirstOrDefaultAsync(x => x.Prnum == prNum && x.CoNum == HttpContext.User.GetConum());
                if (hiddenPr != null)
                {
                    ePDATAContext.EssHiddenPayrolls.Remove(hiddenPr);
                    await ePDATAContext.SaveChangesAsync();
                    return Ok("Deleted");
                }
                else
                {
                    return Ok("Payroll number does not exist");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in DeleteHiddenPayrolls.");
                throw;
            }
        }

        [HttpPost("UpdateSettings")]
        public async Task<IActionResult> UpdateSettings(UpdateSettingsRequest updateSettingsRequest)
        {
            logger.LogDebug("Entering UpdateSettings");
            try
            {
                var coOptionPayroll = await ePDATAContext.CoOptionPayrolls.SingleOrDefaultAsync(x => x.Conum == HttpContext.User.GetConum());
                if (coOptionPayroll == null)
                {
                    coOptionPayroll = new CoOptionPayroll { Conum = HttpContext.User.GetConum() };
                    ePDATAContext.CoOptionPayrolls.Add(coOptionPayroll);
                }

                coOptionPayroll.IsDdBlocked = (!updateSettingsRequest.AllowDd).ToYesNo();
                coOptionPayroll.IsW4Blocked = (!updateSettingsRequest.AllowW4).ToYesNo();
                coOptionPayroll.IsEmContactBlocked = (!updateSettingsRequest.AllowEmContact).ToYesNo();
                await ePDATAContext.SaveChangesAsync();
                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdateSettings.");
                throw;
            }
        }

        [HttpPost("SetAutoInvite")]
        public async Task<IActionResult> SetAutoInvite(SetAutoInviteRequest request)
        {
            try
            {
                logger.LogDebug("Entering SetAutoInvite");
                await ValidateSetup();
                var export = await ePDATAContext.CoOptionsExports.SingleOrDefaultAsync(e => e.CoCode == HttpContext.User.GetConum() && e.ExportFormat == 112);
                if (export == null)
                {
                    export = new CoOptionsExport
                    {
                        CoCode = HttpContext.User.GetConum(),
                        AutoSend = true,
                        CutOffDate = DateTime.Today.AddDays(-1),
                        ExportFormat = 112,
                        PerPayrollScheduleType = 0,
                        WSCreated = $"PD: {HttpContext.User.Identity.Name} {DateTime.Now:g} {Environment.MachineName}",
                        FileCategory = "DD Vouchers"
                    };
                    ePDATAContext.CoOptionsExports.Add(export);
                }
                export.IsDisabled = !request.AutoInvite;
                export.CutOffDate = request.AutoInvite ? DateTime.Today.AddDays(-1) : export.CutOffDate;
                export.AutoSend = request.AutoInvite ? true : false;
                await ePDATAContext.SaveChangesAsync();
                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in SetAutoInvite");
                return BadRequest();
            }
        }

        [HttpPost("UpdateEmail")]
        public async Task<IActionResult> UpdateEmail(UpdateEmployeeRequest updateEmailRequest)
        {
            try
            {
                logger.LogDebug("Entering UpdateEmail. {@updateEmailRequest}", updateEmailRequest);
                if (!IsValidEmail(updateEmailRequest.Email))
                {
                    logger.LogWarning("Invalid email format");
                    return BadRequest("Invalid email address");
                }
                var employees = ePDATAContext.Employees.Where(e => e.Conum == HttpContext.User.GetConum() && e.Empnum == updateEmailRequest.Empnum);
                if (employees.Count() != 1)
                {
                    logger.LogWarning("Employee Count was not 1");
                    return BadRequest();
                }
                var employee = employees.Single();
                logger.LogDebug("Updating email from: {FromEmail} To: {ToEmail}", employee.UserEmail, updateEmailRequest.Email);
                employee.UserEmail = updateEmailRequest.Email;
                await ePDATAContext.SaveChangesAsync();
                return Ok(await GetEssUser(updateEmailRequest.Empnum));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UpdateEmail");
                throw;
            }
        }

        [HttpPost("RetractInvite")]
        public async Task<IActionResult> RetractInvite(UpdateEmployeeRequest updateEmployee)
        {
            try
            {
                logger.LogDebug("Entering RetractInvite. {@RetractInvite}", updateEmployee);
                var employee = await ePDATAContext.Employees.SingleAsync(e => e.Conum == HttpContext.User.GetConum() && e.Empnum == updateEmployee.Empnum);
                var essAuth = await ePDATAContext.Users
                    .Include(e => e.BrandsUserEssEmployees)
                    .ExactlyOneOrZeroAsync(u => u.Email == updateEmployee.Email);

                if (essAuth == null)
                {
                    return BadRequest("User does not exist.");
                }

                if (!essAuth.BrandsUserEssEmployees.Any(ue => ue.EmployeeEntryId == employee.EntryId))
                {
                    logger.LogWarning("tried RetractInvite but the user {UserId} that matches this email {Email}, has no profile for this emp# {Empnum}", essAuth.Id, updateEmployee.Email, updateEmployee.Empnum);
                    return BadRequest("Something went wrong, please try again.");
                }

                if (essAuth.BrandsUserEssEmployees.Count == 1 && !essAuth.BrandsUserEmployees.Any())
                {
                    logger.LogDebug("Ess user only has one profile, deleting user. {UserId}", essAuth.Id);
                    ePDATAContext.Users.Remove(essAuth);
                }
                else
                {
                    logger.LogDebug("Ess user has multiple profiles, deleting profile. {UserId} {EntryId}", essAuth.Id, employee.EntryId);
                    var profile = essAuth.BrandsUserEssEmployees.Single(ue => ue.EmployeeEntryId == employee.EntryId);
                    ePDATAContext.BrandsUserEssEmployees.Remove(profile);
                }
                await ePDATAContext.SaveChangesAsync();
                return Ok(await GetEssUser(updateEmployee.Empnum));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in RetractInvite");
                throw;
            }
        }

        [HttpPost("InviteEmployees")]
        public async Task<IActionResult> InviteEmployees(InviteEmployeesRequest inviteEmployeesRequest)
        {
            logger.LogDebug("Entering InviteEmployees. EmpNums: {EmpNums}", inviteEmployeesRequest.EmpNums);
            await ValidateSetup();
            try
            {
                var inviteEssUsers = new InviteEssUsers
                {
                    CancelOnRejection = false,
                    TestOnly = false,
                    EmpNums = inviteEmployeesRequest.EmpNums,
                    CancelOnUsersExist = false
                };
                var inviteResult = await essUserService.InviteCompanyUsersAsync((int)HttpContext.User.GetConum(), inviteEssUsers);
                return Ok(inviteResult);

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in InviteEmployees");
            }
            return BadRequest();
        }

        [HttpPost("ResendInvite")]
        public async Task<IActionResult> ResendInvitation(ResendInvitationsRequest resendInvitationsRequest)
        {
            logger.LogDebug("Entering ResendInvitation. EmpNums: {EmpNums}", resendInvitationsRequest.EmpNums);

            try
            {
                await essUserService.ResendInvitationAsync(HttpContext.User.GetConum(), resendInvitationsRequest.EmpNums);
                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in ResendInvitation");
            }

            return BadRequest();
        }

        [HttpPost("ResetPassword")]
        public async Task<IActionResult> ResetPassword(ResetEssUserPassword resetPasswordDetails)
        {
            logger.LogDebug("Entering ResetPassword. EmpNum: {EmpNum}", resetPasswordDetails.EmpNum);
            var result = await essUserService.ResetPasswordAsync(HttpContext.User.GetConum(), resetPasswordDetails);
            if (result.Errors.Any())
            {
                return BadRequest(result.Errors);
            }

            return Ok(result.Password);
        }

        [HttpPost("EssAutoInviteExcluded")]
        public async Task<IActionResult> EssAutoInviteExcluded(EssAutoInviteExcludedRequest request)
        {
            logger.LogDebug("Entering EssAutoInviteExcluded. {@Request}", request);
            try
            {
                var employeeOption = await ePDATAContext.EmployeeOptions.SingleOrDefaultAsync(eo => eo.CoNum == HttpContext.User.GetConum() && eo.EmpNum == request.Empnum);
                if (employeeOption == null)
                {
                    employeeOption = new EmployeeOptions { CoNum = HttpContext.User.GetConum(), EmpNum = request.Empnum };
                    ePDATAContext.EmployeeOptions.Add(employeeOption);
                }
                employeeOption.EssAutoInviteExcluded = request.Excluded;
                await ePDATAContext.SaveChangesAsync();
                return Ok(await GetEssUser(request.Empnum));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in EssAutoInviteExcluded");
                return BadRequest();
            }
        }

        [HttpPost("UnlockAccount")]
        public async Task<IActionResult> UnlockEssAccount(UpdateEmployeeRequest request)
        {
            logger.LogDebug("Entering UnlockEssAccount. {@Request}", request);
            try
            {
                var essUser = await ePDATAContext.Users.SingleOrDefaultAsync(u => u.Email == request.Email);
                if (essUser == null)
                {
                    string message = $"User with email {request.Email} was not found.";
                    logger.LogWarning(message);
                    return NotFound(message);
                }

                if (essUser.LockoutEnd == null || essUser.LockoutEnd < DateTime.Now)
                {
                    string message = $"Account {request.Email} is not locked.";
                    logger.LogWarning(message);
                    return BadRequest(message);
                }

                essUser.LockoutEnd = null;
                userEventService.AddUserEvent(essUser, "Reset Lockout", true);
                await ePDATAContext.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in UnlockEssAccount");
                return BadRequest();
            }
        }

        private async Task<bool> HasActiveAutoInviteSetup()
        {
            return await ePDATAContext.CoOptionsExports.AnyAsync(e => e.CoCode == HttpContext.User.GetConum() && e.ExportFormat == 112
                                && (!e.IsDisabled.HasValue || (e.IsDisabled.HasValue && e.IsDisabled.Value == false)));
        }

        private async Task<EssCompanyUser> GetEssUser(decimal empNum)
        {
            logger.LogDebug("Entering GetEssUser. {Empnum}", empNum);
            var users = await GetEssUsers(new decimal[] { empNum });
            return users.FirstOrDefault();
        }

        private async Task<IEnumerable<EssCompanyUser>> GetEssUsers(decimal[] empNums)
        {
            logger.LogDebug("Entering GetEssUsers. {Empnum}", empNums);
            var employees = await essUserService.GetEmployeesAsync(HttpContext.User.GetConum(), empNums);
            var essUsers = employees.Select(e => new EssCompanyUser
            {
                Rejection = e.Rejection,
                AddedProfileOnly = e.AddedProfileOnly,
                IsLockedOut = e.IsLockedOut,
                Empnum = e.EmpNum,
                FirstName = e.FName,
                LastName = e.LName,
                UserEmail = e.UserEmail,
                Department = e.Department,
                TermDate = e.TermDate
            });

            return essUsers;
        }

        private async Task ValidateSetup()
        {
            await ValidateProviderIsSetupAsync();
            await ValidateQbEssStubIsSetupAsync();
            await ValidateQbEssPerEmpW2IsSetup();
        }

        private async Task ValidateProviderIsSetupAsync()
        {
            logger.LogDebug("Entering ValidateProviderIsSetupAsync");
            try
            {
                var integrationAccount = await ePDATAContext.IntegrationAccounts.SingleOrDefaultAsync(ia => ia.provider_id == 9999 && ia.conum == HttpContext.User.GetConum() && ia.account == "Brands1");
                if (integrationAccount == null)
                {
                    logger.LogDebug("integrationAccount is null, will add a new record");
                    var ids = await ePDATAContext.IntegrationAccounts.Where(ia => ia.provider_id == 9999 && ia.conum == HttpContext.User.GetConum()).Select(ia => ia.id).ToListAsync();
                    int nextId = ids.Any() ? ids.Max() + 1 : 1;
                    logger.LogDebug("NextId: {NextId}", nextId);
                    ePDATAContext.IntegrationAccounts.Add(new IntegrationAccounts()
                    {
                        provider_id = 9999,
                        conum = HttpContext.User.GetConum(),
                        id = Convert.ToInt16(nextId),
                        account = "Brands1",
                        type = "service",
                        active = "YES"
                    });
                }
                else if (integrationAccount.active != "YES")
                {
                    logger.LogWarning("Provider was not active. {OldActive}", integrationAccount.active);
                    integrationAccount.active = "YES";
                }
                else
                {
                    logger.LogDebug("Provider is already setup");
                }
                await ePDATAContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in ValidateProviderIsSetupAsync");
                throw;
            }
        }

        private async Task ValidateQbEssStubIsSetupAsync()
        {
            logger.LogDebug("Entering ValidateQbEssStubIsSetup");
            try
            {
                var export = await ePDATAContext.CoOptionsExports.SingleOrDefaultAsync(e => e.CoCode == HttpContext.User.GetConum() && e.ExportFormat == 111);
                if (export == null)
                {
                    logger.LogDebug("QbExport was not setup yet.");
                    ePDATAContext.CoOptionsExports.Add(new CoOptionsExport()
                    {
                        CoCode = HttpContext.User.GetConum(),
                        AutoSend = true,
                        CutOffDate = DateTime.Today.AddYears(-2),
                        ExportFormat = 111,
                        PerPayrollScheduleType = 2,
                        DaysAfterCheckDate = 0,
                        WSCreated = $"PD: {HttpContext.User.Identity.Name} {DateTime.Now:g} {Environment.MachineName}",
                        FileCategory = "DD Vouchers"
                    });
                }
                else if (export.IsDisabled.GetValueOrDefault())
                {
                    logger.LogDebug("QbExport job was disabled");
                    export.IsDisabled = false;
                }
                else
                {
                    logger.LogDebug("QbExport is already setup");
                }
                await ePDATAContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in ValidateQbEssStubIsSetup");
                throw;
            }
        }

        private async Task ValidateQbEssPerEmpW2IsSetup()
        {
            logger.LogDebug("Entering ValidateQbEssPerEmpW2IsSetup");
            try
            {
                var export = await ePDATAContext.CoOptionsExports.SingleOrDefaultAsync(e => e.CoCode == HttpContext.User.GetConum() && e.ExportFormat == 113);
                var payrolls = ePDATAContext.Payrolls.Where(p => p.CONUM == HttpContext.User.GetConum()
                        && !(new string[] { "Prior Payroll", "Prior Taxes", "Entering Checks", "Submitted" }.Contains(p.PyarollStatus)));
                if (!await payrolls.AnyAsync())
                {
                    logger.LogDebug($"This company does not have any payrolls with status Done.{Environment.NewLine}There's no need for this job!");
                    return;
                }
                var lastCheckDate = payrolls.Max(p => p.CHECK_DATE);
                if (export == null)
                {
                    logger.LogDebug("QbExport was not setup yet.");
                    ePDATAContext.CoOptionsExports.Add(new CoOptionsExport()
                    {
                        CoCode = HttpContext.User.GetConum(),
                        AutoSend = true,
                        CutOffDate = lastCheckDate.Value.AddDays(-1),
                        ExportFormat = 113,
                        PerPayrollScheduleType = 0,
                        WSCreated = $"PD: {HttpContext.User.Identity.Name} {DateTime.Now:g} {Environment.MachineName}",
                        FileCategory = "DD Vouchers"
                    });
                }
                else
                {
                    logger.LogDebug("QbExport [ESS Save W2] is already setup");
                }
                await ePDATAContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in ValidateQbEssPerEmpW2IsSetup");
                throw;
            }
        }

        bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        public class UpdateSettingsRequest
        {
            public bool? AllowDd { get; set; }
            public bool? AllowW4 { get; set; }
            public bool? AllowEmContact { get; set; }
        }

        public class SetAutoInviteRequest
        {
            public bool AutoInvite { get; set; }
        }

        public class UpdateEmployeeRequest
        {
            public decimal Empnum { get; set; }
            public string Email { get; set; }
        }

        public class EssCompanyUser
        {
            public string Rejection { get; set; }
            public bool AddedProfileOnly { get; set; }
            public bool IsLockedOut { get; set; }
            public decimal Empnum { get; set; }
            public string FirstName { get; set; }
            public string LastName { get; set; }
            public string UserEmail { get; set; }
            public string Department { get; set; }
            public DateTime? TermDate { get; set; }
        }

      
      

        public class InviteEmployeesRequest
        {
            public decimal[] EmpNums { get; set; }
        }

        public class ResendInvitationsRequest
        {
            public decimal[] EmpNums { get; set; }
        }

        public class EssAutoInviteExcludedRequest
        {
            public int Empnum { get; set; }
            public bool Excluded { get; set; }
        }

        public class EmployeeSummary
        {
            public int ActiveUsers { get; set; }
            public int PendingRegistration { get; set; }
            public int InviteNewEmployees { get; set; }
            public int MissingDetails { get; set; }
        }

        public class EmployeeList
        {
            public decimal Conum { get; set; }
            public decimal Empnum { get; set; }
            public int EntryId { get; set; }
            public string FName { get; set; }
            public string LName { get; set; }
            public DateTime? TermDate { get; set; }
            public string Email { get; set; }
            public bool IsValidEmail { get; set; }
            public bool IsActive { get; set; }
            public bool IsPendingRegistration { get; set; }
            public bool IsInviteNewEmployee { get; set; }
            public bool IsMissingDetails { get; set; }
            public bool IsExcluded { get; set; }
            public bool IsLockedOut { get; set; }
            public string Rejection { get; set; }
        }

        const string SqlEmployeeSummary = @"SELECT 
	SUM(ess.IsActive) ActiveUsers, 
	SUM(ess.IsPendingRegistration) PendingRegistration, 
	SUM(ess.IsInviteNewEmployee) InviteNewEmployees, 
	SUM(ess.IsMissingDetails) MissingDetails 
	FROM (
	SELECT e.EMPNUM, e.F_NAME, e.L_NAME, e.TERM_DATE, e.Email, e.IsValidEmail, eu.UserName, e.entry_id,
		IIF(ue.UserID IS NOT NULL AND eu.PasswordHash IS NOT NULL AND eu.ShouldResetPassword = 0, 1, 0) IsActive,
		IIF(ue.UserID IS NOT NULL AND (eu.PasswordHash IS NULL OR eu.ShouldResetPassword = 1), 1, 0) IsPendingRegistration,
		IIF(ue.UserID IS NULL AND e.TERM_DATE IS NULL AND e.IsValidEmail = 1 AND ISNULL(eo.EssAutoInviteExcluded, 0) = 0, 1, 0) IsInviteNewEmployee,
		IIF(ue.UserID IS NULL AND e.TERM_DATE IS NULL AND e.IsValidEmail = 0, 1,0) IsMissingDetails
	FROM (SELECT a.*, IIF(a.Email LIKE '[a-z,0-9,_,-]%@[a-z,0-9,_,-]%.[a-z][a-z]%'
					AND PATINDEX('[a-z,0-9,_,-]%@[a-z,0-9,_,-]%.[a-z][a-z]%', a.email) = 1
					AND PATINDEX('%[^a-z,0-9,+,@,.,_,\-]%', a.email) = 0, 1, 0) IsValidEmail 
			FROM (SELECT u.CONUM, u.EMPNUM, u.F_NAME, u.L_NAME, u.TERM_DATE, U.entry_id,
				CASE WHEN u.contact_homeemail IS NULL OR LEN(LTRIM(RTRIM(u.contact_homeemail))) = 0
					THEN LTRIM(RTRIM(u.user_email))
					ELSE LTRIM(RTRIM(u.contact_homeemail))
			END Email
			FROM EMPLOYEE_T U) a
		) e 
	LEFT OUTER JOIN custom.BrandsAuthUserEssEmployees ue ON ue.EmployeeEntryID = e.entry_id
	LEFT OUTER JOIN custom.BrandsAuthUser eu ON ue.UserID = eu.Id
    LEFT OUTER JOIN custom.EmployeeOptions eo ON e.CONUM = eo.CoNum AND e.EMPNUM = eo.EmpNum
	WHERE e.CONUM = @Conum
	) ess ";

        const string SqlEmployeeList = @"--DECLARE @conum AS DECIMAL(6,0) = 812;
            WITH emp1 AS (
	            SELECT u.CONUM, u.EMPNUM, U.entry_id as EntryId, u.F_NAME FName, u.L_NAME LName, u.TERM_DATE AS TermDate,
		            CASE WHEN u.contact_homeemail IS NULL OR LEN(LTRIM(RTRIM(u.contact_homeemail))) = 0
			            THEN LTRIM(RTRIM(u.user_email))
			            ELSE LTRIM(RTRIM(u.contact_homeemail))
	            END Email
	            FROM EMPLOYEE_T U
            ), emp AS (
	            SELECT a.*, IIF(a.Email LIKE '[a-z,0-9,_,-]%@[a-z,0-9,_,-]%.[a-z][a-z]%'
		            AND PATINDEX('[a-z,0-9,_,-]%@[a-z,0-9,_,-]%.[a-z][a-z]%', a.email) = 1
		            AND PATINDEX('%[^a-z,0-9,+,@,.,_,\-]%', a.email) = 0, 1, 0) IsValidEmail 
	            FROM emp1 a
            )
            SELECT *
	            FROM (
	            SELECT e.CONUM, e.EMPNUM, e.EntryId, e.FName, e.LName, e.TermDate,
		            ISNULL(eu.Email, e.Email) Email,
		            e.IsValidEmail,
		            IIF(ue.UserID IS NOT NULL AND eu.PasswordHash IS NOT NULL AND eu.ShouldResetPassword = 0, 1, 0) IsActive,
		            IIF(ue.UserID IS NOT NULL AND (eu.PasswordHash IS NULL OR eu.ShouldResetPassword = 1), 1, 0) IsPendingRegistration,
		            IIF(ue.UserID IS NULL AND e.TermDate IS NULL AND e.IsValidEmail = 1 AND ISNULL(eo.EssAutoInviteExcluded, 0) = 0, 1, 0) IsInviteNewEmployee,
		            IIF(ue.UserID IS NULL AND e.TermDate IS NULL AND e.IsValidEmail = 0, 1,0) IsMissingDetails,
		            IIF(ue.UserID IS NULL AND e.TermDate IS NULL AND ISNULL(eo.EssAutoInviteExcluded, 0) = 1, 1, 0) IsExcluded,
                    IIF(eu.LockoutEnd > GETDATE(), 1, 0) IsLockedOut,
		            CASE 
			            WHEN ue.UserID IS NULL AND LEN(e.Email) < 1 THEN 'Missing email.'
			            WHEN ue.UserID IS NULL AND e.IsValidEmail = 0 THEN 'Invalid email.'
			            ELSE NULL 
                    END Rejection
	            FROM emp e
	            LEFT OUTER JOIN custom.BrandsAuthUserEssEmployees ue ON ue.EmployeeEntryID = e.EntryId
	            LEFT OUTER JOIN custom.BrandsAuthUser eu ON ue.UserID = eu.Id
	            LEFT OUTER JOIN custom.EmployeeOptions eo ON e.CONUM = eo.CoNum AND e.EMPNUM = eo.EmpNum
	            WHERE e.CONUM = @Conum
	            ) ess";
    }
}
