﻿using BrandsWebApp.Authentication;
using BrandsWebApp.Models.Department;
using BrandsWebApp.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BrandsWebApp.Api
{
    [ApiController]
    [Route("api/Divisions")]
    [Authorize(Policy = nameof(Permission.ManageContactsAndUsers))]
    public class DivisionsController : ControllerBase
    {
        private readonly DivisionService divisionService;
        private readonly DepartmentService departmentService;

        public DivisionsController(DivisionService divisionService, DepartmentService departmentService)
        {
            this.divisionService = divisionService;
            this.departmentService = departmentService;
        }

        /// <summary>
        /// Gets divisions for current company
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetDivisions()
        {
            var divisions = await divisionService.GetDivisionsAsync(User.GetConum());
            return Ok(divisions);
        }

        /// <summary>
        /// Gets workers comp codes
        /// </summary>
        [HttpGet("WorkersCompCodes")]
        public async Task<IActionResult> GetWorkersCompCodes()
        {
            var codes = await departmentService.GetDepartmentsWorkersCompCodesAsync(User.GetConum());
            return Ok(codes);
        }

        /// <summary>
        /// Gets departments by division for current company
        /// </summary>
        [HttpGet("{divisionNumber}/Departments")]
        public async Task<IActionResult> GetDepartments([FromRoute] decimal divisionNumber)
        {
            var divisions = await departmentService.GetDepartmentsAsync(User.GetConum(), divisionNumber);
            return Ok(divisions);
        }

        /// <summary>
        /// Gets next department number for current company
        /// </summary>
        [HttpGet("{divisionNumber}/Departments/Number")]
        public async Task<IActionResult> GetNextDepartmentNumber([FromRoute] decimal divisionNumber)
        {
            var number = await departmentService.GetNextDepartmentNumberAsync(User.GetConum(), divisionNumber);
            return Ok(number);
        }

        /// <summary>
        /// Adds department for current company
        /// </summary>
        [HttpPost("{divisionNumber}/Departments")]
        public async Task<IActionResult> AddDepartment([FromRoute] decimal divisionNumber, [FromBody] UpsertCompanyDepartment department)
        {
            department.DivNum = divisionNumber;
            await departmentService.AddDepartmentAsync(User.GetConum(), department);
            return NoContent();
        }

        /// <summary>
        /// Updates department for current company
        /// </summary>
        [HttpPatch("{divisionNumber}/Departments/{departmentNumber}")]
        public async Task<IActionResult> UpdateDepartment([FromRoute] decimal divisionNumber, [FromRoute] decimal departmentNumber, [FromBody] UpsertCompanyDepartment department)
        {
            department.DivNum = divisionNumber;
            department.DeptNum = departmentNumber;
            await departmentService.UpdateDepartmentAsync(User.GetConum(), department);
            return NoContent();
        }

        /// <summary>
        /// Deletes department for current company
        /// </summary>
        [HttpDelete("{divisionNumber}/Departments/{departmentNumber}")]
        public async Task<IActionResult> DeleteDepartment([FromRoute] decimal divisionNumber, [FromRoute] decimal departmentNumber)
        {
            await departmentService.DeleteDepartmentAsync(User.GetConum(), divisionNumber, departmentNumber);
            return NoContent();
        }
    }
}
