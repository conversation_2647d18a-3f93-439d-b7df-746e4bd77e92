﻿using Brands.DAL;
using BrandsWebApp.Authentication;
using BrandsWebApp.Hubs;
using BrandsWebApp.Models.PayrollApproval;
using BrandsWebApp.Services;
using BrandsWebApp.Services.Payroll;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Brands.DataModels;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BrandsWebApp.Api.Payrolls
{
    [AllowAnonymous]
    [ApiController]
    [Route("api/PayrollApproval")]
    public class PayrollApprovalController : Controller
    {
        private readonly EPDATAContext ePDATAContext;
        private readonly ILogger<PayrollApprovalController> logger;
        private readonly SecurityUtils securityUtils;
        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly PayrollApprovalService payrollApprovalService;
        private readonly RunReportsService runReportsService;

        public PayrollApprovalController(
            EPDATAContext ePDATAContext,
            ILogger<PayrollApprovalController> logger,
            SecurityUtils securityUtils,
            IHttpContextAccessor httpContextAccessor,
            PayrollApprovalService payrollApprovalService,
            RunReportsService runReportsService)
        {
            this.ePDATAContext = ePDATAContext;
            this.logger = logger;
            this.securityUtils = securityUtils;
            this.httpContextAccessor = httpContextAccessor;
            this.payrollApprovalService = payrollApprovalService;
            this.runReportsService = runReportsService;
        }


        /// <summary>
        /// Gets payroll email register details
        /// </summary>
        [HttpGet("EmailRegister")]
        public async Task<IActionResult> GetPayrollEmailRegisterDetails(string token)
        {
            logger.LogDebug("Entering PayrollApproval Get. Token: {Token}", token);
            PayrollRegisterTokenPayload payload = payrollApprovalService.ParseToken(token);

            var verification = await payrollApprovalService.VerifyPayload(payload);
            if (!verification.success)
            {
                return Ok(new { Message = verification.message });
            }
            else
            {
                return Ok(verification.result);
            }
        }

        /// <summary>
        /// Approves payroll email register
        /// </summary>
        [HttpPost("EmailRegister")]
        public async Task<IActionResult> ApprovePayrollEmailRegister(string token)
        {
            logger.LogDebug("Entering ApprovePayroll. Token: {Token}", token);
            PayrollRegisterTokenPayload payload = payrollApprovalService.ParseToken(token);

            var verification = await payrollApprovalService.VerifyPayload(payload);
            if (!verification.success)
            {
                return Ok(new { Message = verification.message });
            }
            else
            {
                string userName = await GetUserName();

                var p = await ePDATAContext.PrBatchInProcesses.SingleAsync(p => p.rowguid == payload.b);
                p.SentToAgentOn = DateTime.Now;
                p.DoNotShowInPrInProcess = false;
                p.AutoFixPayrollCompletedOn = null;
                p.ClientApprovedRegisterOn = DateTime.Now;
                p.ClientApprovedRegisterVia = "Paydeck";
                p.ClientApprovedRegisterBy = userName;
                p.LastOpenedBy = p.CompleteBy;
                p.LastOpenedDate = p.CompleteDate;
                p.CompleteBy = null;
                p.CompleteDate = null;

                bool isInternalIp = await securityUtils.IsInternalIpAddressAsync();
                var httpContext = httpContextAccessor.HttpContext;
                ePDATAContext.Pr_Batch_Notes.Add(new pr_batch_notes
                {
                    rowguid = Guid.NewGuid(),
                    Conum = verification.result.Conum,
                    PrNum = verification.result.Prnum,
                    DateEntered = DateTime.Now,
                    EnteredBy = "Paydeck",
                    Note = $"Payroll Approved Via Paydeck By {userName} On {DateTime.Now}. {(isInternalIp ? "(Internal IP)" : $"(IP: {httpContext.Connection.RemoteIpAddress})")}",
                    Priority = "3-Low",
                    ListID = Guid.NewGuid(),
                    EmployeeID = -997
                });
                await ePDATAContext.SaveChangesAsync();
                logger.LogDebug("Successfully approved payroll. Co: {conum} CoName: {CoName} Pr: {Prnum}", verification.result.Conum, verification.result.CoName, verification.result.Prnum);
                verification.result.ApprovedOn = DateTime.Now;
                return Ok(verification.result);
            }
        }

        /// <summary>
        /// Re-opens payroll for changes
        /// </summary>
        [HttpPost("ReopenForEdits")]
        public async Task<IActionResult> ReopenForEdits(string token)
        {
            logger.LogDebug("Entering ReopenForEdits. Token: {Token}", token);
            PayrollRegisterTokenPayload payload = payrollApprovalService.ParseToken(token);

            var verification = await payrollApprovalService.VerifyPayload(payload);
            if (!verification.success)
            {
                return Ok(new { Message = verification.message });
            }
            else
            {
                string userName = await GetUserName();

                var p = await ePDATAContext.PrBatchInProcesses.SingleAsync(p => p.rowguid == payload.b);
                p.ClientReopenedForEditsOn = DateTime.Now;
                p.SubmittedForApprovalOn = null;
                p.DoNotShowInPrInProcess = true;
                p.SentToAgentOn = null;
                p.EmailRegisterSentOn = null;
                p.ClientApprovedRegisterOn = null;
                p.ClientApprovedRegisterVia = null;
                p.ClientApprovedRegisterBy = null;
                p.LastOpenedBy = p.CompleteBy;
                p.LastOpenedDate = p.CompleteDate;
                p.CompleteBy = null;
                p.CompleteDate = null;

                bool isInternalIp = await securityUtils.IsInternalIpAddressAsync();
                var httpContext = httpContextAccessor.HttpContext;
                ePDATAContext.Pr_Batch_Notes.Add(new pr_batch_notes
                {
                    rowguid = Guid.NewGuid(),
                    Conum = verification.result.Conum,
                    PrNum = verification.result.Prnum,
                    DateEntered = DateTime.Now,
                    EnteredBy = "Paydeck",
                    Note = $"Payroll Reopened For Edits Via Paydeck By {userName} On {DateTime.Now}. {(isInternalIp ? "(Internal IP)" : $"(IP: {httpContext.Connection.RemoteIpAddress})")}",
                    Priority = "3-Low",
                    ListID = Guid.NewGuid(),
                    EmployeeID = -997
                });
                await ePDATAContext.SaveChangesAsync();
                logger.LogDebug("Successfully approved payroll. Co: {conum} CoName: {CoName} Pr: {Prnum}", verification.result.Conum, verification.result.CoName, verification.result.Prnum);
                verification.result.ReopenedForEditsOn = DateTime.Now;
                return Ok(verification.result);
            }
        }

        /// <summary>
        /// Notifies clients when payroll is processed
        /// </summary>
        [HttpPost("PayrollStatusUpdate")]
        public async Task<IActionResult> SendProcessedPayrollNotification(PayrollQueueUpdate payrollQueueUpdate, [FromServices] IHubContext<MainHub> mainHubContext)
        {
            if (!HttpContext.Request.Headers.TryGetValue(Models.Constants.SIGNAL_R_SECRET_KEY_HEADER_NAME, out var key) || !key.All(v => v == Models.Constants.SIGNAL_R_SECRET_KEY_HEADER_VALUE))
            {
                logger.LogWarning("Unauthorized in SendProcessedPayrollNotification");
                return Unauthorized();
            }

            await payrollApprovalService.NotifyClientsOnProcessedPayrollAsync(payrollQueueUpdate, mainHubContext);
            return NoContent();
        }

        private async Task<string> GetUserName()
        {
            string userName = "Anonymous";
            try
            {
                var authResult = await HttpContext.AuthenticateAsync();
                if (authResult.Succeeded)
                {
                    userName = $"UserId: {authResult.Principal.GetUserId()} {authResult.Principal.GetFirstName()} {authResult.Principal.GetLastName()}";
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error identifying user");
            }

            return userName;
        }
    }
}
