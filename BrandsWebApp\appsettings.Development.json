{
  "JwtConfiguration": {
    "Issuer": "BrandsWeb",
    "Audience": "BrandsWeb",
    "TokenLifetime": 240,
    "SigningKey": "cHupH&vathiGoP-lt&to3ev*7I+iQ2@ru"
  },
  "AllowedHosts": "*",
  "FrontendURL": "https://paydeck.brandspaycheck.com/",
  "ApplicationInsights": {
    "InstrumentationKey": "********-5f95-4262-a376-6438b2314947"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "System": "Information",
      "Microsoft": "Information"
    }
  },
  "ReportsQueueName": "Dev ReportsQueue",
  "ReportsExchangeName": "Dev ReportsQueue",
  "DownloadReportsViaUrl": "false",
  "HashSalt": "%?D<<dJ,a\"hH`9Nvt.NRd8;=r@'x$",
  "UserIdentityTokenExpires": 120,
  "Docusign": {
    "UseDemoBasePath": true,
    "TokenExpiresInHours": 1,
    "OAuthBasePath": "account-d.docusign.com",
    "IntegrationKey": "bc82273d-486d-44d9-b229-3e0ca5c06bc9",
    "UserId": "99f68d06-c216-41f5-ba90-773d6b634482",
    "TemplateId": "6154db14-6161-4440-92a5-f42b6ba3095f",
    "AccountId": "55302f32-3e3f-47ee-ae44-6e245c9e741a",
    "RedirectUrl": "https://localhost:3000",
    "ApiFetchTimeLimitMinutes": 15,
    "HookHMACKey": "8+vLoSrsbHFt7EzyaSM/+4oj+qJn1W7ZO8vzSIDG2e0=",
    "RSAPrivateKey": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "QBO": {
    "RedirectUrl": "http://localhost:3000/qbo/connect",
    "AppEnvironment": "sandbox",
    "ApiUrl": "https://sandbox-quickbooks.api.intuit.com",
    "DiscoveryUrl": "https://developer.api.intuit.com/.well-known/openid_configuration"
  },
  "Calendly": {
    //"BearerToken": "eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2F1dGguY2FsZW5kbHkuY29tIiwiaWF0IjoxNjUzNjYzNzQ2LCJqdGkiOiI5YTE4ODg5My00NTc2LTQ3YzItODNiNy03MWUxODQ1NWE2NjYiLCJ1c2VyX3V1aWQiOiJIR0hHVUFLV0Q2N1lGRklFIn0.5quEVZQYYl3OJHA1mrh9VV1OI8eCCvZfMH2ydm7eVFk",
    "BearerToken": "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "EventName": "Discovery Call",
    "StartTimeIntervalInDays": 30,
    "DiscoveryCall": "https://calendly.com/brandssetup/discovery",
    "OnboardingHelp": "https://calendly.com/brandssetup/paydeckonboarding",
    "OrganizationID": "FFBEWDMWLJVAQGP5",
    "OnboardingQuestion": "Calendar ID"
  },
  "CorsOptions": {
    "AllowOrigins": [
      "http://localhost:3000",
      "https://localhost:3000",
      "https://test.brandspaycheck.com:3000",
      "https://brandswebapp-devui.azurewebsites.net",
      "http://localhost:3001",
      "https://pd-devui1.brandspaycheck.com:4433",
      "http://pd-devui1.brandspaycheck.com:8080",
      "https://pd-devui2.brandspaycheck.com:4433",
      "http://pd-devui2.brandspaycheck.com:8080",
      "https://pd-devui3.brandspaycheck.com:4433",
      "http://pd-devui3.brandspaycheck.com:8080",
      "https://pd-devui4.brandspaycheck.com:4433",
      "http://pd-devui4.brandspaycheck.com:8080",
      "https://localhost:5001",
      "http://************:3000",
      "http://************:3000",
      "https://5a38-100-2-196-34.ngrok-free.app"
    ]
  },
  "login": {
    "paydeckUnavailableMessage": ""
  },
  "SmsOptions": {
    "UseTwilio": false,
    "SwitchSmsServiceAfterAttemptsCount": 2,
    "MinutesBetweenConsecutiveAttempts": 3,
    "From": "+***********",
    "AccountSid": "**********************************",
    "AuthToken": "de15c8152b80c59d2cd3a06f46f9fb66"
  },
  "SwipeClock": {
    "UseHttpClient": false
  },
  "Plaid": {
    "ClientName": "BrandsPayDeck",
    "Language": "en",
    "Products": "auth,transactions",
    "CountryCodes": "US,CA"
  },
  "ESS": {
    "PaystubFilePath": "\\\\************\\EssStubs\\{conum}\\{conum}-{empnum}-{paynum}-{checkcounter}.pdf",
    "PdfFormsBasePath": "C:\\EmployeeDeckPdfForms",
    "BrandsAdminOptions": {
      "ApiKeys": [
        "d15b21e6-e109-4287-bcbd-6032b2578fe2"
      ],
      "IpAddresses": []
    }
  },
  "PowerImportsUrl": "http://localhost:8080/api/file/powerImports",
  "QbReportsUrl": "http://localhost:8080/api/file/qbReports",
  "EmployeeDeckUrl": "https://localhost:54519",
  "2FARememberPeriodInDays": 21,
  "MaxRequestDurationInSeconds": 15,
  "CheckLockTimeoutInSeconds": 30,
  "EmployeeAuthFormFileLocation": "C:\\Reports\\",
  "RabbitMQ_ConnectionString": "host=*************:5672;username=WebUser;password=TSvkJ5HC53ONFHmw;prefetchcount=1",
  "rpcQueueNamePostfix": "Vs_Debug",
  "ExecuPayApiConfig": {
    "ConnectionAuthorization": "ExecupayAPIOAuth:+fP7SrarZaXMVttZKhLV1a.6HR57#kHN",
    "ConnectionPass": "3n!Bw_K2Yz9?s$F68Hc%p4{ME+f5=7Sa",
    "ConnectionUser": "moti.e"
  },
  "Sentry": {
    "Dsn": "https://<EMAIL>/4509481566273536",
    "SendDefaultPii": true,
    "MaxRequestBodySize": "Always",
    "MinimumBreadcrumbLevel": "Debug",
    "MinimumEventLevel": "Warning",
    "AttachStackTrace": true,
    "DiagnosticLevel": "Error",
    "TracesSampleRate": 1.0
  }
}
